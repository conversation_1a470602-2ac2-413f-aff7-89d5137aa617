package top.cywin.onetv.movie.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountTree
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Tv
import androidx.compose.material.icons.filled.Tune
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.SwapHoriz
import androidx.compose.material.icons.filled.Movie
import androidx.compose.material.icons.filled.CloudDownload
import androidx.compose.material.icons.filled.HighQuality
import androidx.compose.material.icons.filled.Speed
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Language
import androidx.compose.material.icons.filled.Router
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import kotlinx.coroutines.delay
import android.util.Log
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import top.cywin.onetv.movie.viewmodel.MovieViewModel
import top.cywin.onetv.movie.viewmodel.MovieUiState
import top.cywin.onetv.movie.event.NavigationEvent
import top.cywin.onetv.movie.event.NavigateToSearchResultsEvent
import top.cywin.onetv.movie.bean.Vod
import top.cywin.onetv.movie.bean.Site
import top.cywin.onetv.movie.ui.model.VodConfigUrl
import top.cywin.onetv.movie.ui.model.HomeCategorySection
import top.cywin.onetv.movie.ui.model.MovieItem
import top.cywin.onetv.movie.ui.model.CategoryInfo
import top.cywin.onetv.movie.bean.Class
import top.cywin.onetv.movie.MovieApp
import top.cywin.onetv.movie.navigation.MovieRoutes
import top.cywin.onetv.movie.ui.components.MovieCard
import top.cywin.onetv.movie.ui.components.QuickCategoryGrid
import top.cywin.onetv.movie.ui.components.RouteSelector

/**
 * OneTV Movie首页 - 按照FongMi_TV整合指南重构
 */
@Composable
fun MovieHomeScreen(
    navController: NavController,
    viewModel: MovieViewModel = viewModel {
        Log.d("ONETV_MOVIE_HOME", "🎬 [第4阶段] 开始创建MovieViewModel实例")
        MovieViewModel()
    }
) {
    Log.d("ONETV_MOVIE_HOME", "🎬 [第4阶段] MovieHomeScreen组件开始初始化")
    Log.d("ONETV_MOVIE_HOME", "📍 位置: MovieHomeScreen.kt:58")
    Log.d("ONETV_MOVIE_HOME", "⏰ 时间戳: ${System.currentTimeMillis()}")

    // ✅ 强制触发ViewModel初始化 - 通过访问ViewModel实例
    Log.d("ONETV_MOVIE_HOME", "🔄 [第4阶段] 强制触发ViewModel初始化")
    val viewModelInstance = viewModel // 强制创建ViewModel实例
    Log.d("ONETV_MOVIE_HOME", "🔄 [第4阶段] ViewModel实例已创建: ${viewModelInstance.javaClass.simpleName}")

    // ✅ 收集UI状态 - 使用多种方式确保状态同步
    Log.d("ONETV_MOVIE_HOME", "🔄 [第4阶段] 开始收集UI状态")
    val uiState by viewModelInstance.uiState.collectAsStateWithLifecycle()
    val navigationEvent by viewModelInstance.navigationEvent.collectAsStateWithLifecycle()

    // 🎯 调试：监控navigationEvent状态变化
    Log.d("ONETV_NAVIGATION", "🎯 [电影ID跟踪] 当前navigationEvent状态: ${navigationEvent?.javaClass?.simpleName ?: "null"}")

    // ✅ 监听立即导航事件
    DisposableEffect(Unit) {
        val eventListener = object {
            @Subscribe(threadMode = ThreadMode.MAIN)
            fun onNavigateToSearchResults(event: NavigateToSearchResultsEvent) {
                Log.d("ONETV_NAVIGATION", "🚀 [修复逻辑] 立即导航到搜索结果列表: ${event.keyword}")
                try {
                    navController.navigate(MovieRoutes.searchResultList(event.keyword))
                    Log.d("ONETV_NAVIGATION", "✅ [修复逻辑] 立即导航成功")
                } catch (e: Exception) {
                    Log.e("ONETV_NAVIGATION", "❌ [修复逻辑] 立即导航失败", e)
                }
            }
        }

        // 注册EventBus监听
        if (!EventBus.getDefault().isRegistered(eventListener)) {
            EventBus.getDefault().register(eventListener)
        }

        onDispose {
            if (EventBus.getDefault().isRegistered(eventListener)) {
                EventBus.getDefault().unregister(eventListener)
            }
        }
    }

    // 🔥 原版FongMi_TV直接导航支持：设置NavController
    LaunchedEffect(Unit) {
        Log.d("ONETV_NAVIGATION", "🔥 [原版直接导航] 设置NavController到ViewModel")
        viewModelInstance.setNavController(navController)
        Log.d("ONETV_NAVIGATION", "✅ [原版直接导航] NavController设置完成")
    }

    // ✅ 监听导航事件 - 保留原有机制作为备用
    LaunchedEffect(navigationEvent) {
        Log.d("ONETV_NAVIGATION", "🎯 [电影ID跟踪] LaunchedEffect触发检查，navigationEvent: ${navigationEvent?.javaClass?.simpleName ?: "null"}")

        navigationEvent?.let { event ->
            Log.d("ONETV_NAVIGATION", "🎯 [电影ID跟踪] LaunchedEffect触发，收到导航事件: ${event.javaClass.simpleName}")
            when (event) {
                is NavigationEvent.NavigateToDetail -> {
                    Log.d("ONETV_NAVIGATION", "🚀 [FongMi_TV兼容] 导航到详情页: ${event.movieName}")
                    Log.d("ONETV_NAVIGATION", "🎯 [电影ID跟踪] 导航参数: vodId=${event.vodId}, siteKey=${event.siteKey}")
                    try {
                        val route = MovieRoutes.detail(event.vodId, event.siteKey)
                        Log.d("ONETV_NAVIGATION", "🔗 [电影ID跟踪] 导航路由: $route")
                        navController.navigate(route)
                        Log.d("ONETV_NAVIGATION", "✅ [FongMi_TV兼容] 导航成功")
                        Log.d("ONETV_NAVIGATION", "🎯 [电影ID跟踪] 成功导航到详情页面")

                        // 🔥 关键修复：清理导航事件，避免重复触发
                        viewModelInstance.clearNavigationEvent()
                    } catch (e: Exception) {
                        Log.e("ONETV_NAVIGATION", "❌ [FongMi_TV兼容] 导航失败", e)
                        Log.e("ONETV_NAVIGATION", "🎯 [电影ID跟踪] 导航失败: ${e.message}")
                    }
                }
                is NavigationEvent.NavigateToCategory -> {
                    Log.d("ONETV_NAVIGATION", "🚀 导航到分类页: ${event.typeName}")
                    navController.navigate(MovieRoutes.category(event.typeId))
                    viewModelInstance.clearNavigationEvent()
                }
                is NavigationEvent.NavigateToSearchResultList -> {
                    Log.d("ONETV_NAVIGATION", "🚀 [修改逻辑] 导航到搜索结果列表页: ${event.keyword}")
                    Log.d("ONETV_NAVIGATION", "📋 [修改逻辑] 搜索结果数: ${event.results.size}")
                    try {
                        navController.navigate(MovieRoutes.searchResultList(event.keyword))
                        Log.d("ONETV_NAVIGATION", "✅ [修改逻辑] 导航到搜索结果列表成功")
                        viewModelInstance.clearNavigationEvent()
                    } catch (e: Exception) {
                        Log.e("ONETV_NAVIGATION", "❌ [修改逻辑] 导航失败", e)
                    }
                }
                else -> {
                    Log.d("ONETV_NAVIGATION", "🔄 其他导航事件: $event")
                }
            }
        }
    }

    // ✅ 添加强制重组触发器
    var recompositionTrigger by remember { mutableStateOf(0) }

    // ✅ 监听ViewModel状态变化，强制重组 - 修复状态同步问题
    LaunchedEffect(viewModelInstance.uiState) {
        viewModelInstance.uiState.collect { state ->
            Log.d("ONETV_MOVIE_HOME", "🔄 [第4阶段] StateFlow状态变化: showWelcomeScreen=${state.showWelcomeScreen}")
            // ✅ 强制重组，确保UI及时响应状态变化
            recompositionTrigger = System.currentTimeMillis().toInt()
        }
    }

    Log.d("ONETV_MOVIE_HOME", "🔄 [第4阶段] UI状态收集完成: ${uiState.javaClass.simpleName}")
    Log.d("ONETV_MOVIE_HOME", "🔄 [第4阶段] 当前UI状态 - isLoading: ${uiState.isLoading}, error: ${uiState.error}")

    // ✅ 监听UI状态变化，添加调试日志和异常恢复机制
    LaunchedEffect(uiState.showWelcomeScreen, uiState.isLoading, uiState.error) {
        Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] UI状态变化监听触发")
        Log.d("ONETV_MOVIE_HOME", "📊 showWelcomeScreen: ${uiState.showWelcomeScreen}")
        Log.d("ONETV_MOVIE_HOME", "📊 isLoading: ${uiState.isLoading}")
        Log.d("ONETV_MOVIE_HOME", "📊 error: ${uiState.error}")
        Log.d("ONETV_MOVIE_HOME", "⏰ 时间戳: ${System.currentTimeMillis()}")

        // ✅ 如果状态变为欢迎界面，强制重组
        if (uiState.showWelcomeScreen) {
            Log.d("ONETV_MOVIE_HOME", "🎉 [第8阶段] 检测到欢迎界面状态，准备重组")
        }

        // ✅ 添加异常恢复机制 - 如果长时间处于加载状态且没有内容，可能是状态更新失败
        if (uiState.isLoading && !uiState.showWelcomeScreen && uiState.error == null) {
            kotlinx.coroutines.delay(5000) // 等待5秒
            // 检查是否仍然在加载且没有内容
            val currentState = viewModelInstance.uiState.value
            if (currentState.isLoading && currentState.recommendMovies.isEmpty() &&
                currentState.categories.isEmpty() && currentState.homeCategories.isEmpty()) {
                Log.w("ONETV_MOVIE_HOME", "⚠️ [第8阶段] 检测到长时间加载无响应，可能是状态更新失败")
                Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 尝试刷新状态")
                viewModelInstance.refresh()
            }
        }
    }

    // ✅ 检查是否有初始化错误
    if (uiState.error != null) {
        Log.e("ONETV_MOVIE_HOME", "❌ [第4阶段] MovieHomeScreen初始化错误: ${uiState.error}")
    } else {
        Log.d("ONETV_MOVIE_HOME", "✅ [第4阶段] MovieHomeScreen初始化成功")
    }



    // ✅ UI准备好后手动加载首页数据
    LaunchedEffect(viewModelInstance) {
        Log.d("ONETV_MOVIE_HOME", "🚀 [第5阶段] UI准备完成，开始加载首页数据")
        viewModelInstance.loadHomeData()
    }

    // ✅ UI内容渲染 - 强制状态同步，修复欢迎界面显示问题
    val currentState = viewModelInstance.uiState.value // 直接获取最新状态

    // ✅ 修复延迟重组机制，确保状态更新后能够正确重组
    LaunchedEffect(uiState.showWelcomeScreen, currentState.showWelcomeScreen) {
        val shouldShowWelcome = uiState.showWelcomeScreen || currentState.showWelcomeScreen
        if (shouldShowWelcome) {
            Log.d("ONETV_MOVIE_HOME", "🎉 [第8阶段] 检测到欢迎界面状态变化，立即强制重组")
            Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] uiState.showWelcomeScreen: ${uiState.showWelcomeScreen}")
            Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] currentState.showWelcomeScreen: ${currentState.showWelcomeScreen}")
            // ✅ 立即重组，不延迟
            recompositionTrigger = System.currentTimeMillis().toInt()
        }
    }
    Log.d("ONETV_MOVIE_HOME", "🎬 [第8阶段] 开始UI内容渲染")
    Log.d("ONETV_MOVIE_HOME", "📍 位置: MovieHomeScreen.kt:154")
    Log.d("ONETV_MOVIE_HOME", "⏰ 时间戳: ${System.currentTimeMillis()}")
    Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 重组触发器: $recompositionTrigger")
    Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] UI状态检查(collectAsStateWithLifecycle) - showWelcomeScreen: ${uiState.showWelcomeScreen}, isLoading: ${uiState.isLoading}, error: ${uiState.error}")
    Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 当前状态检查(直接获取) - showWelcomeScreen: ${currentState.showWelcomeScreen}, isLoading: ${currentState.isLoading}, error: ${currentState.error}")

    // ✅ 修复状态选择逻辑 - 优先使用最新状态，特别是欢迎界面状态
    val renderState = when {
        currentState.showWelcomeScreen || uiState.showWelcomeScreen -> {
            // 如果任一状态显示欢迎界面，优先使用显示欢迎界面的状态
            if (currentState.showWelcomeScreen) currentState else uiState
        }
        else -> uiState // 其他情况使用收集的状态
    }
    Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 最终渲染状态 - showWelcomeScreen: ${renderState.showWelcomeScreen}, isLoading: ${renderState.isLoading}, error: ${renderState.error}")

    // ✅ 修复UI渲染逻辑 - 确保欢迎界面优先显示，添加配置检查逻辑
    when {
        renderState.showWelcomeScreen -> {
            Log.d("ONETV_MOVIE_HOME", "🎉 [第8阶段] 渲染欢迎界面 (使用最终渲染状态)")
            Log.d("ONETV_MOVIE_HOME", "🎨 [第8阶段] 开始渲染WelcomeScreen组件")
            WelcomeScreen(
                onConfigSetup = {
                    // ✅ 导航到配置页面
                    Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 用户点击配置按钮")
                    navController.navigate(MovieRoutes.CONFIG)
                }
            )
        }
        renderState.isLoading -> {
            Log.d("ONETV_MOVIE_HOME", "⏳ [第8阶段] 渲染加载界面 (使用最终渲染状态)")
            LoadingScreen(message = renderState.loadingMessage.ifEmpty { "正在加载配置..." })
        }
        renderState.error != null -> {
            Log.d("ONETV_MOVIE_HOME", "❌ [第8阶段] 渲染错误界面: ${renderState.error} (使用最终渲染状态)")
            val errorMessage = renderState.error ?: "未知错误"
            ErrorScreen(
                error = errorMessage,
                onRetry = { viewModelInstance.refresh() },
                onBack = { /* 首页不需要返回 */ }
            )
        }
        // ✅ 只有在明确要求显示欢迎界面时才显示，不要基于数据为空来判断
        renderState.showWelcomeScreen -> {
            Log.d("ONETV_MOVIE_HOME", "🎉 [第8阶段] 显示欢迎界面 (ViewModel状态控制)")

            // ✅ 欢迎界面由ViewModel状态控制，不需要额外设置

            WelcomeScreen(
                onConfigSetup = {
                    Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 用户点击配置按钮")
                    navController.navigate(MovieRoutes.CONFIG)
                }
            )
        }
        renderState.isStoreHouseIndex && renderState.selectedRoute == null -> {
            Log.d("ONETV_MOVIE_HOME", "🏪 [第8阶段] 渲染仓库选择界面 (使用最终渲染状态)")
            RouteSelectionScreen(
                routes = renderState.availableRoutes,
                onRouteSelect = { route ->
                    viewModelInstance.selectRoute(route)
                }
            )
        }
        else -> {
            Log.d("ONETV_MOVIE_HOME", "🏠 [第8阶段] 渲染正常首页内容 (使用最终渲染状态)")
            HomeContent(
                uiState = renderState,
                onRefresh = { viewModelInstance.refresh() },
                onMovieClick = { movie ->
                    Log.d("ONETV_MOVIE_CLICK", "🎬 [FongMi_TV兼容] 用户点击推荐影片: ${movie.vodName}")
                    Log.d("ONETV_MOVIE_CLICK", "📊 [FongMi_TV兼容] 影片信息 - vodId: ${movie.vodId}, siteKey: ${movie.siteKey}")

                    // 恢复原版逻辑：转换MovieItem为Vod类型，走完整的10步转换链路
                    val vod = Vod().apply {
                        setVodId(movie.vodId)
                        setVodName(movie.vodName)
                        setVodPic(movie.vodPic)
                        setSite(Site().apply { setKey(movie.siteKey) })
                    }
                    viewModel.onMovieClick(vod)
                },
                onCategoryClick = { category ->
                    Log.d("ONETV_CATEGORY_CLICK", "📂 [修复逻辑] 用户点击分类: ${category.typeName}")
                    Log.d("ONETV_CATEGORY_CLICK", "📊 [修复逻辑] 分类信息 - typeId: ${category.typeId}")
                    Log.d("ONETV_CATEGORY_CLICK", "🏠 [修复逻辑] 在主页显示分类内容，不跳转页面")

                    // 🔥 修复逻辑：直接在主页显示分类内容，不跳转页面
                    viewModel.loadCategoryContent(category.typeId, category.typeName)
                },
                onSearchClick = {
                    Log.d("ONETV_SEARCH_CLICK", "🔍 用户点击搜索按钮")
                    Log.d("ONETV_SEARCH_CLICK", "🚀 开始导航到搜索页面")
                    try {
                        navController.navigate(MovieRoutes.SEARCH)
                        Log.d("ONETV_SEARCH_CLICK", "✅ 导航成功")
                    } catch (e: Exception) {
                        Log.e("ONETV_SEARCH_CLICK", "❌ 导航失败", e)
                    }
                },
                onSettingsClick = {
                    Log.d("ONETV_SETTINGS_CLICK", "⚙️ 用户点击设置按钮")
                    Log.d("ONETV_SETTINGS_CLICK", "🚀 开始导航到设置页面")
                    try {
                        navController.navigate(MovieRoutes.SETTINGS)
                        Log.d("ONETV_SETTINGS_CLICK", "✅ 导航成功")
                    } catch (e: Exception) {
                        Log.e("ONETV_SETTINGS_CLICK", "❌ 导航失败", e)
                    }
                },
                onRouteSelect = { route ->
                    viewModelInstance.selectRoute(route)
                },
                onShowRouteSelector = {
                    viewModelInstance.showRouteSelector()
                },
                onHideRouteSelector = {
                    viewModelInstance.hideRouteSelector()
                },
                onBackToLive = {
                    Log.d("ONETV_BACK_TO_LIVE", "📺 用户点击返回直播")
                    try {
                        // TODO: 实现返回直播功能
                        // navController.navigate("live_tv_route")
                        Log.d("ONETV_BACK_TO_LIVE", "✅ 返回直播功能待实现")
                    } catch (e: Exception) {
                        Log.e("ONETV_BACK_TO_LIVE", "❌ 返回直播失败", e)
                    }
                },
                onShowHistory = {
                    Log.d("ONETV_HISTORY", "📚 用户点击观看历史")
                    try {
                        // TODO: 实现观看历史功能
                        viewModelInstance.loadWatchHistory()
                        Log.d("ONETV_HISTORY", "✅ 观看历史功能待实现")
                    } catch (e: Exception) {
                        Log.e("ONETV_HISTORY", "❌ 加载观看历史失败", e)
                    }
                },
                onShowFavorites = {
                    Log.d("ONETV_FAVORITES", "❤️ 用户点击收藏")
                    try {
                        // TODO: 实现收藏功能
                        viewModelInstance.loadFavorites()
                        Log.d("ONETV_FAVORITES", "✅ 收藏功能待实现")
                    } catch (e: Exception) {
                        Log.e("ONETV_FAVORITES", "❌ 加载收藏失败", e)
                    }
                },
                onShowSiteSelector = {
                    Log.d("ONETV_SITE_SELECTOR", "🌐 用户点击站点选择")
                    try {
                        // TODO: 实现站点选择功能
                        viewModelInstance.loadSiteList()
                        Log.d("ONETV_SITE_SELECTOR", "✅ 站点选择功能待实现")
                    } catch (e: Exception) {
                        Log.e("ONETV_SITE_SELECTOR", "❌ 加载站点列表失败", e)
                    }
                },
                onClearCategoryContent = {
                    // 🔥 修复逻辑：清除分类内容，返回主页
                    viewModelInstance.clearCategoryContent()
                },
                viewModel = viewModel  // 🔥 传递ViewModel引用
            )
        }
    }
}

@Composable
private fun HomeContent(
    uiState: MovieUiState,
    onRefresh: () -> Unit,
    onMovieClick: (MovieItem) -> Unit,
    onCategoryClick: (CategoryInfo) -> Unit,
    onSearchClick: () -> Unit,
    onSettingsClick: () -> Unit,
    onRouteSelect: (VodConfigUrl) -> Unit,
    onShowRouteSelector: () -> Unit,
    onHideRouteSelector: () -> Unit,
    onBackToLive: () -> Unit,
    onShowHistory: () -> Unit,
    onShowFavorites: () -> Unit,
    onShowSiteSelector: () -> Unit,
    onClearCategoryContent: () -> Unit,  // 🔥 新增：清除分类内容的回调
    viewModel: MovieViewModel  // 🔥 新增：ViewModel引用，用于分类电影点击
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部工具栏
        TopAppBar(
            title = {
                Text(
                    text = if (uiState.isStoreHouseIndex) uiState.storeHouseName else "OneTV 影视",
                    style = MaterialTheme.typography.headlineSmall
                )
            },
            actions = {
                // 返回直播按钮
                TextButton(onClick = onBackToLive) {
                    Icon(
                        imageVector = Icons.Default.Tv,
                        contentDescription = "返回直播",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "直播",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 观看历史按钮
                TextButton(onClick = onShowHistory) {
                    Icon(
                        imageVector = Icons.Default.History,
                        contentDescription = "观看历史",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "历史",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 收藏按钮
                TextButton(onClick = onShowFavorites) {
                    Icon(
                        imageVector = Icons.Default.Favorite,
                        contentDescription = "收藏",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "收藏",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 站点选择按钮
                TextButton(onClick = onShowSiteSelector) {
                    Icon(
                        imageVector = Icons.Default.Language,
                        contentDescription = "站点选择",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "站点",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 线路选择按钮
                TextButton(onClick = onShowRouteSelector) {
                    Icon(
                        imageVector = Icons.Default.Router,
                        contentDescription = "线路选择",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "线路",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 搜索按钮
                TextButton(onClick = onSearchClick) {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "搜索",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "搜索",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 设置按钮
                TextButton(onClick = onSettingsClick) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "设置",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "设置",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        )

        // 内容区域 - 显示正常的首页内容，修复空内容导致的卡机问题
        if (uiState.recommendMovies.isEmpty() && uiState.categories.isEmpty() && uiState.homeCategories.isEmpty()) {
            // ✅ 当没有内容时，显示空状态界面而不是空的LazyColumn
            EmptyContentScreen(
                onRefresh = onRefresh,
                message = "暂无内容，请检查配置或刷新重试"
            )
        } else {
            HomeContentScreen(
                uiState = uiState,
                onRefresh = onRefresh,
                onMovieClick = onMovieClick,
                onCategoryClick = onCategoryClick,
                onClearCategoryContent = onClearCategoryContent,
                viewModel = viewModel  // 🔥 传递ViewModel引用
            )
        }

        // 线路选择对话框
        if (uiState.showRouteSelector) {
            RouteSelector(
                routes = uiState.availableRoutes,
                selectedRoute = uiState.selectedRoute,
                onRouteSelected = { route ->
                    onRouteSelect(route)
                    onHideRouteSelector()
                }
            )
        }
    }
}

// ✅ 按照指南添加必要的辅助Composable函数

@Composable
private fun LoadingScreen(message: String) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text(text = message)
        }
    }
}

@Composable
private fun EmptyContentScreen(
    onRefresh: () -> Unit,
    message: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Movie,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )

            Text(
                text = message,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )

            Button(onClick = onRefresh) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("刷新")
            }
        }
    }
}

@Composable
private fun ErrorScreen(
    error: String,
    onRetry: () -> Unit,
    onBack: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(onClick = onRetry) {
                    Text("重试")
                }
                OutlinedButton(onClick = onBack) {
                    Text("返回")
                }
            }
        }
    }
}

@Composable
private fun RouteSelectionScreen(
    routes: List<VodConfigUrl>,
    onRouteSelect: (VodConfigUrl) -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxWidth(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            item {
                Text(
                    text = "选择线路",
                    style = MaterialTheme.typography.headlineMedium,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
            }

            items(routes) { route ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onRouteSelect(route) }
                ) {
                    Text(
                        text = route.name,
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
    }
}

@Composable
private fun HomeContentScreen(
    uiState: MovieUiState,
    onRefresh: () -> Unit,
    onMovieClick: (MovieItem) -> Unit,
    onCategoryClick: (CategoryInfo) -> Unit,
    onClearCategoryContent: () -> Unit,  // 🔥 新增：清除分类内容的回调
    viewModel: MovieViewModel  // 🔥 新增：ViewModel引用，用于分类电影点击
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 固定的分类导航栏（包含影视主页按钮）
        if (uiState.categories.isNotEmpty()) {
            CategoryGridWithHome(
                categories = uiState.categories,
                selectedCategoryName = uiState.currentCategoryName,  // 🔥 新增：传递当前选中的分类名称
                onCategoryClick = onCategoryClick,
                onHomeClick = {
                    Log.d("ONETV_MOVIE_HOME", "🏠 [修复逻辑] 用户点击影视主页 - 返回主页内容")
                    // 🔥 修复逻辑：清除分类内容，返回主页
                    onClearCategoryContent()
                }
            )
        }

        // 可滚动的内容区域
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {

        // 🔥 修复逻辑：根据是否有分类内容决定显示内容
        if (uiState.currentCategoryName != null && uiState.categoryMovies.isNotEmpty()) {
            // 显示分类内容
            // 🔥 用户要求：注释掉分类标签文本，为电影资源显示留出更多空间
            /*
            item {
                Text(
                    text = uiState.currentCategoryName,
                    style = MaterialTheme.typography.titleLarge,
                    modifier = Modifier.padding(bottom = 12.dp)
                )
            }
            */

            // 🔥 修复逻辑：分类电影网格，一行显示5张电影卡片
            items(uiState.categoryMovies.chunked(5)) { movieRow ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    movieRow.forEach { movie ->
                        // 🔧 调试日志：确认分类电影数据
                        android.util.Log.d("ONETV_CATEGORY_MOVIE", "🎬 渲染分类电影: ${movie.vodName}")
                        android.util.Log.d("ONETV_CATEGORY_MOVIE", "📊 电影数据: vodId=${movie.vodId}, siteKey=${movie.siteKey}")

                        MovieCard(
                            movie = movie,
                            onClick = {
                                android.util.Log.d("ONETV_CATEGORY_MOVIE_CLICK", "🎬 [调试] 分类电影被点击: ${movie.vodName}")
                                android.util.Log.d("ONETV_CATEGORY_MOVIE_CLICK", "📊 [调试] 电影信息 - vodId: ${movie.vodId}, siteKey: ${movie.siteKey}")

                                // 🔥 修复：直接使用与推荐电影相同的处理逻辑
                                val vod = Vod().apply {
                                    setVodId(movie.vodId)
                                    setVodName(movie.vodName)
                                    setVodPic(movie.vodPic)
                                    setSite(Site().apply { setKey(movie.siteKey) })
                                }
                                viewModel.onMovieClick(vod)
                            },
                            modifier = Modifier.weight(1f)
                        )
                    }
                    // 如果不足5个电影，添加空白占位
                    repeat(5 - movieRow.size) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
        } else {
            // 显示推荐内容和各分类内容（原有逻辑）

            // 推荐内容轮播
            if (uiState.recommendMovies.isNotEmpty()) {
                item {
                    RecommendCarousel(
                        movies = uiState.recommendMovies,
                        onMovieClick = onMovieClick
                    )
                }
            }

            // 🔥 修复逻辑：删除TV内容区域（各分类内容），只显示推荐内容
        }
        } // LazyColumn 结束
    } // Column 结束
}

@Composable
private fun RecommendCarousel(
    movies: List<MovieItem>,
    onMovieClick: (MovieItem) -> Unit
) {
    Column {
        // 🎬 注释掉"推荐内容"文本 - 按用户要求释放空间
        /*
        Text(
            text = "推荐内容",
            style = MaterialTheme.typography.titleLarge,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        */

        // 限制显示前10个推荐内容，使用网格布局
        LazyVerticalGrid(
            columns = GridCells.Fixed(5),
            modifier = Modifier.height(480.dp), // 限制高度，显示2行
            contentPadding = PaddingValues(0.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(movies.take(10)) { movie ->
                MovieCard(
                    movie = movie,
                    onClick = { onMovieClick(movie) }
                )
            }
        }
    } // LazyColumn 结束 // Column 结束
}

@Composable
private fun CategoryGridWithHome(
    categories: List<CategoryInfo>,
    selectedCategoryName: String?,  // 🔥 新增：当前选中的分类名称
    onCategoryClick: (CategoryInfo) -> Unit,
    onHomeClick: () -> Unit
) {
    // 包含影视主页按钮的分类导航栏
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 4.dp
    ) {
        LazyRow(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 影视主页按钮（固定在最左侧）
            item {
                val isHomeSelected = selectedCategoryName == null  // 🔥 判断是否选中主页
                Card(
                    modifier = Modifier.clickable { onHomeClick() },
                    colors = CardDefaults.cardColors(
                        containerColor = if (isHomeSelected) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.surface
                        }
                    ),
                    border = if (isHomeSelected) null else androidx.compose.foundation.BorderStroke(
                        1.dp,
                        MaterialTheme.colorScheme.outline
                    )
                ) {
                    Text(
                        text = "影视主页",
                        modifier = Modifier.padding(12.dp),
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (isHomeSelected) {
                            MaterialTheme.colorScheme.onPrimary
                        } else {
                            MaterialTheme.colorScheme.onSurface
                        },
                        fontWeight = if (isHomeSelected) FontWeight.Bold else FontWeight.Medium
                    )
                }
            }

            // 分类标签
            items(categories) { category ->
                val isSelected = selectedCategoryName == category.typeName  // 🔥 判断是否选中当前分类
                Card(
                    modifier = Modifier.clickable { onCategoryClick(category) },
                    colors = CardDefaults.cardColors(
                        containerColor = if (isSelected) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.surface
                        }
                    ),
                    border = if (isSelected) null else androidx.compose.foundation.BorderStroke(
                        1.dp,
                        MaterialTheme.colorScheme.outline
                    )
                ) {
                    Text(
                        text = category.typeName ?: "未知分类",
                        modifier = Modifier.padding(12.dp),
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimary
                        } else {
                            MaterialTheme.colorScheme.onSurface
                        },
                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium
                    )
                }
            }
        }
    }
}

@Composable
private fun CategoryGrid(
    categories: List<CategoryInfo>,
    onCategoryClick: (CategoryInfo) -> Unit
) {
    // 纯分类导航栏（无"分类"文本标题）
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 4.dp
    ) {
        LazyRow(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 分类标签
            items(categories) { category ->
                Card(
                    modifier = Modifier.clickable { onCategoryClick(category) }
                ) {
                    Text(
                        text = category.typeName ?: "未知分类",
                        modifier = Modifier.padding(12.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun CategorySection(
    section: HomeCategorySection,
    onMovieClick: (MovieItem) -> Unit,
    onMoreClick: () -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = section.categoryName,
                style = MaterialTheme.typography.titleLarge
            )

            TextButton(onClick = onMoreClick) {
                Text("更多")
            }
        }

        // 分类内容使用网格布局，显示前10个
        LazyVerticalGrid(
            columns = GridCells.Fixed(5),
            modifier = Modifier.height(480.dp), // 限制高度，显示2行
            contentPadding = PaddingValues(0.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(section.movies.take(10)) { movie ->
                MovieCard(
                    movie = movie,
                    onClick = { onMovieClick(movie) }
                )
            }
        }
    }
}

/**
 * 欢迎界面组件 - 漂亮的首次使用引导界面
 */
@Composable
private fun WelcomeScreen(
    onConfigSetup: () -> Unit,
    modifier: Modifier = Modifier
) {
    Log.d("ONETV_MOVIE_HOME", "🎨 [第8阶段] WelcomeScreen组件开始渲染")
    Log.d("ONETV_MOVIE_HOME", "📍 位置: MovieHomeScreen.kt:507")
    Log.d("ONETV_MOVIE_HOME", "⏰ 时间戳: ${System.currentTimeMillis()}")

    // ✅ 修复重组触发器 - 确保组件能正确渲染
    val recompositionTrigger by remember { mutableStateOf(System.currentTimeMillis()) }
    Log.d("ONETV_MOVIE_HOME", "🔄 [第8阶段] 重组触发器: $recompositionTrigger")
    Log.d("ONETV_MOVIE_HOME", "✅ [第8阶段] WelcomeScreen组件正在渲染中...")

    // ✅ 重新设计布局 - 合理分配空间，确保所有内容在一屏内美观显示
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                        MaterialTheme.colorScheme.surface,
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.05f)
                    )
                )
            )
            .padding(horizontal = 32.dp, vertical = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        // 顶部区域：标题和图标（占用较少空间）
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 应用图标（更小尺寸）
            Icon(
                imageVector = Icons.Default.Movie,
                contentDescription = null,
                modifier = Modifier.size(80.dp),
                tint = MaterialTheme.colorScheme.primary
            )

            // 标题
            Text(
                text = "欢迎使用 OneTV 影视",
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Bold
            )

            // 副标题
            Text(
                text = "您的专属影视娱乐平台",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }

        // 中间区域：功能介绍（紧凑布局）
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // 三个功能特性横向排列
            FeatureItemCompact(
                icon = Icons.Default.CloudDownload,
                title = "海量资源",
                        description = "支持多种影视接口，内容丰富多样"
            )

            FeatureItemCompact(
                icon = Icons.Default.HighQuality,
                title = "高清播放",
                        description = "支持多种清晰度，观影体验极佳"
            )

            FeatureItemCompact(
                icon = Icons.Default.Speed,
                title = "快速解析",
                        description = "智能解析技术，播放流畅无卡顿"
            )
        }

        // 底部区域：配置提示和按钮
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 配置提示
            Log.d("ONETV_MOVIE_HOME", "🎨 [第8阶段] 开始渲染配置提示")
            Text(
                text = "请先配置影视接口以开始使用",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                textAlign = TextAlign.Center
            )

            // 配置按钮
            Log.d("ONETV_MOVIE_HOME", "🎨 [第8阶段] 开始渲染配置按钮")
            Button(
                onClick = {
                    Log.d("ONETV_MOVIE_HOME", "🔘 [第8阶段] 用户点击配置按钮")
                    onConfigSetup()
                },
                modifier = Modifier
                    .fillMaxWidth(0.6f) // ✅ 按钮宽度适中
                    .height(56.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                ),
                elevation = ButtonDefaults.buttonElevation(defaultElevation = 4.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "开始您的影视之旅",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 紧凑版功能介绍组件 - 用于欢迎界面的横向布局
 */
@Composable
private fun FeatureItemCompact(
    icon: ImageVector,
    title: String,
    description: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.width(100.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(32.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.Center
        )

        Text(
            text = description,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 功能介绍项目组件（保留原版本用于其他地方）
 */
@Composable
private fun FeatureItem(
    icon: ImageVector,
    title: String,
    description: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium
            )

            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}