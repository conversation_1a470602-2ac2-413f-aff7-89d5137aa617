name: Android CI

on:
  push:
    branches: [ "main" ]
    paths-ignore:
      - '**.md'
      - '.github/workflows/**'
      - 'tv-stable.json'
  pull_request:
    branches: [ "main" ]
  workflow_dispatch:  # 添加手动触发选项

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      BOOTSTRAP_URL: ${{ secrets.BOOTSTRAP_URL }}
      BOOTSTRAP_KEY: ${{ secrets.BOOTSTRAP_KEY }}
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        cache: gradle

    - name: Set up Supabase configuration
      run: |
        echo "BOOTSTRAP_URL=${{ secrets.BOOTSTRAP_URL }}" > supabase_config.properties
        echo "BOOTSTRAP_KEY=${{ secrets.BOOTSTRAP_KEY }}" >> supabase_config.properties
        echo "配置文件已创建"
        
    - name: Setup Android Keystore
      run: |
        echo "${{ secrets.KEYSTORE_BASE64 }}" | base64 -d > keystore.jks
        echo "storeFile=keystore.jks" > key.properties
        echo "storePassword=${{ secrets.KEYSTORE_PASSWORD }}" >> key.properties
        echo "keyAlias=${{ secrets.KEY_ALIAS }}" >> key.properties
        echo "keyPassword=${{ secrets.KEY_PASSWORD }}" >> key.properties
        # 复制keystore.jks到mobile目录
        mkdir -p mobile
        cp keystore.jks mobile/keystore.jks
        # 复制keystore.jks到tv目录
        mkdir -p tv
        cp keystore.jks tv/keystore.jks

    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Build with Gradle
      run: ./gradlew build
      env:
        BOOTSTRAP_URL: ${{ secrets.BOOTSTRAP_URL }}
        BOOTSTRAP_KEY: ${{ secrets.BOOTSTRAP_KEY }}
        SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
        KEYSTORE: keystore.jks
        KEYSTORE_PASSWORD: ${{ secrets.KEYSTORE_PASSWORD }}
        KEY_ALIAS: ${{ secrets.KEY_ALIAS }}
        KEY_PASSWORD: ${{ secrets.KEY_PASSWORD }}
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: app-debug
        path: |
          **/build/outputs/apk/debug/*.apk
          **/build/outputs/bundle/debug/*.aab 