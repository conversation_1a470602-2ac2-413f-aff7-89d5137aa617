# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
# 保留Retrofit和Gson相关类
-keepattributes Signature
-keepattributes *Annotation*

# 保留数据模型类
-keep class top.cywin.onetv.core.data.repositories.user.OnlineUsersResponse {
    public <fields>;
    public <methods>;
}

# 保留Retrofit接口
-keep interface top.cywin.onetv.core.data.repositories.user.ApiService { *; }

# 修复R8混淆错误：保留XmlPullParser相关类
-keep class org.xmlpull.v1.** { *; }
-keep class android.content.res.XmlResourceParser { *; }
-dontwarn org.xmlpull.v1.**
-dontwarn android.content.res.XmlResourceParser

# 保留AAR文件中的关键类，避免混淆导致的问题
-keep class master.flame.danmaku.** { *; }
-keep class com.forcetech.** { *; }
-keep class com.gsoft.mitv.** { *; }
-keep class com.anymediacloud.iptv.standard.** { *; }

# 保留JavaScript引擎相关类
-keep class javax.script.** { *; }
-dontwarn javax.script.**

# 保留媒体播放相关类
-keep class androidx.media3.** { *; }
-dontwarn androidx.media3.**

# 避免混淆导致的反射问题
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# 修复R8混淆缺失类错误：保留CatVod相关类
-keep class com.github.catvod.** { *; }
-dontwarn com.github.catvod.**

# 保留Java Bean相关类（JavaScript引擎需要）
-keep class java.beans.** { *; }
-dontwarn java.beans.**

# 保留企业级注入相关类（UPnP库需要）
-keep class javax.enterprise.** { *; }
-keep class javax.inject.** { *; }
-dontwarn javax.enterprise.**
-dontwarn javax.inject.**

# 保留UPnP相关类（DLNA功能需要）
-keep class org.fourthline.cling.** { *; }
-dontwarn org.fourthline.cling.**

# 保留TVBus引擎相关类
-keep class com.tvbus.engine.** { *; }
-dontwarn com.tvbus.engine.**

# 保留MITV相关类
-keep class com.gsoft.mitv.** { *; }
-dontwarn com.gsoft.mitv.**

# 保留Mozilla JavaScript引擎相关类
-keep class org.mozilla.javascript.** { *; }
-dontwarn org.mozilla.javascript.**

# ========================================
# R8混淆完整解决方案：处理所有缺失的桌面应用类
# ========================================

# 1. Java AWT桌面组件（Android环境不支持）
-dontwarn java.awt.BorderLayout
-dontwarn java.awt.Component
-dontwarn java.awt.Container
-dontwarn java.awt.Dimension
-dontwarn java.awt.LayoutManager
-dontwarn java.awt.Rectangle
-dontwarn java.awt.Toolkit
-dontwarn java.awt.Window
-dontwarn java.awt.**

# 2. Java AWT数据传输和事件（剪贴板、鼠标等）
-dontwarn java.awt.datatransfer.Clipboard
-dontwarn java.awt.datatransfer.ClipboardOwner
-dontwarn java.awt.datatransfer.StringSelection
-dontwarn java.awt.datatransfer.Transferable
-dontwarn java.awt.datatransfer.**
-dontwarn java.awt.event.ActionEvent
-dontwarn java.awt.event.ActionListener
-dontwarn java.awt.event.ItemListener
-dontwarn java.awt.event.WindowEvent
-dontwarn java.awt.event.WindowListener
-dontwarn java.awt.event.**

# 3. Java Swing UI组件（Android使用自己的UI系统）
-dontwarn javax.swing.AbstractAction
-dontwarn javax.swing.AbstractButton
-dontwarn javax.swing.BorderFactory
-dontwarn javax.swing.Box
-dontwarn javax.swing.BoxLayout
-dontwarn javax.swing.Icon
-dontwarn javax.swing.ImageIcon
-dontwarn javax.swing.JButton
-dontwarn javax.swing.JCheckBox
-dontwarn javax.swing.JComboBox
-dontwarn javax.swing.JDialog
-dontwarn javax.swing.JFrame
-dontwarn javax.swing.JLabel
-dontwarn javax.swing.JPanel
-dontwarn javax.swing.JScrollPane
-dontwarn javax.swing.JTable
-dontwarn javax.swing.JToolBar
-dontwarn javax.swing.ListSelectionModel
-dontwarn javax.swing.SwingUtilities
-dontwarn javax.swing.**

# 4. Swing边框和表格组件
-dontwarn javax.swing.border.Border
-dontwarn javax.swing.border.TitledBorder
-dontwarn javax.swing.border.**
-dontwarn javax.swing.event.ListSelectionListener
-dontwarn javax.swing.event.**
-dontwarn javax.swing.table.AbstractTableModel
-dontwarn javax.swing.table.DefaultTableCellRenderer
-dontwarn javax.swing.table.JTableHeader
-dontwarn javax.swing.table.TableCellRenderer
-dontwarn javax.swing.table.TableColumn
-dontwarn javax.swing.table.TableColumnModel
-dontwarn javax.swing.table.TableModel
-dontwarn javax.swing.table.**

# 5. Seamless Swing桌面组件（UPnP库的桌面应用部分）
-dontwarn org.seamless.swing.**
-dontwarn org.seamless.swing.logging.**
-dontwarn org.seamless.swing.Application
-dontwarn org.seamless.swing.Controller
-dontwarn org.seamless.swing.AbstractController
-dontwarn org.seamless.swing.DefaultAction

# 6. 保留UPnP核心功能（网络通信部分）
-keep class org.seamless.util.** { *; }
-keep class org.seamless.http.** { *; }
-keep class org.seamless.xml.** { *; }
-keep class org.seamless.statemachine.** { *; }

# 7. 其他可能的桌面应用相关类
-dontwarn java.beans.**
-dontwarn javax.enterprise.**
-dontwarn javax.inject.**
-dontwarn java.desktop.**

# 8. 确保R8不会因为缺失的可选依赖而失败
-dontwarn org.apache.commons.logging.**
-dontwarn org.slf4j.**
-dontwarn ch.qos.logback.**

# 9. 保留反射相关属性（避免运行时错误）
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# 10. 针对UPnP库的特殊处理
-keep class org.fourthline.cling.** { *; }
-dontwarn org.fourthline.cling.**

# 11. 新发现的缺失类：Java管理和图像处理
-dontwarn java.lang.management.ManagementFactory
-dontwarn java.lang.management.ThreadMXBean
-dontwarn java.lang.management.**
-dontwarn javax.imageio.ImageIO
-dontwarn javax.imageio.**

# 12. Java邮件API（javax.mail）
-dontwarn javax.mail.Address
-dontwarn javax.mail.Authenticator
-dontwarn javax.mail.BodyPart
-dontwarn javax.mail.Message$RecipientType
-dontwarn javax.mail.Message
-dontwarn javax.mail.MessagingException
-dontwarn javax.mail.Multipart
-dontwarn javax.mail.Session
-dontwarn javax.mail.Transport
-dontwarn javax.mail.internet.InternetAddress
-dontwarn javax.mail.internet.MimeBodyPart
-dontwarn javax.mail.internet.MimeMessage
-dontwarn javax.mail.internet.MimeMultipart
-dontwarn javax.mail.**

# 13. DBUnit数据库测试框架
-dontwarn org.dbunit.database.DatabaseConfig
-dontwarn org.dbunit.database.DatabaseConnection
-dontwarn org.dbunit.database.IDatabaseConnection
-dontwarn org.dbunit.dataset.IDataSet
-dontwarn org.dbunit.dataset.ReplacementDataSet
-dontwarn org.dbunit.dataset.datatype.DataType
-dontwarn org.dbunit.dataset.datatype.DataTypeException
-dontwarn org.dbunit.dataset.datatype.DefaultDataTypeFactory
-dontwarn org.dbunit.dataset.xml.FlatXmlDataSet
-dontwarn org.dbunit.operation.DatabaseOperation
-dontwarn org.dbunit.**

# 14. Hibernate ORM框架
-dontwarn org.hibernate.SessionFactory
-dontwarn org.hibernate.cfg.Configuration
-dontwarn org.hibernate.**