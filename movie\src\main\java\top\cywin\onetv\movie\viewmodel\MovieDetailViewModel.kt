package top.cywin.onetv.movie.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import top.cywin.onetv.movie.MovieApp
import top.cywin.onetv.movie.bean.Vod
import top.cywin.onetv.movie.bean.Flag
import android.util.Log

// ✅ 添加EventBus支持
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import top.cywin.onetv.movie.event.*
import top.cywin.onetv.movie.event.MovieIdTransformEvent
import top.cywin.onetv.movie.ui.model.*
import top.cywin.onetv.movie.adapter.ViewModelAdapter

/**
 * 详情页UI状态数据类 - 完整版本
 */
data class DetailUiState(
    // 基础状态
    val isLoading: Boolean = false,
    val error: String? = null,
    val vodId: String = "",
    val siteKey: String = "",

    // 影片信息
    val movie: MovieItem? = null,

    // 播放相关
    val playFlags: List<PlayFlag> = emptyList(),
    val currentFlag: PlayFlag? = null,
    val episodes: List<Episode> = emptyList(),
    val currentEpisode: Episode? = null,
    val isParsingPlayUrl: Boolean = false,

    // 用户状态
    val isFavorite: Boolean = false,
    val watchHistory: WatchHistory? = null,

    // UI控制
    val showFlagSelector: Boolean = false,
    val showEpisodeSelector: Boolean = false,
    val showMoreInfo: Boolean = false,

    // 相关推荐
    val relatedMovies: List<MovieItem> = emptyList(),
    val isLoadingRelated: Boolean = false
)

/**
 * OneTV Movie详情页ViewModel - 完整版本
 * 处理影片详情、播放源、剧集等完整功能
 */
class MovieDetailViewModel : ViewModel() {

    companion object {
        private const val TAG = "ONETV_MOVIE_DETAIL_VM"
    }

    // ✅ 通过MovieApp访问适配器系统
    private val movieApp = MovieApp.getInstance()
    private val repositoryAdapter = movieApp.repositoryAdapter
    private val viewModelAdapter = movieApp.viewModelAdapter

    private val _uiState = MutableStateFlow(DetailUiState())
    val uiState: StateFlow<DetailUiState> = _uiState.asStateFlow()

    // 🔥 关键修复：标记当前ViewModel是否为活跃状态，防止EventBus事件风暴
    private var isActive = false
    private var currentVodId: String? = null
    private var currentSiteKey: String? = null

    init {
        Log.d(TAG, "🏗️ MovieDetailViewModel 初始化")

        // ✅ 注册EventBus监听FongMi_TV事件
        EventBus.getDefault().register(this)
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "🧹 MovieDetailViewModel 清理")

        // 🔥 关键修复：重置活跃状态
        isActive = false
        currentVodId = null
        currentSiteKey = null
        Log.d(TAG, "🔥 [EventBus修复] 重置ViewModel活跃状态")

        // ✅ 取消EventBus注册
        try {
            EventBus.getDefault().unregister(this)
        } catch (e: Exception) {
            Log.e(TAG, "EventBus取消注册失败", e)
        }
    }

    // ===== EventBus事件监听 =====

    /**
     * 监听电影ID转换事件 - 实现原版VideoActivity的ID转换机制
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMovieIdTransform(event: MovieIdTransformEvent) {
        Log.d(TAG, "📡 [FongMi_TV兼容] 收到ID转换事件: success=${event.isSuccess}")
        Log.d(TAG, "📊 [FongMi_TV兼容] 转换结果: ${event.movieName} -> vodId: ${event.realVodId}, siteKey: ${event.siteKey}")

        if (event.isSuccess && event.realVodId != null) {
            // ID转换成功，使用真实ID加载详情
            Log.d(TAG, "✅ [FongMi_TV兼容] ID转换成功，开始加载详情")
            loadMovieDetail(event.realVodId, event.siteKey)
        } else {
            // ID转换失败
            Log.e(TAG, "❌ [FongMi_TV兼容] ID转换失败: ${event.error}")
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = event.error ?: "ID转换失败"
            )
        }
    }

    /**
     * 监听内容详情事件
     * 🔥 关键修复：只有活跃的ViewModel才处理事件，防止EventBus事件风暴
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onContentDetail(event: ContentDetailEvent) {
        // 🔥 关键修复：只有活跃的ViewModel且匹配当前请求的才处理事件
        if (!isActive) {
            Log.d(TAG, "🚫 [EventBus修复] ViewModel非活跃状态，忽略ContentDetailEvent")
            return
        }

        // 检查事件是否匹配当前请求
        val eventVodId = event.vod?.vodId
        val eventSiteKey = event.vod?.siteKey
        if (eventVodId != currentVodId || eventSiteKey != currentSiteKey) {
            Log.d(TAG, "🚫 [EventBus修复] 事件不匹配当前请求，忽略: eventVodId=$eventVodId, currentVodId=$currentVodId")
            return
        }

        Log.d(TAG, "📡 [EventBus修复] 活跃ViewModel收到匹配的内容详情事件: success=${event.success}")

        try {
            if (event.vod != null && event.success) {
                // 🔥 处理成功后立即设置为非活跃，防止重复处理
                isActive = false
                handleContentDetailSuccess(event.vod)
            } else {
                Log.w(TAG, "⚠️ [详情事件修复] 详情获取失败: ${event.errorMessage}")
                isActive = false
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = event.errorMessage ?: "获取详情失败"
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "💥 详情处理失败", e)
            isActive = false
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "详情处理异常: ${e.message}"
            )
        }
    }

    /**
     * 监听收藏更新事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFavoriteUpdate(event: FavoriteUpdateEvent) {
        Log.d(TAG, "📡 收到收藏更新事件: vodId=${event.vodId}, favorite=${event.isFavorite}")

        if (event.isSuccess) {
            _uiState.value = _uiState.value.copy(
                isFavorite = event.isFavorite
            )
        }
    }

    /**
     * 监听历史更新事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onHistoryUpdate(event: HistoryUpdateEvent) {
        Log.d(TAG, "📡 收到历史更新事件: vodId=${event.vodId}")

        if (event.isSuccess) {
            val watchHistory = WatchHistory(
                vodId = event.vodId,
                vodName = _uiState.value.movie?.vodName ?: "",
                siteKey = _uiState.value.movie?.siteKey ?: "",
                episodeName = _uiState.value.currentEpisode?.name ?: "",
                position = event.position,
                duration = event.duration,
                watchTime = System.currentTimeMillis(),
                isCompleted = event.position >= event.duration * 0.9
            )

            _uiState.value = _uiState.value.copy(
                watchHistory = watchHistory
            )
        }
    }

    /**
     * 监听播放地址解析事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPlayUrlParse(event: PlayUrlParseEvent) {
        Log.d(TAG, "📡 [播放地址解析] 收到播放地址解析事件")
        Log.d(TAG, "🎬 [播放地址解析] 解析结果: url=${event.playUrl}")
        Log.d(TAG, "🎬 [播放地址解析] 播放线路: flag=${event.flag}")

        if (!event.playUrl.isNullOrEmpty()) {
            // 🔥 更新当前剧集的播放地址
            val currentState = _uiState.value
            if (currentState.currentEpisode != null) {
                val updatedEpisode = currentState.currentEpisode.copy(
                    playUrl = event.playUrl
                )

                _uiState.value = currentState.copy(
                    currentEpisode = updatedEpisode,
                    isParsingPlayUrl = false
                )

                Log.d(TAG, "✅ [播放地址解析] 播放地址更新成功: ${event.playUrl}")
            }
        } else {
            Log.w(TAG, "⚠️ [播放地址解析] 播放地址为空")
            _uiState.value = _uiState.value.copy(
                isParsingPlayUrl = false,
                error = "播放地址解析失败"
            )
        }
    }

    /**
     * 监听错误事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onError(event: ErrorEvent) {
        Log.e(TAG, "📡 收到错误事件: ${event.msg}")

        _uiState.value = _uiState.value.copy(
            isLoading = false,
            error = event.msg
        )
    }

    // ===== 公共方法 =====

    /**
     * 加载影片详情 - 标准方法
     * 🔥 关键修复：设置活跃状态，防止EventBus事件风暴
     */
    fun loadMovieDetail(vodId: String, siteKey: String = "") {
        viewModelScope.launch {
            try {
                Log.d(TAG, "🎬 [FongMi_TV兼容] 开始加载影片详情: vodId=$vodId, siteKey=$siteKey")
                Log.d(TAG, "🎯 [电影ID跟踪] MovieDetailViewModel.loadMovieDetail被调用")

                // 🔥 关键修复：设置当前ViewModel为活跃状态
                isActive = true
                currentVodId = vodId
                currentSiteKey = siteKey
                Log.d(TAG, "🔥 [EventBus修复] 设置ViewModel为活跃状态: vodId=$vodId, siteKey=$siteKey")

                // ✅ 数据有效性检查（不过滤，只是记录警告）
                if (vodId.isBlank()) {
                    Log.w(TAG, "⚠️ [FongMi_TV兼容] 电影ID为空，但仍尝试加载")
                }

                if (vodId == "no_data" || vodId.contains("无数据")) {
                    Log.w(TAG, "⚠️ [FongMi_TV兼容] 检测到可能的无效数据，但仍尝试加载: $vodId")
                }

                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    error = null,
                    vodId = vodId,
                    siteKey = siteKey
                )

                // ✅ 通过适配器获取影片详情（添加超时保护）
                withTimeout(30000) { // 30秒超时
                    Log.d(TAG, "🔄 [FongMi_TV兼容] 调用RepositoryAdapter.getContentDetail")
                    Log.d(TAG, "🎯 [电影ID跟踪] 调用getContentDetail: vodId=$vodId, siteKey=$siteKey")
                    repositoryAdapter.getContentDetail(vodId, siteKey)
                }

                // ✅ 同时检查收藏状态和观看历史
                checkFavoriteAndHistory(vodId, siteKey)

            } catch (e: TimeoutCancellationException) {
                Log.e(TAG, "⏰ 影片详情加载超时: vodId=$vodId")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载超时，请重试"
                )
            } catch (e: Exception) {
                Log.e(TAG, "💥 影片详情加载失败: vodId=$vodId", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "详情加载失败，但不影响应用运行"
                )
            }
        }
    }

    /**
     * 切换收藏状态
     */
    fun toggleFavorite() {
        val currentState = _uiState.value
        val movie = currentState.movie ?: return

        viewModelScope.launch {
            try {
                val newFavoriteState = !currentState.isFavorite

                // ✅ 乐观更新UI
                _uiState.value = _uiState.value.copy(isFavorite = newFavoriteState)

                // ✅ 通过适配器更新收藏状态
                if (newFavoriteState) {
                    // 需要转换为FongMi_TV的Vod对象
                    val fongmiVod = convertToFongMiVod(movie)
                    repositoryAdapter.addToFavorites(fongmiVod)
                } else {
                    repositoryAdapter.removeFromFavorites(movie.vodId, movie.siteKey)
                }

                Log.d(TAG, "✅ 收藏状态更新: ${if (newFavoriteState) "已收藏" else "已取消收藏"}")

            } catch (e: Exception) {
                Log.e(TAG, "💥 收藏操作失败", e)
                // 回滚UI状态
                _uiState.value = _uiState.value.copy(isFavorite = currentState.isFavorite)
            }
        }
    }

    /**
     * 选择播放线路
     */
    fun selectFlag(flag: PlayFlag) {
        Log.d(TAG, "🔄 选择播放线路: ${flag.flag}")

        val episodes = ViewModelAdapter.convertVodEpisodes(flag.urls)
        val defaultEpisode = episodes.firstOrNull()

        _uiState.value = _uiState.value.copy(
            currentFlag = flag,
            episodes = episodes,
            currentEpisode = defaultEpisode,
            showFlagSelector = false
        )
    }

    /**
     * 选择剧集
     */
    fun selectEpisode(episode: Episode) {
        Log.d(TAG, "🔄 选择剧集: ${episode.name}")

        _uiState.value = _uiState.value.copy(
            currentEpisode = episode,
            showEpisodeSelector = false
        )
    }

    /**
     * 显示线路选择器
     */
    fun showFlagSelector() {
        _uiState.value = _uiState.value.copy(showFlagSelector = true)
    }

    /**
     * 隐藏线路选择器
     */
    fun hideFlagSelector() {
        _uiState.value = _uiState.value.copy(showFlagSelector = false)
    }

    /**
     * 显示剧集选择器
     */
    fun showEpisodeSelector() {
        _uiState.value = _uiState.value.copy(showEpisodeSelector = true)
    }

    /**
     * 隐藏剧集选择器
     */
    fun hideEpisodeSelector() {
        _uiState.value = _uiState.value.copy(showEpisodeSelector = false)
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    // ===== 私有方法 =====

    /**
     * 处理内容详情成功
     * 🔥 关键修复：添加完整的异常处理，防止死循环
     */
    private fun handleContentDetailSuccess(vod: top.cywin.onetv.movie.bean.Vod) {
        Log.d(TAG, "✅ 处理内容详情成功: ${vod.vodName}")

        try {
            // ✅ 转换为UI模型
            val movieItem = ViewModelAdapter.convertVodToMovie(vod)
            if (movieItem == null) {
                Log.e(TAG, "❌ [详情处理修复] 数据转换失败: ${vod.vodName}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "数据转换失败"
                )
                return
            }

            // ✅ 解析播放源 - 🔥 关键修复：添加详细日志和异常捕获
            Log.d(TAG, "🎬 [详情处理修复] 开始解析播放线路: ${vod.vodName}")
            Log.d(TAG, "🎬 [详情处理修复] Vod对象信息: vodFlags数量=${vod.vodFlags?.size ?: 0}")

            // 🔥 添加详细的Vod数据日志
            vod.vodFlags?.forEachIndexed { index, flag ->
                Log.d(TAG, "🎬 [详情处理修复] Flag[$index]: name=${flag.flag}, urls长度=${flag.urls?.length ?: 0}")
                if (flag.urls != null && flag.urls.isNotEmpty()) {
                    Log.d(TAG, "🎬 [详情处理修复] Flag[$index] urls前100字符: ${flag.urls.take(100)}")
                }
            }

            val playFlags = try {
                ViewModelAdapter.convertVodFlags(vod)
            } catch (e: Exception) {
                Log.e(TAG, "❌ [详情处理修复] 播放线路转换失败: ${vod.vodName}", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "播放线路解析失败: ${e.message}"
                )
                return
            }

            Log.d(TAG, "🎬 [详情处理修复] 播放线路转换完成: 数量=${playFlags.size}")
            if (playFlags.isEmpty()) {
                Log.w(TAG, "⚠️ [详情处理修复] 没有找到播放源: ${vod.vodName}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "没有找到播放源"
                )
                return
            }

            // ✅ 选择默认播放源和剧集
            val defaultFlag = playFlags.firstOrNull()
            Log.d(TAG, "🎬 [详情处理修复] 默认播放线路: ${defaultFlag?.flag}, urls长度=${defaultFlag?.urls?.length ?: 0}")

            val episodes = if (defaultFlag != null) {
                Log.d(TAG, "🎬 [详情处理修复] 开始转换剧集，urls内容: ${defaultFlag.urls.take(200)}")
                val convertedEpisodes = ViewModelAdapter.convertVodEpisodes(defaultFlag.urls)
                Log.d(TAG, "🎬 [详情处理修复] 剧集转换完成: 数量=${convertedEpisodes.size}")
                convertedEpisodes.forEachIndexed { index, episode ->
                    Log.d(TAG, "🎬 [详情处理修复] Episode[$index]: name=${episode.name}, url=${episode.url.take(50)}")
                }
                convertedEpisodes
            } else {
                Log.w(TAG, "⚠️ [详情处理修复] 默认播放线路为null")
                emptyList()
            }

            if (episodes.isEmpty()) {
                Log.w(TAG, "⚠️ [详情处理修复] 没有找到可播放的剧集: ${vod.vodName}")
                Log.w(TAG, "⚠️ [详情处理修复] 播放线路数量: ${playFlags.size}")
                Log.w(TAG, "⚠️ [详情处理修复] 默认线路urls: ${defaultFlag?.urls}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "没有找到可播放的剧集"
                )
                return
            }

            val targetEpisode = episodes.firstOrNull()
            if (targetEpisode == null) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "剧集信息无效"
                )
                return
            }

            // ✅ 更新UI状态
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                movie = movieItem,
                playFlags = playFlags,
                currentFlag = defaultFlag,
                episodes = episodes,
                currentEpisode = targetEpisode,
                error = null
            )

            // 🔥 新增：自动解析第一个剧集的播放地址
            if (targetEpisode != null && defaultFlag != null) {
                Log.d(TAG, "🎬 [播放地址解析] 开始自动解析第一个剧集: ${targetEpisode.name}")
                Log.d(TAG, "🎬 [播放地址解析] 剧集URL: ${targetEpisode.url}")
                Log.d(TAG, "🎬 [播放地址解析] 播放线路: ${defaultFlag.flag}")
                Log.d(TAG, "🎬 [播放地址解析] 站点Key: ${movieItem.siteKey}")

                // 🔥 设置解析状态
                _uiState.value = _uiState.value.copy(isParsingPlayUrl = true)

                try {
                    // 🔥 关键：调用SiteViewModel.playerContent()解析播放地址
                    repositoryAdapter.parsePlayUrl(targetEpisode.url, movieItem.siteKey, defaultFlag.flag)
                    Log.d(TAG, "✅ [播放地址解析] 播放地址解析请求已发送")
                } catch (e: Exception) {
                    Log.e(TAG, "❌ [播放地址解析] 播放地址解析请求失败", e)
                    _uiState.value = _uiState.value.copy(
                        isParsingPlayUrl = false,
                        error = "播放地址解析失败: ${e.message}"
                    )
                }
            } else {
                Log.w(TAG, "⚠️ [播放地址解析] 无法解析播放地址: targetEpisode=${targetEpisode?.name}, defaultFlag=${defaultFlag?.flag}")
            }

            // 🔥 新增：自动解析第一个剧集的播放地址
            if (targetEpisode != null && defaultFlag != null) {
                Log.d(TAG, "🎬 [播放地址解析] 开始自动解析第一个剧集: ${targetEpisode.name}")
                Log.d(TAG, "🎬 [播放地址解析] 剧集URL: ${targetEpisode.url}")
                Log.d(TAG, "🎬 [播放地址解析] 播放线路: ${defaultFlag.flag}")

                try {
                    // 通过RepositoryAdapter解析播放地址
                    repositoryAdapter.parsePlayUrl(targetEpisode.url, movieItem.siteKey, defaultFlag.flag)
                    Log.d(TAG, "✅ [播放地址解析] 播放地址解析请求已发送")
                } catch (e: Exception) {
                    Log.e(TAG, "❌ [播放地址解析] 播放地址解析请求失败", e)
                }
            } else {
                Log.w(TAG, "⚠️ [播放地址解析] 无法解析播放地址: targetEpisode=${targetEpisode?.name}, defaultFlag=${defaultFlag?.flag}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "💥 详情处理失败", e)
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "详情处理失败: ${e.message}"
            )
        }
    }

    /**
     * 检查收藏状态和观看历史
     */
    private suspend fun checkFavoriteAndHistory(vodId: String, siteKey: String) {
        try {
            // ✅ 检查收藏状态
            val isFavorite = repositoryAdapter.isFavorite(vodId, siteKey)

            // ✅ 获取观看历史
            val history = repositoryAdapter.getWatchHistory(vodId, siteKey)
            val watchHistory = if (history != null) {
                WatchHistory(
                    vodId = history.vodId,
                    vodName = history.vodName,
                    siteKey = siteKey,
                    episodeName = history.vodRemarks ?: "",
                    position = history.position,
                    duration = history.duration,
                    watchTime = history.createTime,
                    isCompleted = history.position >= history.duration * 0.9
                )
            } else null

            // ✅ 更新UI状态
            _uiState.value = _uiState.value.copy(
                isFavorite = isFavorite,
                watchHistory = watchHistory
            )

        } catch (e: Exception) {
            Log.e(TAG, "💥 检查收藏和历史失败", e)
        }
    }

    /**
     * 转换为FongMi_TV的Vod对象
     */
    private fun convertToFongMiVod(movie: MovieItem): top.cywin.onetv.movie.bean.Vod {
        val vod = top.cywin.onetv.movie.bean.Vod()
        vod.setVodId(movie.vodId)
        vod.setVodName(movie.vodName)
        vod.setVodPic(movie.vodPic)
        // 注意：以下字段没有setter方法，需要通过反射或其他方式设置
        // 暂时跳过这些字段的设置，保持基本功能
        // vod.vodRemarks = movie.vodRemarks
        // vod.vodYear = movie.vodYear
        // vod.vodArea = movie.vodArea
        // vod.vodDirector = movie.vodDirector
        // vod.vodActor = movie.vodActor
        // vod.vodContent = movie.vodContent

        // 设置站点信息
        val site = repositoryAdapter.getCurrentSite()
        vod.setSite(site)

        return vod
    }

    /**
     * 检查收藏状态 - 辅助方法
     */
    private suspend fun checkFavoriteStatus(vodId: String, siteKey: String): Boolean {
        return try {
            repositoryAdapter.isFavorite(vodId, siteKey)
        } catch (e: Exception) {
            Log.e(TAG, "💥 检查收藏状态失败", e)
            false
        }
    }

    /**
     * 获取观看历史 - 辅助方法
     */
    private suspend fun getWatchHistory(vodId: String, siteKey: String): WatchHistory? {
        return try {
            val history = repositoryAdapter.getWatchHistory(vodId, siteKey)
            if (history != null) {
                WatchHistory(
                    vodId = history.vodId,
                    vodName = history.vodName,
                    siteKey = siteKey,
                    episodeName = history.vodRemarks ?: "",
                    position = history.position,
                    duration = history.duration,
                    watchTime = history.createTime,
                    isCompleted = history.position >= history.duration * 0.9
                )
            } else null
        } catch (e: Exception) {
            Log.e(TAG, "💥 获取观看历史失败", e)
            null
        }
    }
}
