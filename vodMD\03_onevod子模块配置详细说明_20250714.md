# onevod子模块配置详细说明

**文档版本**: v1.0  
**创建日期**: 2025-07-14  
**目标**: 详细说明onevod模块中每个子模块的配置方法

## 📋 子模块概览

### 核心子模块列表
```
onevod/
├── catvod/          # 爬虫核心引擎 (必需) ✅
├── quickjs/         # JavaScript引擎 (必需) ✅
├── chaquo/          # Python引擎 (必需) ✅
├── hook/            # Hook机制 (必需) ✅
├── forcetech/       # 解析器模块 (必需) ✅
├── jianpian/        # 解析器模块 (必需) ✅
├── thunder/         # 迅雷解析器 (必需) ✅
└── tvbus/           # 直播解析器 (必需) ✅
```

**重要说明**: 所有模块都是必需的，确保FongMi_TV功能100%完整保留

## 🔧 子模块配置详解

### 1. catvod模块 (核心爬虫引擎)

#### 目录结构
```
onevod/catvod/
├── src/main/java/com/github/catvod/
│   ├── crawler/     # 爬虫接口和实现
│   ├── net/         # 网络请求封装
│   ├── utils/       # 工具类
│   └── bean/        # 数据模型
├── libs/            # 第三方JAR包
└── build.gradle.kts # 构建配置
```

#### build.gradle.kts配置
```kotlin
plugins {
    id("com.onetv.library")
}

android {
    namespace = "com.github.catvod.crawler"
    compileSdk = 35
    
    defaultConfig {
        minSdk = 21
        targetSdk = 28
        consumerProguardFiles("consumer-rules.pro")
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    
    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    api(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))
    
    // 核心依赖
    api("androidx.annotation:annotation:1.6.0")
    api("androidx.preference:preference:1.2.1")
    api("com.google.code.gson:gson:2.11.0")
    api("com.squareup.okhttp3:okhttp:5.0.0-alpha.14")
    api("com.squareup.okhttp3:okhttp-dnsoverhttps:5.0.0-alpha.14")
    api("com.squareup.okhttp3:logging-interceptor:5.0.0-alpha.14")
    api("com.googlecode.juniversalchardet:juniversalchardet:1.0.3")
    api("com.orhanobut:logger:2.2.0")
    
    // Cronet支持
    api("com.google.net.cronet:cronet-okhttp:0.1.0")
    api("org.chromium.net:cronet-embedded:76.3809.111")
    
    // Guava工具库
    api("com.google.guava:guava:33.3.1-onetv") {
        exclude(group = "com.google.code.findbugs", module = "jsr305")
        exclude(group = "org.checkerframework", module = "checker-compat-qual")
        exclude(group = "org.checkerframework", module = "checker-qual")
        exclude(group = "com.google.errorprone", module = "error_prone_annotations")
        exclude(group = "com.google.j2objc", module = "j2objc-annotations")
        exclude(group = "org.codehaus.mojo", module = "animal-sniffer-annotations")
    }
}
```

### 2. quickjs模块 (JavaScript引擎)

#### 目录结构
```
onevod/quickjs/
├── src/main/
│   ├── java/com/script/
│   └── jniLibs/         # 原生库文件
│       ├── arm64-v8a/
│       └── armeabi-v7a/
└── build.gradle.kts
```

#### build.gradle.kts配置
```kotlin
plugins {
    id("com.onetv.library")
}

android {
    namespace = "com.script"
    compileSdk = 35
    
    defaultConfig {
        minSdk = 21
        targetSdk = 28
        consumerProguardFiles("consumer-rules.pro")
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    
    buildFeatures {
        buildConfig = true
    }
    
    packagingOptions {
        jniLibs {
            useLegacyPackaging = true
        }
    }
}

dependencies {
    api("androidx.annotation:annotation:1.6.0")
}
```

### 3. chaquo模块 (Python引擎)

#### 目录结构
```
onevod/chaquo/
├── src/main/
│   ├── java/com/chaquo/
│   └── python/          # Python脚本文件
└── build.gradle.kts
```

#### build.gradle.kts配置
```kotlin
plugins {
    id("com.onetv.library")
    id("com.chaquo.python")
}

android {
    namespace = "com.chaquo.python"
    compileSdk = 35
    
    defaultConfig {
        minSdk = 21
        targetSdk = 28
        consumerProguardFiles("consumer-rules.pro")
        
        ndk {
            abiFilters += listOf("arm64-v8a", "armeabi-v7a")
        }
        
        python {
            buildPython("3.8")
            pip {
                install("requests")
                install("beautifulsoup4")
                install("lxml")
                install("cryptography")
                install("pycryptodome")
            }
        }
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
}

dependencies {
    api("androidx.annotation:annotation:1.6.0")
}
```

### 4. hook模块 (Hook机制)

#### build.gradle.kts配置
```kotlin
plugins {
    id("com.onetv.library")
}

android {
    namespace = "com.github.tvbox.osc.hook"
    compileSdk = 35
    
    defaultConfig {
        minSdk = 21
        targetSdk = 28
        consumerProguardFiles("consumer-rules.pro")
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
}

dependencies {
    api("androidx.annotation:annotation:1.6.0")
    api(project(":onevod:catvod"))
}
```

### 5. 解析器模块 (forcetech, jianpian, thunder, tvbus)

#### 通用build.gradle.kts模板
```kotlin
plugins {
    id("com.onetv.library")
}

android {
    namespace = "com.forcetech.onetv" // 根据模块调整
    compileSdk = 35
    
    defaultConfig {
        minSdk = 21
        targetSdk = 28
        consumerProguardFiles("consumer-rules.pro")
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
}

dependencies {
    api("androidx.annotation:annotation:1.6.0")
    api(project(":onevod:catvod"))
    
    // 根据模块需要添加特定依赖
    implementation("com.squareup.okhttp3:okhttp:5.0.0-alpha.14")
    implementation("com.google.code.gson:gson:2.11.0")
}
```

## 📝 配置文件创建脚本

### PowerShell脚本 (create_submodule_configs.ps1)
```powershell
# 创建所有子模块的build.gradle.kts文件

$modules = @{
    "catvod" = "com.github.catvod.crawler"
    "quickjs" = "com.script"
    "chaquo" = "com.chaquo.python"
    "hook" = "com.github.tvbox.osc.hook"
    "forcetech" = "com.forcetech.android"
    "jianpian" = "com.jianpian.android"
    "thunder" = "com.thunder.android"
    "tvbus" = "com.tvbus.android"
}

foreach ($module in $modules.Keys) {
    $namespace = $modules[$module]
    $buildFile = "onevod\$module\build.gradle.kts"
    
    if (Test-Path "onevod\$module") {
        $content = @"
plugins {
    id("com.android.library")
}

android {
    namespace = "$namespace"
    compileSdk = 35
    
    defaultConfig {
        minSdk = 21
        targetSdk = 28
        consumerProguardFiles("consumer-rules.pro")
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    
    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    api("androidx.annotation:annotation:1.6.0")
    
    // 根据模块需要添加依赖
    if ("$module" -ne "quickjs") {
        api(project(":onevod:catvod"))
    }
    
    implementation("com.squareup.okhttp3:okhttp:5.0.0-alpha.14")
    implementation("com.google.code.gson:gson:2.11.0")
}
"@
        
        Set-Content -Path $buildFile -Value $content -Encoding UTF8
        Write-Host "已创建: $buildFile" -ForegroundColor Green
    }
}
```

### Bash脚本 (create_submodule_configs.sh)
```bash
#!/bin/bash

# 创建所有子模块的build.gradle.kts文件

declare -A modules=(
    ["catvod"]="com.github.catvod.crawler"
    ["quickjs"]="com.script"
    ["chaquo"]="com.chaquo.python"
    ["hook"]="com.github.tvbox.osc.hook"
    ["forcetech"]="com.forcetech.android"
    ["jianpian"]="com.jianpian.android"
    ["thunder"]="com.thunder.android"
    ["tvbus"]="com.tvbus.android"
)

for module in "${!modules[@]}"; do
    namespace=${modules[$module]}
    build_file="onevod/$module/build.gradle.kts"
    
    if [ -d "onevod/$module" ]; then
        cat > "$build_file" << EOF
plugins {
    id("com.android.library")
}

android {
    namespace = "$namespace"
    compileSdk = 35
    
    defaultConfig {
        minSdk = 21
        targetSdk = 28
        consumerProguardFiles("consumer-rules.pro")
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    
    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    api("androidx.annotation:annotation:1.6.0")
    
    if [ "$module" != "quickjs" ]; then
        api(project(":onevod:catvod"))
    fi
    
    implementation("com.squareup.okhttp3:okhttp:5.0.0-alpha.14")
    implementation("com.google.code.gson:gson:2.11.0")
}
EOF
        echo "已创建: $build_file"
    fi
done
```

## 🔍 配置验证

### 验证子模块配置
```bash
# 检查所有子模块build.gradle.kts文件
find onevod/ -name "build.gradle.kts" -exec echo "检查: {}" \; -exec head -5 {} \;

# 验证编译
./gradlew :onevod:catvod:compileDebugJavaWithJavac
./gradlew :onevod:quickjs:compileDebugJavaWithJavac
```

### 常见问题解决

#### 1. 包名冲突
```kotlin
// 如果出现包名冲突，修改namespace
android {
    namespace = "com.onetv.fongmi.catvod" // 使用唯一包名
}
```

#### 2. 依赖冲突
```kotlin
// 排除冲突的依赖
implementation("com.example:library:1.0") {
    exclude(group = "com.conflict", module = "conflict-module")
}
```

#### 3. 原生库问题
```kotlin
// 确保原生库正确打包
android {
    packagingOptions {
        jniLibs {
            useLegacyPackaging = true
        }
    }
}
```

## 📋 配置检查清单

- [ ] 所有子模块目录已创建
- [ ] 每个子模块都有build.gradle.kts文件
- [ ] namespace配置正确
- [ ] 依赖关系配置正确
- [ ] 原生库文件已复制
- [ ] Python脚本文件已复制
- [ ] 编译无错误
- [ ] 模块间依赖正常

## ⚠️ 注意事项

1. **模块顺序**：catvod必须最先编译，其他模块依赖它
2. **Python模块**：chaquo模块编译时间较长，可考虑排除
3. **原生库**：确保.so文件在正确的架构目录下
4. **版本统一**：所有子模块使用相同的编译版本
5. **依赖管理**：避免重复依赖和版本冲突

## 🚀 优化建议

1. **全功能集成**：包含所有子模块确保功能完整性
2. **全模块保留**：保留所有解析器模块，一个都不能少
3. **依赖优化**：使用api/implementation合理声明依赖
4. **编译优化**：启用并行编译和缓存
5. **功能完整性**：确保FongMi_TV所有功能100%保留
