# 新建 film 模块实施方案

## 方案概述

**建议**: 新建 `film` 模块，完整实现 FongMi/TV 解析系统  
**原因**: 风险最低，开发效率最高，实施最快速  
**策略**: 并行开发 → 功能验证 → 一次性切换 → 清理旧模块  

## 1. 实施优势分析

### 1.1 开发效率对比

| 方面 | movie 模块重构 | 新建 film 模块 |
|------|----------------|----------------|
| 开发速度 | 🐌 慢（需要小心替换） | 🚀 快（专注新功能） |
| 风险控制 | ⚠️ 高风险 | ✅ 低风险 |
| 测试复杂度 | 😰 复杂（新旧混合） | 😊 简单（独立测试） |
| 调试难度 | 😵 困难（问题定位复杂） | 😎 容易（问题清晰） |
| 回滚能力 | ❌ 困难 | ✅ 简单 |
| 并行开发 | ❌ 不可能 | ✅ 可以 |

### 1.2 时间成本对比

| 阶段 | movie 模块重构 | 新建 film 模块 |
|------|----------------|----------------|
| 准备阶段 | 2-3天（分析现有代码） | 0.5天（创建模块） |
| 开发阶段 | 15-20天（小心替换） | 8-10天（专注开发） |
| 测试阶段 | 5-7天（复杂测试） | 2-3天（独立测试） |
| 集成阶段 | 3-5天（修复冲突） | 1天（切换依赖） |
| **总计** | **25-35天** | **11-14天** |

## 2. film 模块架构设计

### 2.1 模块结构
```
film/
├── build.gradle.kts
├── consumer-rules.pro
└── src/main/
    ├── AndroidManifest.xml
    ├── java/top/cywin/onetv/film/
    │   ├── FilmApp.kt                    # 模块入口
    │   ├── catvod/                       # CatVod 核心
    │   ├── spider/                       # Spider 解析引擎
    │   ├── engine/                       # 多引擎系统
    │   ├── hook/                         # Hook 机制
    │   ├── proxy/                        # 代理系统
    │   ├── jar/                          # JAR 包管理
    │   ├── network/                      # 网络层
    │   ├── parser/                       # 解析器
    │   ├── concurrent/                   # 并发处理
    │   ├── cache/                        # 缓存系统
    │   ├── utils/                        # 工具类
    │   └── data/                         # 数据层
    │       ├── models/                   # 数据模型
    │       ├── repository/               # 仓库层
    │       ├── api/                      # API 服务
    │       └── database/                 # 数据库
    └── cpp/                              # Native 代码
        ├── CMakeLists.txt
        └── quickjs-android.cpp
```

### 2.2 依赖配置
```kotlin
// film/build.gradle.kts
dependencies {
    // 核心依赖
    implementation(project(":core:data"))
    implementation(project(":core:designsystem"))
    implementation(project(":core:util"))
    
    // FongMi/TV 解析依赖
    implementation("com.github.seven332:quickjs-onetv:0.9.0")
    implementation("org.jsoup:jsoup:1.15.3")
    implementation("com.squareup.okhttp3:okhttp:4.10.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.10.0")
    
    // 序列化
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.5.0")
    
    // 协程
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-onetv:1.6.4")
    
    // 数据库
    implementation("androidx.room:room-runtime:2.5.0")
    implementation("androidx.room:room-ktx:2.5.0")
    kapt("androidx.room:room-compiler:2.5.0")
}
```

## 3. 实施步骤

### 3.1 第一步：创建 film 模块（0.5天）
```bash
# 1. 创建模块目录
mkdir film
mkdir -p film/src/main/java/top/cywin/onetv/film
mkdir -p film/src/main/cpp

# 2. 创建配置文件
touch film/build.gradle.kts
touch film/consumer-rules.pro
touch film/src/main/AndroidManifest.xml

# 3. 更新 settings.gradle.kts
echo 'include(":film")' >> settings.gradle.kts
```

### 3.2 第二步：实现核心架构（2-3天）
```kotlin
// film/src/main/java/top/cywin/onetv/film/FilmApp.kt
object FilmApp {
    private var isInitialized = false
    
    // 核心管理器
    val spiderManager by lazy { SpiderManager() }
    val engineManager by lazy { EngineManager() }
    val proxyManager by lazy { ProxyManager() }
    val jarLoader by lazy { JarLoader() }
    
    suspend fun initialize(context: Context) {
        if (!isInitialized) {
            // 初始化所有组件
            initializeComponents(context)
            isInitialized = true
        }
    }
}
```

### 3.3 第三步：实现解析器（3-4天）
- 实现所有 Spider 解析器
- 实现多引擎系统
- 实现 JAR 包加载
- 实现代理系统

### 3.4 第四步：实现数据层（2-3天）
```kotlin
// film/src/main/java/top/cywin/onetv/film/data/repository/FilmRepository.kt
class FilmRepository {
    suspend fun loadConfig(configUrl: String): Result<VodConfigResponse>
    suspend fun getContentList(typeId: String, page: Int): Result<VodResponse>
    suspend fun searchContent(keyword: String): Result<VodResponse>
    suspend fun getVideoDetail(vodId: String): Result<VodItem>
    suspend fun getPlayUrl(flag: String, playUrl: String): Result<String>
}
```

### 3.5 第五步：集成测试（1-2天）
- 独立测试 film 模块功能
- 验证解析器工作正常
- 性能测试

### 3.6 第六步：切换依赖（1天）
```kotlin
// 修改其他模块的依赖
// tv/build.gradle.kts
dependencies {
    // 注释掉旧依赖
    // implementation(project(":movie"))
    
    // 添加新依赖
    implementation(project(":film"))
}

// 修改注入点
// tv/src/main/java/...
// 将 MovieApp 替换为 FilmApp
```

### 3.7 第七步：清理旧模块（0.5天）
```bash
# 删除 movie 模块
rm -rf movie/

# 更新 settings.gradle.kts
# 移除 include(":movie")
```

## 4. 风险控制

### 4.1 并行开发策略
```
时间线：
Day 1-7:   开发 film 模块（movie 模块继续工作）
Day 8-9:   测试 film 模块（movie 模块继续工作）
Day 10:    切换到 film 模块（一次性操作）
Day 11:    清理 movie 模块
```

### 4.2 回滚方案
```kotlin
// 如果 film 模块有问题，可以立即回滚
// tv/build.gradle.kts
dependencies {
    // 恢复旧依赖
    implementation(project(":movie"))
    
    // 注释新依赖
    // implementation(project(":film"))
}
```

### 4.3 渐进式切换
```kotlin
// 可以先在部分功能中测试 film 模块
class TestActivity {
    private val useNewFilmModule = BuildConfig.DEBUG // 调试版本使用新模块
    
    private val repository = if (useNewFilmModule) {
        FilmApp.filmRepository
    } else {
        MovieApp.vodRepository
    }
}
```

## 5. 实施时间表

| 天数 | 任务 | 交付物 |
|------|------|--------|
| Day 1 | 创建 film 模块，基础架构 | 模块框架 |
| Day 2-3 | 实现 Spider 系统 | 解析器框架 |
| Day 4-5 | 实现引擎和代理系统 | 核心功能 |
| Day 6-7 | 实现数据层和缓存 | 完整功能 |
| Day 8-9 | 独立测试验证 | 测试报告 |
| Day 10 | 切换依赖，集成测试 | 系统集成 |
| Day 11 | 清理旧模块，文档更新 | 项目清理 |

**总计**: 11天完成

## 6. 成功标准

### 6.1 功能标准
- ✅ 100% 实现 FongMi/TV 解析功能
- ✅ 所有现有点播功能正常工作
- ✅ 性能不低于现有系统

### 6.2 质量标准
- ✅ 无崩溃问题
- ✅ 内存使用合理
- ✅ 解析成功率 >95%

### 6.3 集成标准
- ✅ 直播系统完全不受影响
- ✅ 用户界面无变化
- ✅ 配置迁移成功

## 7. 结论

**强烈推荐新建 film 模块方案**，因为：

1. **开发效率最高**: 11天 vs 25-35天
2. **风险最低**: 并行开发，随时可回滚
3. **质量最好**: 全新代码，无历史包袱
4. **测试最简单**: 独立测试，问题定位清晰
5. **维护最容易**: 代码结构清晰，易于扩展

这个方案可以让您在最短时间内获得最好的结果，同时将风险降到最低。
