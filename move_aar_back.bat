@echo off
echo Moving AAR files back to TV module following FongMi/TV architecture...

echo Current movie/libs directory:
dir "movie\libs"
echo.

echo Moving AAR files from movie/libs to tv/libs...
for %%f in (movie\libs\*.aar) do (
    echo Moving %%f to tv\libs\
    move "%%f" "tv\libs\"
)

echo.
echo After moving - tv/libs directory:
dir "tv\libs"
echo.
echo After moving - movie/libs directory:
dir "movie\libs"

echo.
echo AAR files moved successfully following FongMi/TV architecture!
echo TV module (application) now contains all AAR files.
echo Movie module (library) will use AAR functionality through TV module.

pause
