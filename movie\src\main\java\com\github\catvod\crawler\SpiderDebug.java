package com.github.catvod.crawler;

import android.text.TextUtils;
import android.util.Log;

/**
 * 移植自原版FongMi_TV的SpiderDebug类
 * 适配我们的项目，使用Android Log替代Logger
 */
public class SpiderDebug {

    private static final String TAG = SpiderDebug.class.getSimpleName();

    public static void log(Throwable th) {
        if (th != null) th.printStackTrace();
    }

    public static void log(String msg) {
        if (!TextUtils.isEmpty(msg)) Log.d(TAG, msg);
    }
}
