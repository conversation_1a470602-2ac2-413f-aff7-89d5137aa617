package top.cywin.onetv.movie.player.danmaku;

import java.io.InputStream;

import master.flame.danmaku.danmaku.parser.android.AndroidFileSource;
// import master.flame.danmaku.danmaku.parser.IllegalDataException; // 使用本地AAR文件中的弹幕库

public interface ILoader {
    void load(String url) throws Exception; // 使用通用Exception替代IllegalDataException
    void load(InputStream stream) throws Exception; // 使用通用Exception替代IllegalDataException
    AndroidFileSource getDataSource();
}
