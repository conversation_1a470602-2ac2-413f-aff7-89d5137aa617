package top.cywin.onetv.movie.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import top.cywin.onetv.movie.MovieApp
import top.cywin.onetv.movie.adapter.ViewModelAdapter
import top.cywin.onetv.movie.ui.model.MovieItem
import top.cywin.onetv.movie.event.SearchResultEvent
import top.cywin.onetv.movie.ui.screens.MovieSearchResultListUiState
import top.cywin.onetv.movie.ui.screens.SiteInfo

/**
 * 搜索结果列表页面ViewModel
 */
class MovieSearchResultListViewModel : ViewModel() {
    
    companion object {
        private const val TAG = "MovieSearchResultListViewModel"
    }

    private val _uiState = MutableStateFlow(MovieSearchResultListUiState())
    val uiState: StateFlow<MovieSearchResultListUiState> = _uiState.asStateFlow()

    private val repositoryAdapter = MovieApp.getInstance().repositoryAdapter

    init {
        // 注册EventBus
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun onCleared() {
        super.onCleared()
        // 注销EventBus
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    /**
     * 设置初始空结果状态（页面初始化时调用）- 实现瞬间跳转
     */
    fun setInitialEmptyState(keyword: String) {
        Log.d(TAG, "🚀 [修复逻辑] 设置初始空结果状态: keyword=$keyword")
        _uiState.value = _uiState.value.copy(
            keyword = keyword,
            isLoading = false,  // 🔥 关键：不显示加载状态，实现瞬间跳转
            allResults = emptyList(),
            filteredResults = emptyList(),
            sites = emptyList(),
            selectedSite = null,
            hasMore = false,
            error = null
        )
        Log.d(TAG, "✅ [修复逻辑] 初始空结果状态设置完成，等待动态搜索结果")
    }

    /**
     * 设置加载状态（页面初始化时调用）
     */
    fun setLoadingState(keyword: String) {
        Log.d(TAG, "🔄 设置初始加载状态: keyword=$keyword")
        _uiState.value = _uiState.value.copy(
            keyword = keyword,
            isLoading = true,
            error = null
        )
    }

    /**
     * 设置搜索结果（从导航事件接收已有的搜索结果）
     */
    fun setSearchResults(keyword: String, results: List<top.cywin.onetv.movie.bean.Vod>) {
        Log.d(TAG, "📋 [修改逻辑] 接收已有搜索结果: keyword=$keyword, count=${results.size}")

        val movieItems = results.map { vod ->
            ViewModelAdapter.convertVodToMovie(vod)
        }.filterNotNull()

        // 🎯 应用精准匹配过滤
        val keywordLower = keyword.trim().lowercase()
        val preciseResults = movieItems.filter { movie ->
            val movieNameLower = movie.vodName.trim().lowercase()
            val isExactMatch = movieNameLower.contains(keywordLower)

            if (isExactMatch) {
                Log.d(TAG, "✅ 精准匹配: [${movie.vodName}] 包含 [$keyword]")
            } else {
                Log.d(TAG, "❌ 拒绝匹配: [${movie.vodName}] 不包含 [$keyword]")
            }

            isExactMatch
        }

        Log.d(TAG, "🎯 精准匹配过滤: 原始${movieItems.size}个 -> 精准${preciseResults.size}个")

        // 按站点分组统计
        val siteGroups = preciseResults.groupBy { it.siteKey }
        val sites = siteGroups.map { (siteKey, movies) ->
            SiteInfo(
                siteKey = siteKey,
                siteName = getSiteName(siteKey),
                resultCount = movies.size
            )
        }.sortedByDescending { it.resultCount } // 按结果数量降序排列

        _uiState.value = _uiState.value.copy(
            keyword = keyword,
            isLoading = false,
            allResults = preciseResults,
            filteredResults = preciseResults, // 默认显示全部结果
            sites = sites,
            selectedSite = null, // 默认选择全部显示
            hasMore = false,
            error = null
        )

        Log.d(TAG, "✅ [修改逻辑] 搜索结果设置完成: 总数=${preciseResults.size}, 站点数=${sites.size}")
    }

    /**
     * 搜索电影（备用方法，用于刷新）
     */
    fun searchMovies(keyword: String) {
        Log.d(TAG, "🔍 开始搜索电影: $keyword")

        _uiState.value = _uiState.value.copy(
            keyword = keyword,
            isLoading = true,
            error = null
        )

        viewModelScope.launch {
            try {
                // 使用RepositoryAdapter的搜索功能
                repositoryAdapter.searchContent(keyword)
                Log.d(TAG, "✅ 搜索请求已发送")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 搜索失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "搜索失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 选择站点筛选
     */
    fun selectSite(site: SiteInfo?) {
        Log.d(TAG, "🔄 选择站点筛选: ${site?.siteName ?: "全部显示"}")
        
        val filteredResults = if (site == null) {
            _uiState.value.allResults
        } else {
            _uiState.value.allResults.filter { it.siteKey == site.siteKey }
        }

        _uiState.value = _uiState.value.copy(
            selectedSite = site,
            filteredResults = filteredResults
        )
    }

    /**
     * 监听搜索结果事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSearchResult(event: SearchResultEvent) {
        Log.d(TAG, "📡 收到搜索结果事件: keyword=${event.keyword}, count=${event.results.size}")

        // 如果结果为空，跳过处理
        if (event.results.isEmpty()) {
            Log.d(TAG, "⚠️ 搜索结果为空，跳过处理")
            return
        }

        val newMovieItems = event.results.map { vod ->
            ViewModelAdapter.convertVodToMovie(vod)
        }.filterNotNull()

        // 如果转换后的结果为空，跳过处理
        if (newMovieItems.isEmpty()) {
            Log.d(TAG, "⚠️ 转换后的搜索结果为空，跳过处理")
            return
        }

        // 🎯 精准匹配过滤：参考原版FongMi_TV，只保留包含完整关键词的结果
        val keyword = event.keyword.trim()
        val keywordLower = keyword.lowercase()

        val preciseResults = newMovieItems.filter { movie ->
            val movieName = movie.vodName.trim()
            val movieNameLower = movieName.lowercase()

            // 🔥 核心规则：电影名必须包含完整关键词（*朝雪录* 通配符匹配）
            // 这样可以匹配：朝雪录、朝雪录2025、1234朝雪录、朝雪录之xxx等
            // 但拒绝：异闻录、朝阳雪花录音等单字匹配
            val isExactMatch = movieNameLower.contains(keywordLower)

            if (isExactMatch) {
                Log.d(TAG, "✅ 精准匹配: [$movieName] 包含 [$keyword]")
            } else {
                Log.d(TAG, "❌ 拒绝匹配: [$movieName] 不包含 [$keyword]")
            }

            isExactMatch
        }

        Log.d(TAG, "🎯 精准匹配过滤: 原始${newMovieItems.size}个 -> 精准${preciseResults.size}个")

        // 🔥 关键修复：累加结果而不是覆盖
        val currentState = _uiState.value
        val existingResults = currentState.allResults

        // 合并新结果到现有结果中（去重处理）- 使用精准过滤后的结果
        val allResults = (existingResults + preciseResults).distinctBy { "${it.siteKey}_${it.vodId}" }

        // 重新按站点分组统计
        val siteGroups = allResults.groupBy { it.siteKey }

        // 🔧 调试日志：检查分组结果
        Log.d(TAG, "🔍 站点分组调试:")
        siteGroups.forEach { (siteKey, movies) ->
            Log.d(TAG, "  站点: '$siteKey' -> ${movies.size}个结果")
            movies.take(3).forEach { movie ->
                Log.d(TAG, "    - ${movie.vodName} (siteKey='${movie.siteKey}')")
            }
        }

        val sites = siteGroups.map { (siteKey, movies) ->
            SiteInfo(
                siteKey = siteKey,
                siteName = getSiteName(siteKey),
                resultCount = movies.size
            )
        }.sortedByDescending { it.resultCount } // 按结果数量降序排列

        // 更新过滤结果（如果当前有选中的站点，需要重新过滤）
        val filteredResults = if (currentState.selectedSite == null) {
            allResults // 显示全部结果
        } else {
            allResults.filter { it.siteKey == currentState.selectedSite.siteKey }
        }

        _uiState.value = currentState.copy(
            isLoading = false,
            allResults = allResults,
            filteredResults = filteredResults,
            sites = sites,
            hasMore = event.hasMore,
            error = null
        )

        Log.d(TAG, "✅ 搜索结果累加完成: 新增=${preciseResults.size}, 总数=${allResults.size}, 站点数=${sites.size}")

        // 详细日志：显示每个站点的结果数量
        sites.forEach { site ->
            Log.d(TAG, "📊 站点统计: ${site.siteName} = ${site.resultCount}个结果")
        }
    }

    /**
     * 计算字符串相似度（简单的Jaccard相似度）
     */
    private fun calculateSimilarity(str1: String, str2: String): Double {
        if (str1.isEmpty() && str2.isEmpty()) return 1.0
        if (str1.isEmpty() || str2.isEmpty()) return 0.0

        val set1 = str1.toCharArray().toSet()
        val set2 = str2.toCharArray().toSet()

        val intersection = set1.intersect(set2).size
        val union = set1.union(set2).size

        return intersection.toDouble() / union.toDouble()
    }

    /**
     * 🎯 严格相似度计算 - 用于精准搜索过滤
     * 要求连续字符匹配，避免"异闻录"匹配"朝雪录"的情况
     */
    private fun calculateStrictSimilarity(movieName: String, keyword: String): Double {
        if (movieName.isEmpty() && keyword.isEmpty()) return 1.0
        if (movieName.isEmpty() || keyword.isEmpty()) return 0.0

        // 计算最长公共子序列长度
        val lcs = longestCommonSubsequence(movieName, keyword)

        // 相似度 = LCS长度 / 较长字符串长度
        return lcs.toDouble() / maxOf(movieName.length, keyword.length)
    }

    /**
     * 计算最长公共子序列长度
     */
    private fun longestCommonSubsequence(str1: String, str2: String): Int {
        val m = str1.length
        val n = str2.length
        val dp = Array(m + 1) { IntArray(n + 1) }

        for (i in 1..m) {
            for (j in 1..n) {
                if (str1[i - 1] == str2[j - 1]) {
                    dp[i][j] = dp[i - 1][j - 1] + 1
                } else {
                    dp[i][j] = maxOf(dp[i - 1][j], dp[i][j - 1])
                }
            }
        }

        return dp[m][n]
    }

    /**
     * 🌐 多语言匹配检查：支持中文、拼音、英文、拼音首字母匹配
     * 例如搜索"朝雪录"可以匹配：朝雪录、zhaoxuelu、zxl等
     * 例如搜索"射雕英雄传"可以匹配：射雕英雄传、shediaoyingxiongzhuan、sdyxz等
     */
    private fun isMultiLanguageMatch(movieName: String, keyword: String): Boolean {
        // 转换为拼音进行匹配
        val movieNamePinyin = convertToPinyin(movieName)
        val keywordPinyin = convertToPinyin(keyword)

        // 检查完整拼音匹配
        if (movieNamePinyin.contains(keywordPinyin) || keywordPinyin.contains(movieNamePinyin)) {
            return true
        }

        // 🎯 新增：拼音首字母匹配
        val movieNameInitials = convertToPinyinInitials(movieName)
        val keywordInitials = convertToPinyinInitials(keyword)

        // 检查拼音首字母匹配
        if (movieNameInitials.isNotEmpty() && keywordInitials.isNotEmpty()) {
            if (movieNameInitials.contains(keywordInitials) || keywordInitials.contains(movieNameInitials)) {
                return true
            }
        }

        // 检查英文字母匹配（去除空格和特殊字符）
        val movieNameAlpha = extractAlphaNumeric(movieName)
        val keywordAlpha = extractAlphaNumeric(keyword)

        if (movieNameAlpha.isNotEmpty() && keywordAlpha.isNotEmpty()) {
            if (movieNameAlpha.contains(keywordAlpha) || keywordAlpha.contains(movieNameAlpha)) {
                return true
            }
        }

        return false
    }

    /**
     * 简化的中文转拼音函数
     * 注意：这是一个简化版本，实际项目中建议使用专业的拼音库如pinyin4j
     */
    private fun convertToPinyin(text: String): String {
        // 简化的拼音映射表（包含影视常用字）
        val pinyinMap = mapOf(
            // 电影名称常用字
            '朝' to "zhao", '雪' to "xue", '录' to "lu", '异' to "yi", '闻' to "wen",
            '梅' to "mei", '根' to "gen", '花' to "hua", '爱' to "ai", '情' to "qing",
            '故' to "gu", '事' to "shi", '传' to "chuan", '奇' to "qi", '战' to "zhan",
            '争' to "zheng", '英' to "ying", '雄' to "xiong", '美' to "mei", '国' to "guo",
            '中' to "zhong", '华' to "hua", '人' to "ren", '民' to "min", '共' to "gong",
            '和' to "he", '电' to "dian", '影' to "ying", '视' to "shi", '剧' to "ju",
            '综' to "zong", '艺' to "yi", '动' to "dong", '画' to "hua", '片' to "pian",
            '纪' to "ji", '大' to "da", '小' to "xiao", '新' to "xin", '老' to "lao",
            '好' to "hao", '坏' to "huai", '长' to "chang", '短' to "duan", '高' to "gao",
            '低' to "di", '快' to "kuai", '慢' to "man", '强' to "qiang", '弱' to "ruo",
            '红' to "hong", '黑' to "hei", '白' to "bai", '蓝' to "lan", '绿' to "lv",
            '黄' to "huang", '紫' to "zi", '粉' to "fen", '灰' to "hui", '棕' to "zong",
            // 数字
            '一' to "yi", '二' to "er", '三' to "san", '四' to "si", '五' to "wu",
            '六' to "liu", '七' to "qi", '八' to "ba", '九' to "jiu", '十' to "shi",
            '零' to "ling", '百' to "bai", '千' to "qian", '万' to "wan",
            // 常见姓氏
            '王' to "wang", '李' to "li", '张' to "zhang", '刘' to "liu", '陈' to "chen",
            '杨' to "yang", '赵' to "zhao", '黄' to "huang", '周' to "zhou", '吴' to "wu",
            '徐' to "xu", '孙' to "sun", '胡' to "hu", '朱' to "zhu", '高' to "gao",
            '林' to "lin", '何' to "he", '郭' to "guo", '马' to "ma", '罗' to "luo"
        )

        return text.map { char ->
            pinyinMap[char] ?: char.toString()
        }.joinToString("")
    }

    /**
     * 🎯 中文转拼音首字母
     * 例如："朝雪录" -> "zxl"，"射雕英雄传" -> "sdyxz"
     */
    private fun convertToPinyinInitials(text: String): String {
        // 拼音首字母映射表
        val pinyinInitialsMap = mapOf(
            // 电影名称常用字
            '朝' to "z", '雪' to "x", '录' to "l", '异' to "y", '闻' to "w",
            '梅' to "m", '根' to "g", '花' to "h", '爱' to "a", '情' to "q",
            '故' to "g", '事' to "s", '传' to "c", '奇' to "q", '战' to "z",
            '争' to "z", '英' to "y", '雄' to "x", '美' to "m", '国' to "g",
            '中' to "z", '华' to "h", '人' to "r", '民' to "m", '共' to "g",
            '和' to "h", '电' to "d", '影' to "y", '视' to "s", '剧' to "j",
            '综' to "z", '艺' to "y", '动' to "d", '画' to "h", '片' to "p",
            '纪' to "j", '大' to "d", '小' to "x", '新' to "x", '老' to "l",
            '好' to "h", '坏' to "h", '长' to "c", '短' to "d", '高' to "g",
            '低' to "d", '快' to "k", '慢' to "m", '强' to "q", '弱' to "r",
            '红' to "h", '黑' to "h", '白' to "b", '蓝' to "l", '绿' to "l",
            '黄' to "h", '紫' to "z", '粉' to "f", '灰' to "h", '棕' to "z",
            // 射雕英雄传相关字
            '射' to "s", '雕' to "d", '神' to "s", '侠' to "x", '倚' to "y",
            '天' to "t", '屠' to "t", '龙' to "l", '记' to "j", '笑' to "x",
            '傲' to "a", '江' to "j", '湖' to "h", '鹿' to "l", '鼎' to "d",
            '书' to "s", '剑' to "j", '恩' to "e", '仇' to "c", '飞' to "f",
            '狐' to "h", '外' to "w", '白' to "b", '马' to "m", '啸' to "x",
            '西' to "x", '风' to "f", '连' to "l", '城' to "c", '诀' to "j",
            // 数字
            '一' to "y", '二' to "e", '三' to "s", '四' to "s", '五' to "w",
            '六' to "l", '七' to "q", '八' to "b", '九' to "j", '十' to "s",
            '零' to "l", '百' to "b", '千' to "q", '万' to "w",
            // 常见姓氏
            '王' to "w", '李' to "l", '张' to "z", '刘' to "l", '陈' to "c",
            '杨' to "y", '赵' to "z", '黄' to "h", '周' to "z", '吴' to "w",
            '徐' to "x", '孙' to "s", '胡' to "h", '朱' to "z", '高' to "g",
            '林' to "l", '何' to "h", '郭' to "g", '马' to "m", '罗' to "l",
            // 更多常用字
            '的' to "d", '了' to "l", '在' to "z", '是' to "s", '我' to "w",
            '有' to "y", '他' to "t", '这' to "z", '个' to "g", '们' to "m",
            '来' to "l", '到' to "d", '时' to "s", '会' to "h", '地' to "d",
            '她' to "t", '说' to "s", '其' to "q", '只' to "z", '如' to "r",
            '被' to "b", '还' to "h", '因' to "y", '由' to "y", '从' to "c",
            '都' to "d", '同' to "t", '三' to "s", '两' to "l", '看' to "k",
            '出' to "c", '而' to "e", '要' to "y", '当' to "d", '开' to "k",
            '年' to "n", '生' to "s", '自' to "z", '面' to "m", '发' to "f",
            '成' to "c", '方' to "f", '多' to "d", '经' to "j", '么' to "m",
            '去' to "q", '法' to "f", '学' to "x", '如' to "r", '水' to "s",
            '化' to "h", '高' to "g", '自' to "z", '二' to "e", '理' to "l",
            '起' to "q", '小' to "x", '物' to "w", '现' to "x", '实' to "s",
            '加' to "j", '量' to "l", '都' to "d", '两' to "l", '体' to "t",
            '制' to "z", '机' to "j", '当' to "d", '使' to "s", '点' to "d",
            '从' to "c", '业' to "y", '本' to "b", '去' to "q", '把' to "b",
            '性' to "x", '好' to "h", '应' to "y", '开' to "k", '它' to "t",
            '合' to "h", '还' to "h", '因' to "y", '由' to "y", '其' to "q"
        )

        return text.mapNotNull { char ->
            pinyinInitialsMap[char]
        }.joinToString("")
    }

    /**
     * 提取字母数字字符
     */
    private fun extractAlphaNumeric(text: String): String {
        return text.filter { it.isLetterOrDigit() }.lowercase()
    }

    /**
     * 添加搜索结果 - 用于动态更新搜索结果
     */
    fun addSearchResults(event: top.cywin.onetv.movie.event.SearchResultEvent) {
        Log.d(TAG, "🔄 [修复逻辑] 添加搜索结果: keyword=${event.keyword}, 新增结果数=${event.results.size}")

        val currentState = _uiState.value

        // 转换搜索结果为MovieItem
        val newMovieItems = event.results.map { vod ->
            ViewModelAdapter.convertVodToMovie(vod)
        }.filterNotNull()

        // 🎯 应用精准匹配过滤
        val keyword = event.keyword.trim()
        val keywordLower = keyword.lowercase()
        val preciseNewResults = newMovieItems.filter { movie ->
            val movieNameLower = movie.vodName.trim().lowercase()
            val isExactMatch = movieNameLower.contains(keywordLower)

            if (isExactMatch) {
                Log.d(TAG, "✅ 精准匹配: [${movie.vodName}] 包含 [$keyword]")
            } else {
                Log.d(TAG, "❌ 拒绝匹配: [${movie.vodName}] 不包含 [$keyword]")
            }

            isExactMatch
        }

        Log.d(TAG, "🎯 精准匹配过滤: 新增原始${newMovieItems.size}个 -> 精准${preciseNewResults.size}个")

        // 合并到现有结果中
        val allResults = (currentState.allResults + preciseNewResults).distinctBy { "${it.siteKey}_${it.vodId}" }

        // 按站点分组统计
        val siteGroups = allResults.groupBy { it.siteKey }
        val sites = siteGroups.map { (siteKey, movies) ->
            SiteInfo(
                siteKey = siteKey,
                siteName = getSiteName(siteKey),
                resultCount = movies.size
            )
        }.sortedByDescending { it.resultCount }

        // 更新过滤结果
        val filteredResults = if (currentState.selectedSite == null) {
            allResults
        } else {
            allResults.filter { it.siteKey == currentState.selectedSite.siteKey }
        }

        _uiState.value = currentState.copy(
            isLoading = false,
            allResults = allResults,
            filteredResults = filteredResults,
            sites = sites,
            hasMore = event.hasMore,
            error = null
        )

        Log.d(TAG, "✅ [修复逻辑] 搜索结果添加完成: 总数=${allResults.size}, 站点数=${sites.size}")
    }

    /**
     * 清理搜索状态 - 用于返回时避免卡在加载状态
     */
    fun clearSearchState() {
        Log.d(TAG, "🧹 清理搜索状态")
        _uiState.value = _uiState.value.copy(
            isLoading = false,
            error = null
        )
    }

    /**
     * 获取站点名称
     */
    private fun getSiteName(siteKey: String): String {
        return try {
            val site = top.cywin.onetv.movie.api.config.VodConfig.get().getSite(siteKey)
            site?.name ?: siteKey
        } catch (e: Exception) {
            Log.w(TAG, "获取站点名称失败: $siteKey", e)
            siteKey
        }
    }

    /**
     * 刷新搜索结果
     */
    fun refresh() {
        val currentKeyword = _uiState.value.keyword
        if (currentKeyword.isNotEmpty()) {
            searchMovies(currentKeyword)
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}
