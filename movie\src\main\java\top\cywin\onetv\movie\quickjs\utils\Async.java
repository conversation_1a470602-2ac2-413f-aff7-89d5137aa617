package top.cywin.onetv.movie.quickjs.utils;

import com.whl.quickjs.wrapper.JSCallFunction;
import com.whl.quickjs.wrapper.JSFunction;
import com.whl.quickjs.wrapper.JSObject;

import java9.util.concurrent.CompletableFuture;

public class Async {

    private final CompletableFuture<Object> future;

    public static CompletableFuture<Object> run(JSObject object, String name, Object[] args) {
        return new Async().call(object, name, args);
    }

    private Async() {
        this.future = new CompletableFuture<>();
    }

    private CompletableFuture<Object> call(JSObject object, String name, Object[] args) {
        android.util.Log.d("ONETV_JS_FUNCTION", "🔄 调用JavaScript函数: " + name + ", 参数数量: " + (args != null ? args.length : 0));

        JSFunction function = object.getJSFunction(name);
        if (function == null) {
            android.util.Log.w("ONETV_JS_FUNCTION", "❌ JavaScript函数不存在: " + name);
            return empty();
        }

        android.util.Log.d("ONETV_JS_FUNCTION", "✅ 找到JavaScript函数: " + name + ", 开始调用");
        long startTime = System.currentTimeMillis();

        Object result = function.call(args);

        long endTime = System.currentTimeMillis();
        android.util.Log.d("ONETV_JS_FUNCTION", "✅ JavaScript函数调用完成: " + name + ", 耗时: " + (endTime - startTime) + "ms");

        if (result != null) {
            String resultStr = result.toString();
            int resultLength = resultStr.length();
            android.util.Log.d("ONETV_JS_FUNCTION", "📊 JavaScript函数返回结果: " + name + ", 长度: " + resultLength + "字符");
            if (resultLength > 100) {
                android.util.Log.d("ONETV_JS_FUNCTION", "📄 结果预览: " + resultStr.substring(0, 100) + "...");
            } else {
                android.util.Log.d("ONETV_JS_FUNCTION", "📄 完整结果: " + resultStr);
            }
        } else {
            android.util.Log.d("ONETV_JS_FUNCTION", "📊 JavaScript函数返回: null");
        }

        if (result instanceof JSObject) then(result);
        else future.complete(result);
        function.release();
        return future;
    }

    private CompletableFuture<Object> empty() {
        future.complete(null);
        return future;
    }

    private void then(Object result) {
        JSObject promise = (JSObject) result;
        JSFunction then = promise.getJSFunction("then");
        if (then != null) then.call(callback);
        if (then != null) then.release();
    }

    private final JSCallFunction callback = new JSCallFunction() {
        @Override
        public Object call(Object... args) {
            future.complete(args[0]);
            return null;
        }
    };
}
