# VOD资源隔离与AndroidManifest合并策略实施方案

## 问题分析

### 当前问题
1. **应用名称错误**：构建后显示"影视点播"而不是"壹来电视"
2. **UI界面异常**：文字图标都非常大，不适配
3. **资源冲突**：VOD模块的资源覆盖了TV模块的资源

### 根本原因
- VOD模块的`app_name`与TV模块冲突
- 资源合并时VOD资源优先级过高
- AndroidManifest合并策略不当

## 解决方案概述

### 核心策略
1. **资源前缀隔离**：给VOD所有资源添加`vod_`前缀
2. **AndroidManifest合并优化**：确保TV配置优先级最高
3. **同步更新引用**：修改VOD模块中所有资源引用

## 详细实施步骤

### 第一阶段：资源前缀配置

#### 1.1 在VOD模块添加资源前缀强制配置
**文件**：`vod/build.gradle.kts`
**位置**：android块内
```kotlin
android {
    // 强制所有新资源使用vod_前缀
    resourcePrefix = "vod_"
    
    // 添加资源合并日志
    println("[OneTV-Build] VOD模块启用资源前缀: vod_")
}
```

#### 1.2 字符串资源重命名
**文件**：`vod/src/main/res/values/strings.xml`

**需要重命名的资源**：
- `app_name` → `vod_app_name`
- 其他可能冲突的字符串资源

**示例**：
```xml
<!-- 修改前 -->
<string name="app_name">VOD_TV</string>

<!-- 修改后 -->
<string name="vod_app_name">VOD_TV</string>
```

#### 1.3 颜色资源重命名
**文件**：`vod/src/main/res/values/colors.xml`

**重命名规则**：所有颜色添加`vod_`前缀
```xml
<!-- 示例 -->
<color name="vod_primary_color">#FF6200EE</color>
<color name="vod_background_color">#FFFFFF</color>
```

#### 1.4 尺寸资源重命名
**文件**：`vod/src/main/res/values/dimens.xml`

**重命名规则**：所有尺寸添加`vod_`前缀
```xml
<!-- 示例 -->
<dimen name="vod_text_size">16sp</dimen>
<dimen name="vod_margin_large">16dp</dimen>
```

#### 1.5 样式和主题资源重命名
**文件**：`vod/src/main/res/values/styles.xml`, `vod/src/main/res/values/themes.xml`

**重命名规则**：所有样式和主题添加`Vod`前缀
```xml
<!-- 示例 -->
<style name="VodAppTheme" parent="Theme.Material3">
<style name="VodButtonStyle" parent="Widget.Material3.Button">
```

### 第二阶段：布局和Drawable资源重命名

#### 2.1 布局文件重命名
**目录**：`vod/src/main/res/layout/`

**重命名规则**：所有布局文件添加`vod_`前缀
```
activity_home.xml → vod_activity_home.xml
fragment_list.xml → vod_fragment_list.xml
item_video.xml → vod_item_video.xml
```

#### 2.2 Drawable资源重命名
**目录**：`vod/src/main/res/drawable/`

**重命名规则**：所有drawable文件添加`vod_`前缀
```
ic_play.xml → vod_ic_play.xml
bg_card.xml → vod_bg_card.xml
```

### 第三阶段：代码引用同步更新

#### 3.1 Java/Kotlin文件中的资源引用更新

**需要更新的文件类型**：
- Activity类
- Fragment类
- Adapter类
- 自定义View类

**更新示例**：
```java
// 修改前
R.string.app_name → R.string.vod_app_name
R.layout.activity_home → R.layout.vod_activity_home
R.drawable.ic_play → R.drawable.vod_ic_play
R.color.primary_color → R.color.vod_primary_color
```

#### 3.2 关键文件更新清单

**HomeActivity.java**：
```java
// 修改
ResUtil.getString(R.string.app_name) 
// 改为
ResUtil.getString(R.string.vod_app_name)
```

**其他Activity和Fragment**：
- 检查所有setContentView()调用
- 检查所有findViewById()调用
- 检查所有资源引用

#### 3.3 XML布局文件中的引用更新

**需要检查的引用类型**：
- `@string/` 引用
- `@color/` 引用
- `@dimen/` 引用
- `@style/` 引用
- `@drawable/` 引用

### 第四阶段：AndroidManifest合并策略

#### 4.1 TV模块AndroidManifest优化
**文件**：`tv/src/main/AndroidManifest.xml`

**添加合并策略**：
```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    
    <application
        android:name=".TVApplication"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        tools:replace="android:label,android:name,android:icon">
        
        <!-- 确保TV的配置优先级最高 -->
    </application>
</manifest>
```

#### 4.2 VOD模块AndroidManifest优化
**文件**：`vod/src/leanback/AndroidManifest.xml`

**添加合并标记**：
```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    
    <application tools:node="merge">
        <!-- 所有Activity配置添加merge标记 -->
        <activity
            android:name=".ui.activity.HomeActivity"
            tools:node="merge" />
    </application>
</manifest>
```

### 第五阶段：构建配置优化

#### 5.1 TV模块资源合并优先级配置
**文件**：`tv/build.gradle.kts`

**添加配置**：
```kotlin
android {
    packagingOptions {
        // 确保TV模块资源优先级
        pickFirst "**/strings.xml"
        pickFirst "**/AndroidManifest.xml"
    }
    
    // 资源合并日志
    println("[OneTV-Build] TV模块配置资源合并优先级")
}
```

## 实施检查清单

### 资源重命名检查
- [ ] strings.xml 所有字符串添加vod_前缀
- [ ] colors.xml 所有颜色添加vod_前缀  
- [ ] dimens.xml 所有尺寸添加vod_前缀
- [ ] styles.xml 所有样式添加Vod前缀
- [ ] 所有layout文件添加vod_前缀
- [ ] 所有drawable文件添加vod_前缀

### 代码引用更新检查
- [ ] HomeActivity.java 资源引用更新
- [ ] 所有Activity类资源引用更新
- [ ] 所有Fragment类资源引用更新
- [ ] 所有Adapter类资源引用更新
- [ ] 所有XML布局文件引用更新

### AndroidManifest配置检查
- [ ] TV模块添加tools:replace属性
- [ ] VOD模块添加tools:node="merge"属性
- [ ] 确保VOD模块没有主启动Activity配置

### 构建测试检查
- [ ] 清理构建缓存
- [ ] VOD模块单独构建成功
- [ ] TV模块构建成功
- [ ] 生成的APK应用名称为"壹来电视"
- [ ] UI界面显示正常，文字图标大小适配

## 预期结果

### 成功标准
1. **应用名称正确**：安装后显示"壹来电视"
2. **UI界面正常**：文字图标大小适配，界面布局正确
3. **功能完整**：TV和VOD功能都正常工作
4. **资源隔离**：VOD资源不再与TV资源冲突

### 验证方法
1. 构建并安装APK
2. 检查应用名称和图标
3. 测试TV直播功能
4. 测试VOD点播功能
5. 检查界面适配情况

## 风险评估

### 潜在风险
1. **资源引用遗漏**：可能有部分资源引用未更新
2. **构建失败**：资源重命名可能导致编译错误
3. **功能异常**：资源引用错误可能导致功能问题

### 风险缓解
1. **分步实施**：按阶段逐步实施，每步都进行测试
2. **备份代码**：实施前创建Git分支备份
3. **详细检查**：使用IDE的全局搜索功能检查所有引用

## 后续优化

### 长期优化建议
1. **自动化检查**：编写脚本自动检查资源命名规范
2. **CI/CD集成**：在构建流程中添加资源冲突检查
3. **文档维护**：建立资源命名规范文档

## 详细实施命令

### 资源文件批量重命名命令

#### 布局文件重命名
```bash
# 在vod/src/main/res/layout/目录下执行
for file in *.xml; do
    if [[ $file != vod_* ]]; then
        mv "$file" "vod_$file"
    fi
done
```

#### Drawable文件重命名
```bash
# 在vod/src/main/res/drawable/目录下执行
for file in *; do
    if [[ $file != vod_* ]]; then
        mv "$file" "vod_$file"
    fi
done
```

### 代码引用批量替换

#### 使用IDE全局替换
1. **字符串引用替换**：
   - 查找：`R.string.app_name`
   - 替换：`R.string.vod_app_name`
   - 范围：vod模块

2. **布局引用替换**：
   - 查找：`R.layout.activity_`
   - 替换：`R.layout.vod_activity_`
   - 范围：vod模块

3. **Drawable引用替换**：
   - 查找：`R.drawable.`
   - 替换：`R.drawable.vod_`
   - 范围：vod模块

### 关键文件修改示例

#### HomeActivity.java 修改示例
```java
// 修改前
public class HomeActivity extends BaseActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_home);
        mBinding.title.setText(title.isEmpty() ?
            ResUtil.getString(R.string.app_name) : title);
    }
}

// 修改后
public class HomeActivity extends BaseActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.vod_activity_home);
        mBinding.title.setText(title.isEmpty() ?
            ResUtil.getString(R.string.vod_app_name) : title);
    }
}
```

## 测试验证步骤

### 1. 编译测试
```bash
# 清理构建缓存
./gradlew clean

# 单独构建VOD模块
./gradlew :vod:assembleDebug

# 构建TV模块
./gradlew :tv:assembleDebug
```

### 2. 资源检查
```bash
# 检查合并后的资源文件
cat tv/build/intermediates/merged_res/debug/values/values.xml | grep app_name

# 检查AndroidManifest合并结果
cat tv/build/intermediates/merged_manifests/debug/AndroidManifest.xml | grep application
```

### 3. APK验证
```bash
# 使用aapt检查APK信息
aapt dump badging tv/build/outputs/apk/debug/onetv-*.apk | grep application-label
```

## 常见问题解决

### Q1: 资源前缀配置后仍有冲突
**解决方案**：
1. 检查是否有遗漏的资源文件
2. 确认resourcePrefix配置生效
3. 清理并重新构建

### Q2: 编译错误：找不到资源
**解决方案**：
1. 检查资源引用是否正确更新
2. 使用IDE的"Find Usages"功能查找所有引用
3. 确认XML文件中的引用也已更新

### Q3: AndroidManifest合并失败
**解决方案**：
1. 检查tools命名空间是否正确导入
2. 确认合并策略语法正确
3. 查看构建日志中的详细错误信息

---

**实施负责人**：开发团队
**预计完成时间**：2-3个工作日
**优先级**：高
**状态**：待实施
**文档版本**：v1.0
**最后更新**：2025-01-16
