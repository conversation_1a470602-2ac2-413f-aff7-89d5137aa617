<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MyTV">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.MyTV">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <!-- 暂时注释掉Supabase Auth重定向活动
        <activity
            onetv:name="io.github.jan.supabase.auth.redirects.DefaultRedirectActivity"
            onetv:exported="true">
           <intent-filter>
                <action onetv:name="onetv.intent.action.VIEW" />
                <category onetv:name="onetv.intent.category.DEFAULT" />
                <category onetv:name="onetv.intent.category.BROWSABLE" />
                <data onetv:scheme="io.onetv.app" onetv:host="auth-callback" />
            </intent-filter>
        </activity>
        -->
    </application>

</manifest>