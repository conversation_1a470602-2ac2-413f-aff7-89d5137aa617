/**
 * 第三方库模块: Thunder Download Engine (迅雷下载引擎)
 * 原始模块名: thunder
 * 库包名: com.xunlei.downloadlib
 * 功能说明: 迅雷下载工具类，提供文件处理和媒体类型判断功能
 *
 * 注意: 文件夹名为 xunlei 是为了符合 Java 包名规范
 * 实际对应的是 thunder 第三方库模块
 */
package com.xunlei.downloadlib;

import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.List;

public class Util {

    private static final List<String> VIDEO = Arrays.asList("avi", "flv", "mkv", "mov", "mp4", "mpeg", "mpe", "mpg",
            "wmv");
    private static final List<String> AUDIO = Arrays.asList("aac", "ape", "flac", "mp3", "m4a", "ogg");
    private static final String[] UNITS = new String[] { "bytes", "KB", "MB", "GB", "TB" };
    private static final long MINIMAL = 30 * 1024 * 1024;

    public static boolean isMedia(String ext, long size) {
        return (VIDEO.contains(ext) || AUDIO.contains(ext)) && size > MINIMAL;
    }

    public static String size(long size) {
        if (size <= 0)
            return "";
        int group = (int) (Math.log10(size) / Math.log10(1024));
        return "[" + new DecimalFormat("###0.#").format(size / Math.pow(1024, group)) + " " + UNITS[group] + "] ";
    }
}
