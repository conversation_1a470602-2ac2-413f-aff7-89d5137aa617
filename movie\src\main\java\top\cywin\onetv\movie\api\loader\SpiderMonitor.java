package top.cywin.onetv.movie.api.loader;

import android.content.Context;
import com.github.catvod.crawler.Spider;
import java.util.HashMap;
import java.util.List;

/**
 * Spider监控包装器
 * 用于监控Spider方法的执行情况，帮助诊断搜索失败的原因
 */
public class SpiderMonitor extends Spider {
    
    private final Spider wrappedSpider;
    private final String siteKey;
    
    public SpiderMonitor(Spider wrappedSpider, String siteKey) {
        this.wrappedSpider = wrappedSpider;
        this.siteKey = siteKey;
    }
    
    @Override
    public void init(Context context) throws Exception {
        android.util.Log.d("ONETV_SPIDER_MONITOR", "🚀 [" + siteKey + "] init(context) 开始");
        try {
            wrappedSpider.init(context);
            android.util.Log.d("ONETV_SPIDER_MONITOR", "✅ [" + siteKey + "] init(context) 成功");
        } catch (Exception e) {
            android.util.Log.e("ONETV_SPIDER_MONITOR", "❌ [" + siteKey + "] init(context) 失败", e);
            throw e;
        }
    }
    
    @Override
    public void init(Context context, String extend) throws Exception {
        android.util.Log.d("ONETV_SPIDER_MONITOR", "🚀 [" + siteKey + "] init(context, extend) 开始, extend长度: " + 
            (extend != null ? extend.length() : 0));
        try {
            wrappedSpider.init(context, extend);
            android.util.Log.d("ONETV_SPIDER_MONITOR", "✅ [" + siteKey + "] init(context, extend) 成功");
        } catch (Exception e) {
            android.util.Log.e("ONETV_SPIDER_MONITOR", "❌ [" + siteKey + "] init(context, extend) 失败", e);
            throw e;
        }
    }
    
    @Override
    public String homeContent(boolean filter) throws Exception {
        android.util.Log.d("ONETV_SPIDER_MONITOR", "🚀 [" + siteKey + "] homeContent(filter=" + filter + ") 开始");
        try {
            long startTime = System.currentTimeMillis();
            String result = wrappedSpider.homeContent(filter);
            long duration = System.currentTimeMillis() - startTime;
            android.util.Log.d("ONETV_SPIDER_MONITOR", "✅ [" + siteKey + "] homeContent() 成功, 耗时: " + duration + "ms, 结果长度: " + 
                (result != null ? result.length() : 0));
            return result;
        } catch (Exception e) {
            android.util.Log.e("ONETV_SPIDER_MONITOR", "❌ [" + siteKey + "] homeContent() 失败", e);
            throw e;
        }
    }
    
    @Override
    public String categoryContent(String tid, String pg, boolean filter, HashMap<String, String> extend) throws Exception {
        android.util.Log.d("ONETV_SPIDER_MONITOR", "🚀 [" + siteKey + "] categoryContent(tid=" + tid + ", pg=" + pg + 
            ", filter=" + filter + ") 开始");
        try {
            long startTime = System.currentTimeMillis();
            String result = wrappedSpider.categoryContent(tid, pg, filter, extend);
            long duration = System.currentTimeMillis() - startTime;
            android.util.Log.d("ONETV_SPIDER_MONITOR", "✅ [" + siteKey + "] categoryContent() 成功, 耗时: " + duration + "ms, 结果长度: " + 
                (result != null ? result.length() : 0));
            return result;
        } catch (Exception e) {
            android.util.Log.e("ONETV_SPIDER_MONITOR", "❌ [" + siteKey + "] categoryContent() 失败", e);
            throw e;
        }
    }
    
    @Override
    public String detailContent(List<String> ids) throws Exception {
        android.util.Log.d("ONETV_SPIDER_MONITOR", "🚀 [" + siteKey + "] detailContent(ids=" + 
            (ids != null ? ids.size() : 0) + ") 开始");
        try {
            long startTime = System.currentTimeMillis();
            String result = wrappedSpider.detailContent(ids);
            long duration = System.currentTimeMillis() - startTime;
            android.util.Log.d("ONETV_SPIDER_MONITOR", "✅ [" + siteKey + "] detailContent() 成功, 耗时: " + duration + "ms, 结果长度: " + 
                (result != null ? result.length() : 0));
            return result;
        } catch (Exception e) {
            android.util.Log.e("ONETV_SPIDER_MONITOR", "❌ [" + siteKey + "] detailContent() 失败", e);
            throw e;
        }
    }
    
    @Override
    public String searchContent(String key, boolean quick) throws Exception {
        android.util.Log.d("ONETV_SPIDER_MONITOR", "🔍 [" + siteKey + "] searchContent(key='" + key + "', quick=" + quick + ") 开始");
        try {
            long startTime = System.currentTimeMillis();
            String result = wrappedSpider.searchContent(key, quick);
            long duration = System.currentTimeMillis() - startTime;
            
            // 🔥 详细分析搜索结果
            if (result == null) {
                android.util.Log.w("ONETV_SPIDER_MONITOR", "⚠️ [" + siteKey + "] searchContent() 返回null, 耗时: " + duration + "ms");
            } else if (result.isEmpty()) {
                android.util.Log.w("ONETV_SPIDER_MONITOR", "⚠️ [" + siteKey + "] searchContent() 返回空字符串, 耗时: " + duration + "ms");
            } else if (result.trim().equals("{}")) {
                android.util.Log.w("ONETV_SPIDER_MONITOR", "⚠️ [" + siteKey + "] searchContent() 返回空JSON对象, 耗时: " + duration + "ms");
            } else {
                android.util.Log.d("ONETV_SPIDER_MONITOR", "✅ [" + siteKey + "] searchContent() 成功, 耗时: " + duration + "ms, 结果长度: " + result.length());
                // 显示结果的前100个字符用于调试
                String preview = result.length() > 100 ? result.substring(0, 100) + "..." : result;
                android.util.Log.d("ONETV_SPIDER_MONITOR", "📄 [" + siteKey + "] 搜索结果预览: " + preview);
            }
            
            return result;
        } catch (Exception e) {
            android.util.Log.e("ONETV_SPIDER_MONITOR", "❌ [" + siteKey + "] searchContent() 异常", e);
            throw e;
        }
    }
    
    @Override
    public String searchContent(String key, boolean quick, String pg) throws Exception {
        android.util.Log.d("ONETV_SPIDER_MONITOR", "🔍 [" + siteKey + "] searchContent(key='" + key + "', quick=" + quick + 
            ", pg='" + pg + "') 开始");
        try {
            long startTime = System.currentTimeMillis();
            String result = wrappedSpider.searchContent(key, quick, pg);
            long duration = System.currentTimeMillis() - startTime;
            
            // 🔥 详细分析搜索结果
            if (result == null) {
                android.util.Log.w("ONETV_SPIDER_MONITOR", "⚠️ [" + siteKey + "] searchContent(3参数) 返回null, 耗时: " + duration + "ms");
            } else if (result.isEmpty()) {
                android.util.Log.w("ONETV_SPIDER_MONITOR", "⚠️ [" + siteKey + "] searchContent(3参数) 返回空字符串, 耗时: " + duration + "ms");
            } else if (result.trim().equals("{}")) {
                android.util.Log.w("ONETV_SPIDER_MONITOR", "⚠️ [" + siteKey + "] searchContent(3参数) 返回空JSON对象, 耗时: " + duration + "ms");
            } else {
                android.util.Log.d("ONETV_SPIDER_MONITOR", "✅ [" + siteKey + "] searchContent(3参数) 成功, 耗时: " + duration + "ms, 结果长度: " + result.length());
                // 显示结果的前100个字符用于调试
                String preview = result.length() > 100 ? result.substring(0, 100) + "..." : result;
                android.util.Log.d("ONETV_SPIDER_MONITOR", "📄 [" + siteKey + "] 搜索结果预览: " + preview);
            }
            
            return result;
        } catch (Exception e) {
            android.util.Log.e("ONETV_SPIDER_MONITOR", "❌ [" + siteKey + "] searchContent(3参数) 异常", e);
            throw e;
        }
    }
    
    @Override
    public String playerContent(String flag, String id, List<String> vipFlags) throws Exception {
        android.util.Log.d("ONETV_SPIDER_MONITOR", "🚀 [" + siteKey + "] playerContent(flag=" + flag + ", id=" + id + ") 开始");
        try {
            long startTime = System.currentTimeMillis();
            String result = wrappedSpider.playerContent(flag, id, vipFlags);
            long duration = System.currentTimeMillis() - startTime;
            android.util.Log.d("ONETV_SPIDER_MONITOR", "✅ [" + siteKey + "] playerContent() 成功, 耗时: " + duration + "ms, 结果长度: " + 
                (result != null ? result.length() : 0));
            return result;
        } catch (Exception e) {
            android.util.Log.e("ONETV_SPIDER_MONITOR", "❌ [" + siteKey + "] playerContent() 失败", e);
            throw e;
        }
    }
    
    @Override
    public boolean isVideoFormat(String url) {
        try {
            boolean result = wrappedSpider.isVideoFormat(url);
            android.util.Log.d("ONETV_SPIDER_MONITOR", "🔍 [" + siteKey + "] isVideoFormat(url=" + url + ") = " + result);
            return result;
        } catch (Exception e) {
            android.util.Log.e("ONETV_SPIDER_MONITOR", "❌ [" + siteKey + "] isVideoFormat() 失败", e);
            return false;
        }
    }
    
    @Override
    public boolean manualVideoCheck() {
        try {
            boolean result = wrappedSpider.manualVideoCheck();
            android.util.Log.d("ONETV_SPIDER_MONITOR", "🔍 [" + siteKey + "] manualVideoCheck() = " + result);
            return result;
        } catch (Exception e) {
            android.util.Log.e("ONETV_SPIDER_MONITOR", "❌ [" + siteKey + "] manualVideoCheck() 失败", e);
            return false;
        }
    }
}
