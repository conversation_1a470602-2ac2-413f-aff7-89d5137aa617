package top.cywin.onetv.movie.event;

/**
 * 电影ID转换事件 - 实现原版VideoActivity的ID转换机制
 */
public class MovieIdTransformEvent {
    private final String originalVodId;
    private final String realVodId;
    private final String siteKey;
    private final String movieName;
    private final String error;
    private final boolean success;

    // 成功转换的构造函数
    public MovieIdTransformEvent(String originalVodId, String realVodId, String siteKey, String movieName) {
        this.originalVodId = originalVodId;
        this.realVodId = realVodId;
        this.siteKey = siteKey;
        this.movieName = movieName;
        this.error = null;
        this.success = true;
    }

    // 转换失败的构造函数
    public MovieIdTransformEvent(String originalVodId, String realVodId, String siteKey, String movieName, String error) {
        this.originalVodId = originalVodId;
        this.realVodId = realVodId;
        this.siteKey = siteKey;
        this.movieName = movieName;
        this.error = error;
        this.success = false;
    }

    public String getOriginalVodId() {
        return originalVodId;
    }

    public String getRealVodId() {
        return realVodId;
    }

    public String getSiteKey() {
        return siteKey;
    }

    public String getMovieName() {
        return movieName;
    }

    public String getError() {
        return error;
    }

    public boolean isSuccess() {
        return success;
    }
}
