package top.cywin.onetv.movie.api.config;

import android.text.TextUtils;
import android.util.Log;

import top.cywin.onetv.movie.App;
import top.cywin.onetv.movie.R;
import top.cywin.onetv.movie.api.Decoder;
import top.cywin.onetv.movie.api.loader.BaseLoader;
import top.cywin.onetv.movie.bean.Config;
import top.cywin.onetv.movie.bean.Depot;
import top.cywin.onetv.movie.bean.Parse;
import top.cywin.onetv.movie.bean.Rule;
import top.cywin.onetv.movie.bean.Site;
import top.cywin.onetv.movie.impl.Callback;
import top.cywin.onetv.movie.utils.Notify;
import top.cywin.onetv.movie.utils.UrlUtil;
import com.github.catvod.bean.Doh;
import com.github.catvod.net.OkHttp;
import com.github.catvod.utils.Json;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class VodConfig {

    private List<Doh> doh;
    private List<Rule> rules;
    private List<Site> sites;
    private List<Parse> parses;
    private List<String> flags;
    private List<String> ads;
    private boolean loadLive;
    private Config config;
    private Parse parse;
    private String wall;
    private Site home;
    private String lastLoadedUrl; // 缓存最后加载的URL

    private static class Loader {
        static volatile VodConfig INSTANCE = new VodConfig();
    }

    public static VodConfig get() {
        return Loader.INSTANCE;
    }

    public static int getCid() {
        Config config = get().getConfig();
        return config != null ? config.getId() : 0;
    }

    public static String getUrl() {
        Config config = get().getConfig();
        return config != null ? (config.getUrl() != null ? config.getUrl() : "") : "";
    }

    public static String getDesc() {
        Config config = get().getConfig();
        return config != null ? (config.getDesc() != null ? config.getDesc() : "") : "";
    }

    public static int getHomeIndex() {
        return get().getSites().indexOf(get().getHome());
    }

    public static boolean hasParse() {
        return !get().getParses().isEmpty();
    }

    public static void load(Config config, Callback callback) {
        try {
            android.util.Log.d("ONETV_VOD_CONFIG", "🎬 VodConfig.load开始");
            android.util.Log.d("ONETV_VOD_CONFIG", "📊 传入Config对象: " + (config != null ? "存在" : "null"));

            if (config != null) {
                android.util.Log.d("ONETV_VOD_CONFIG", "📊 Config详情 - URL: " + config.getUrl() + ", Name: "
                        + config.getName() + ", Type: " + config.getType());
            }

            android.util.Log.d("ONETV_VOD_CONFIG", "🔄 执行clear().config().load()链式调用");
            get().clear().config(config).load(callback);
            android.util.Log.d("ONETV_VOD_CONFIG", "✅ VodConfig.load完成");

        } catch (Exception e) {
            android.util.Log.e("ONETV_VOD_CONFIG", "❌ VodConfig.load异常", e);
            if (callback != null) {
                callback.error("VodConfig.load异常: " + e.getMessage());
            }
        }
    }

    public VodConfig init() {
        android.util.Log.d("ONETV_VOD_CONFIG", "🎬 [第8阶段] VodConfig初始化开始");
        android.util.Log.d("ONETV_VOD_CONFIG", "📍 位置: VodConfig.java:73");
        android.util.Log.d("ONETV_VOD_CONFIG", "⏰ 时间戳: " + System.currentTimeMillis());

        this.wall = null;
        this.home = null;
        this.parse = null;
        this.config = Config.vod();
        this.ads = new ArrayList<>();

        android.util.Log.d("ONETV_VOD_CONFIG", "✅ [第8阶段] VodConfig基础属性初始化完成");
        this.doh = new ArrayList<>();
        this.rules = new ArrayList<>();
        this.sites = new ArrayList<>();
        this.flags = new ArrayList<>();
        this.parses = new ArrayList<>();
        this.loadLive = false;
        return this;
    }

    public VodConfig config(Config config) {
        try {
            android.util.Log.d("ONETV_VOD_CONFIG", "🔧 设置VodConfig的config属性");
            android.util.Log.d("ONETV_VOD_CONFIG", "📊 设置前config: " + (this.config != null ? "存在" : "null"));
            android.util.Log.d("ONETV_VOD_CONFIG", "📊 新config: " + (config != null ? "存在" : "null"));

            this.config = config;

            android.util.Log.d("ONETV_VOD_CONFIG", "📊 设置后config: " + (this.config != null ? "存在" : "null"));
            android.util.Log.d("ONETV_VOD_CONFIG", "✅ config属性设置完成");

            return this;
        } catch (Exception e) {
            android.util.Log.e("ONETV_VOD_CONFIG", "❌ VodConfig.config异常", e);
            return this;
        }
    }

    public VodConfig clear() {
        this.wall = null;
        this.home = null;
        this.parse = null;
        this.ads.clear();
        this.doh.clear();
        this.rules.clear();
        this.sites.clear();
        this.flags.clear();
        this.parses.clear();
        this.loadLive = true;
        BaseLoader.get().clear();
        return this;
    }

    public void load(Callback callback) {
        App.execute(() -> loadConfig(callback));
    }

    private void loadConfig(Callback callback) {
        try {
            // ✅ 添加递归调用检测
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            long loadConfigCount = java.util.Arrays.stream(stackTrace)
                    .filter(element -> element.getMethodName().equals("loadConfig"))
                    .count();
            Log.d("ONETV_VOD_CONFIG", "🔄 loadConfig调用深度: " + loadConfigCount);

            if (loadConfigCount > 2) {
                Log.e("ONETV_VOD_CONFIG", "❌ 检测到可能的无限递归，停止加载");
                App.post(() -> callback.error("递归调用过深"));
                return;
            }

            // ✅ 检查config是否为null
            if (config == null) {
                Log.e("ONETV_VOD_CONFIG", "❌ 配置对象为null，无法加载");
                App.post(() -> callback.error("配置对象为null"));
                return;
            }

            String configUrl = config.getUrl();
            if (TextUtils.isEmpty(configUrl)) {
                Log.e("ONETV_VOD_CONFIG", "❌ 配置URL为空，无法加载");
                App.post(() -> callback.error("配置URL为空"));
                return;
            }

            // ✅ 添加缓存检查 - 如果配置已加载且站点不为空，直接返回
            if (configUrl.equals(lastLoadedUrl) && !sites.isEmpty()) {
                Log.d("ONETV_VOD_CONFIG", "✅ 配置已缓存，站点数: " + sites.size() + "，直接返回");
                App.post(() -> callback.success(""));
                return;
            }

            // 记录当前加载的URL
            lastLoadedUrl = configUrl;

            // ✅ 添加详细的网络请求日志
            Log.d("ONETV_VOD_CONFIG", "🌐 开始网络请求 (调用深度: " + loadConfigCount + ")");
            Log.d("ONETV_VOD_CONFIG", "📍 原始URL: " + configUrl);

            String convertedUrl = UrlUtil.convert(configUrl);
            Log.d("ONETV_VOD_CONFIG", "📍 转换后URL: " + convertedUrl);

            OkHttp.cancel("vod");
            Log.d("ONETV_VOD_CONFIG", "🔄 开始下载配置文件");

            String jsonContent = Decoder.getJson(convertedUrl, "vod");
            Log.d("ONETV_VOD_CONFIG", "📥 配置文件下载完成，长度: " + (jsonContent != null ? jsonContent.length() : 0));

            if (jsonContent == null || jsonContent.isEmpty()) {
                Log.e("ONETV_VOD_CONFIG", "❌ 配置文件内容为空");
                App.post(() -> callback.error("配置文件内容为空"));
                return;
            }

            // 显示配置文件的前200个字符用于调试
            String preview = jsonContent.length() > 200 ? jsonContent.substring(0, 200) + "..." : jsonContent;
            Log.d("ONETV_VOD_CONFIG", "📄 配置文件预览: " + preview);

            Log.d("ONETV_VOD_CONFIG", "🔧 开始解析JSON");
            JsonObject jsonObject = Json.parse(jsonContent).getAsJsonObject();
            Log.d("ONETV_VOD_CONFIG", "✅ JSON解析成功");

            checkJson(jsonObject, callback);
        } catch (Throwable e) {
            String configUrl = (config != null && config.getUrl() != null) ? config.getUrl() : "";
            Log.e("ONETV_VOD_CONFIG", "❌ loadConfig异常，URL: " + configUrl, e);
            if (TextUtils.isEmpty(configUrl))
                App.post(() -> callback.error(""));
            else
                loadCache(callback, e);
            e.printStackTrace();
        }
    }

    private void loadCache(Callback callback, Throwable e) {
        try {
            // ✅ 修复空指针异常 - 检查config是否为空
            if (config != null && !TextUtils.isEmpty(config.getJson())) {
                checkJson(Json.parse(config.getJson()).getAsJsonObject(), callback);
            } else {
                android.util.Log.w("ONETV_VOD_CONFIG", "⚠️ 缓存配置为空或config为null，返回错误");
                App.post(() -> callback.error(Notify.getError(R.string.vod_error_config_get, e)));
            }
        } catch (Exception ex) {
            android.util.Log.e("ONETV_VOD_CONFIG", "❌ loadCache异常", ex);
            App.post(() -> callback.error("缓存加载异常: " + ex.getMessage()));
        }
    }

    private void checkJson(JsonObject object, Callback callback) {
        try {
            Log.d("ONETV_VOD_CONFIG", "🔍 开始检查JSON结构");

            // 显示JSON的主要键
            Log.d("ONETV_VOD_CONFIG", "📊 JSON键列表: " + object.keySet().toString());

            if (object.has("msg")) {
                String msg = object.get("msg").getAsString();
                Log.e("ONETV_VOD_CONFIG", "❌ 配置文件包含错误消息: " + msg);
                App.post(() -> callback.error(msg));
            } else if (object.has("urls")) {
                Log.d("ONETV_VOD_CONFIG", "🏪 检测到多仓库配置，开始解析仓库列表");
                parseDepot(object, callback);
            } else {
                Log.d("ONETV_VOD_CONFIG", "📺 检测到单仓库配置，开始解析站点配置");
                parseConfig(object, callback);
            }
        } catch (Exception e) {
            Log.e("ONETV_VOD_CONFIG", "❌ checkJson异常", e);
            App.post(() -> callback.error("JSON检查异常: " + e.getMessage()));
        }
    }

    private void parseDepot(JsonObject object, Callback callback) {
        try {
            Log.d("ONETV_VOD_CONFIG", "🏪 开始解析多仓库配置");

            // 获取urls数组
            if (!object.has("urls")) {
                Log.e("ONETV_VOD_CONFIG", "❌ 配置文件缺少urls字段");
                App.post(() -> callback.error("配置文件缺少urls字段"));
                return;
            }

            String urlsJson = object.getAsJsonArray("urls").toString();
            Log.d("ONETV_VOD_CONFIG", "📋 仓库列表JSON: " + urlsJson);

            List<Depot> items = Depot.arrayFrom(urlsJson);
            Log.d("ONETV_VOD_CONFIG", "📊 解析到仓库数量: " + items.size());

            // 显示每个仓库的信息
            for (int i = 0; i < items.size(); i++) {
                Depot depot = items.get(i);
                Log.d("ONETV_VOD_CONFIG", "📦 仓库" + (i + 1) + " - 名称: " + depot.getName() + ", URL: " + depot.getUrl());
            }

            List<Config> configs = new ArrayList<>();
            for (Depot item : items) {
                Config depotConfig = Config.find(item, 0);
                if (depotConfig != null) {
                    configs.add(depotConfig);
                    Log.d("ONETV_VOD_CONFIG", "✅ 仓库配置创建成功: " + item.getName());
                } else {
                    Log.w("ONETV_VOD_CONFIG", "⚠️ 仓库配置创建失败: " + item.getName());
                }
            }

            // ✅ 修复空指针异常 - 检查config和configs是否为空
            if (config != null && config.getUrl() != null) {
                Log.d("ONETV_VOD_CONFIG", "🗑️ 删除旧配置: " + config.getUrl());
                Config.delete(config.getUrl());
            }

            if (configs.isEmpty()) {
                Log.e("ONETV_VOD_CONFIG", "❌ 配置列表为空，无法解析");
                App.post(() -> callback.error("配置列表为空"));
                return;
            }

            config = configs.get(0);
            if (config == null) {
                Log.e("ONETV_VOD_CONFIG", "❌ 配置对象为null，无法解析");
                App.post(() -> callback.error("配置对象为null"));
                return;
            }

            Log.d("ONETV_VOD_CONFIG", "🎯 选择第一个仓库进行加载: " + config.getName());
            Log.d("ONETV_VOD_CONFIG", "📍 仓库URL: " + config.getUrl());
            Log.d("ONETV_VOD_CONFIG", "🔄 开始递归调用loadConfig加载选中的仓库");

            // ✅ 递归调用loadConfig来加载选中的仓库配置
            loadConfig(callback);
        } catch (Exception e) {
            Log.e("ONETV_VOD_CONFIG", "❌ parseDepot异常", e);
            App.post(() -> callback.error("配置解析异常: " + e.getMessage()));
        }
    }

    private void parseConfig(JsonObject object, Callback callback) {
        try {
            Log.d("ONETV_VOD_CONFIG", "🔧 开始解析站点配置");
            Log.d("ONETV_VOD_CONFIG", "📊 Config对象状态: " + (config != null ? "存在" : "null"));

            // ✅ 检查config对象
            if (config == null) {
                Log.e("ONETV_VOD_CONFIG", "❌ Config对象为null，无法解析配置");
                App.post(() -> callback.error("Config对象为null"));
                return;
            }

            Log.d("ONETV_VOD_CONFIG", "🏗️ 开始初始化站点");
            initSite(object);
            Log.d("ONETV_VOD_CONFIG", "✅ 站点初始化完成，站点数量: " + sites.size());

            Log.d("ONETV_VOD_CONFIG", "🏗️ 开始初始化解析器");
            initParse(object);
            Log.d("ONETV_VOD_CONFIG", "✅ 解析器初始化完成，解析器数量: " + parses.size());

            Log.d("ONETV_VOD_CONFIG", "🏗️ 开始初始化其他配置");
            initOther(object);
            Log.d("ONETV_VOD_CONFIG", "✅ 其他配置初始化完成");

            if (loadLive && object.has("lives")) {
                Log.d("ONETV_VOD_CONFIG", "🏗️ 开始初始化直播配置");
                initLive(object);
                Log.d("ONETV_VOD_CONFIG", "✅ 直播配置初始化完成");
            }

            String notice = Json.safeString(object, "notice");
            String logo = Json.safeString(object, "logo");

            Log.d("ONETV_VOD_CONFIG", "🏗️ 设置配置属性");
            Log.d("ONETV_VOD_CONFIG", "📊 Notice: " + notice);
            Log.d("ONETV_VOD_CONFIG", "📊 Logo: " + logo);

            config.logo(logo);
            config.json(object.toString()).update();

            Log.d("ONETV_VOD_CONFIG", "✅ 配置解析完成");
            Log.d("ONETV_VOD_CONFIG", "📊 最终统计 - 站点: " + sites.size() + "个, 解析器: " + parses.size() + "个");

            // ✅ 添加回调执行日志
            Log.d("ONETV_VOD_CONFIG", "🔄 准备调用success回调");
            Log.d("ONETV_VOD_CONFIG", "📊 回调对象: " + (callback != null ? "存在" : "null"));
            Log.d("ONETV_VOD_CONFIG", "📊 Notice内容: " + (notice != null ? notice : "null"));

            // ✅ 只调用一次success回调
            Log.d("ONETV_VOD_CONFIG", "🔄 当前线程: " + Thread.currentThread().getName());
            Log.d("ONETV_VOD_CONFIG", "🔄 准备通过App.post调用success回调");

            App.post(() -> {
                try {
                    Log.d("ONETV_VOD_CONFIG", "🚀 App.post回调开始执行");
                    Log.d("ONETV_VOD_CONFIG", "🔄 回调执行线程: " + Thread.currentThread().getName());
                    Log.d("ONETV_VOD_CONFIG", "📊 即将调用callback.success()");

                    callback.success(notice);

                    Log.d("ONETV_VOD_CONFIG", "✅ callback.success()调用完成");
                    Log.d("ONETV_VOD_CONFIG", "✅ success回调执行完成");
                } catch (Exception e) {
                    Log.e("ONETV_VOD_CONFIG", "❌ success回调执行异常", e);
                    e.printStackTrace();
                }
            });

            Log.d("ONETV_VOD_CONFIG", "✅ App.post调用完成，等待回调执行");
        } catch (Throwable e) {
            Log.e("ONETV_VOD_CONFIG", "❌ parseConfig异常", e);
            e.printStackTrace();
            App.post(() -> callback.error(Notify.getError(R.string.vod_error_config_parse, e)));
        }
    }

    private void initSite(JsonObject object) {
        try {
            Log.d("ONETV_VOD_CONFIG", "🏗️ 开始初始化站点配置");

            if (object.has("video")) {
                Log.d("ONETV_VOD_CONFIG", "📺 检测到video节点，递归处理");
                initSite(object.getAsJsonObject("video"));
                return;
            }

            String spider = Json.safeString(object, "spider");
            Log.d("ONETV_VOD_CONFIG", "🕷️ 配置文件Spider JAR URL: " + spider);

            // 🔧 修复：优先使用配置文件中的JAR路径，如果为空则使用本地默认JAR
            if (TextUtils.isEmpty(spider)) {
                spider = "assets://jar/spider.jar"; // 使用实际存在的JAR文件
                Log.d("ONETV_VOD_CONFIG", "🔧 配置文件JAR为空，使用本地默认Spider JAR: " + spider);
            } else {
                Log.d("ONETV_VOD_CONFIG", "✅ 使用配置文件中的Spider JAR: " + spider);
            }

            if (!TextUtils.isEmpty(spider)) {
                Log.d("ONETV_VOD_CONFIG", "🔄 开始解析Spider JAR: " + spider);
                BaseLoader.get().parseJar(spider, true);
                Log.d("ONETV_VOD_CONFIG", "✅ Spider JAR解析完成");
            } else {
                Log.w("ONETV_VOD_CONFIG", "⚠️ Spider JAR URL为空");
            }

            Log.d("ONETV_VOD_CONFIG", "🔄 开始解析站点列表");
            int siteCount = 0;
            for (JsonElement element : Json.safeListElement(object, "sites")) {
                try {
                    Site site = Site.objectFrom(element);
                    if (sites.contains(site)) {
                        Log.d("ONETV_VOD_CONFIG", "⚠️ 站点已存在，跳过: " + site.getName());
                        continue;
                    }

                    site.setApi(UrlUtil.convert(site.getApi()));
                    site.setExt(UrlUtil.convert(site.getExt()));
                    site.setJar(parseJar(site, spider));
                    sites.add(site.trans().sync());
                    siteCount++;

                    Log.d("ONETV_VOD_CONFIG", "✅ 站点添加成功: " + site.getName() + " (" + site.getKey() + ")");
                } catch (Exception e) {
                    Log.w("ONETV_VOD_CONFIG", "⚠️ 站点解析失败", e);
                }
            }
            Log.d("ONETV_VOD_CONFIG", "📊 站点解析完成，成功添加: " + siteCount + " 个站点");

            // ✅ 检查config对象再设置home
            if (config != null) {
                Log.d("ONETV_VOD_CONFIG", "🔄 开始设置默认站点");
                String homeKey = config.getHome();
                Log.d("ONETV_VOD_CONFIG", "🏠 默认站点Key: " + homeKey);

                for (Site site : sites) {
                    if (site.getKey().equals(homeKey)) {
                        setHome(site);
                        Log.d("ONETV_VOD_CONFIG", "✅ 默认站点设置成功: " + site.getName());
                        break;
                    }
                }
            } else {
                Log.w("ONETV_VOD_CONFIG", "⚠️ Config对象为null，跳过默认站点设置");
            }

            Log.d("ONETV_VOD_CONFIG", "✅ 站点初始化完成，总站点数: " + sites.size());
        } catch (Exception e) {
            Log.e("ONETV_VOD_CONFIG", "❌ initSite异常", e);
        }
    }

    private void initLive(JsonObject object) {
        try {
            // ✅ 修复空指针异常 - 检查config是否为空
            if (config == null) {
                android.util.Log.w("ONETV_VOD_CONFIG", "⚠️ config为null，跳过Live配置初始化");
                return;
            }

            Config temp = Config.find(config, 1).save();
            String configUrl = config.getUrl();
            if (configUrl == null) {
                android.util.Log.w("ONETV_VOD_CONFIG", "⚠️ configUrl为null，跳过Live配置同步");
                return;
            }

            boolean sync = LiveConfig.get().needSync(configUrl);
            if (sync)
                LiveConfig.get().clear().config(temp).parse(object);
        } catch (Exception e) {
            android.util.Log.e("ONETV_VOD_CONFIG", "❌ initLive异常", e);
        }
    }

    private void initParse(JsonObject object) {
        try {
            Log.d("ONETV_VOD_CONFIG", "🔧 开始初始化解析器配置");

            int parseCount = 0;
            for (JsonElement element : Json.safeListElement(object, "parses")) {
                try {
                    Parse parse = Parse.objectFrom(element);
                    if (parse == null) {
                        Log.w("ONETV_VOD_CONFIG", "⚠️ 解析器对象为null，跳过");
                        continue;
                    }

                    Log.d("ONETV_VOD_CONFIG", "🔧 处理解析器: " + parse.getName() + " (类型: " + parse.getType() + ")");

                    // ✅ 修复空指针异常 - 检查config和parse是否为空
                    if (config != null && parse.getName() != null && config.getParse() != null) {
                        if (parse.getName().equals(config.getParse()) && parse.getType() > 1) {
                            setParse(parse);
                            Log.d("ONETV_VOD_CONFIG", "✅ 设置默认解析器: " + parse.getName());
                        }
                    }

                    if (!parses.contains(parse)) {
                        parses.add(parse);
                        parseCount++;
                        Log.d("ONETV_VOD_CONFIG", "✅ 解析器添加成功: " + parse.getName());
                    } else {
                        Log.d("ONETV_VOD_CONFIG", "⚠️ 解析器已存在，跳过: " + parse.getName());
                    }
                } catch (Exception e) {
                    Log.w("ONETV_VOD_CONFIG", "⚠️ 单个解析器处理失败", e);
                }
            }

            Log.d("ONETV_VOD_CONFIG", "📊 解析器初始化完成，成功添加: " + parseCount + " 个解析器");
            Log.d("ONETV_VOD_CONFIG", "📊 总解析器数量: " + parses.size());
        } catch (Exception e) {
            Log.e("ONETV_VOD_CONFIG", "❌ initParse异常", e);
        }
    }

    private void initOther(JsonObject object) {
        try {
            Log.d("ONETV_VOD_CONFIG", "🔧 开始初始化其他配置");

            // 添加God解析器
            if (!parses.isEmpty()) {
                parses.add(0, Parse.god());
                Log.d("ONETV_VOD_CONFIG", "✅ 添加God解析器");
            }

            // 设置默认站点
            if (home == null) {
                Site defaultSite = sites.isEmpty() ? new Site() : sites.get(0);
                setHome(defaultSite);
                Log.d("ONETV_VOD_CONFIG",
                        "✅ 设置默认站点: " + (defaultSite.getName() != null ? defaultSite.getName() : "空站点"));
            }

            // 设置默认解析器
            if (parse == null) {
                Parse defaultParse = parses.isEmpty() ? new Parse() : parses.get(0);
                setParse(defaultParse);
                Log.d("ONETV_VOD_CONFIG",
                        "✅ 设置默认解析器: " + (defaultParse.getName() != null ? defaultParse.getName() : "空解析器"));
            }

            // 初始化各种配置
            Log.d("ONETV_VOD_CONFIG", "🔧 初始化规则配置");
            setRules(Rule.arrayFrom(object.getAsJsonArray("rules")));

            Log.d("ONETV_VOD_CONFIG", "🔧 初始化DNS配置");
            setDoh(Doh.arrayFrom(object.getAsJsonArray("doh")));

            Log.d("ONETV_VOD_CONFIG", "🔧 初始化Headers配置");
            setHeaders(Json.safeListElement(object, "headers"));

            Log.d("ONETV_VOD_CONFIG", "🔧 初始化Flags配置");
            setFlags(Json.safeListString(object, "flags"));

            Log.d("ONETV_VOD_CONFIG", "🔧 初始化Hosts配置");
            setHosts(Json.safeListString(object, "hosts"));

            Log.d("ONETV_VOD_CONFIG", "🔧 初始化Proxy配置");
            setProxy(Json.safeListString(object, "proxy"));

            String wallpaper = Json.safeString(object, "wallpaper");
            setWall(wallpaper);
            Log.d("ONETV_VOD_CONFIG", "🔧 设置壁纸: " + wallpaper);

            Log.d("ONETV_VOD_CONFIG", "🔧 初始化广告配置");
            setAds(Json.safeListString(object, "ads"));

            Log.d("ONETV_VOD_CONFIG", "✅ 其他配置初始化完成");
        } catch (Exception e) {
            Log.e("ONETV_VOD_CONFIG", "❌ initOther异常", e);
        }
    }

    private String parseJar(Site site, String spider) {
        String siteJar = site.getJar();
        if (siteJar.isEmpty()) {
            // 站点没有特定JAR，使用全局Spider JAR
            return spider;
        } else {
            // 站点有特定JAR，检查是否需要优先使用本地版本
            Log.d("ONETV_VOD_CONFIG", "📊 站点 " + site.getName() + " 使用特定JAR: " + siteJar);
            return siteJar;
        }
    }

    public List<Doh> getDoh() {
        List<Doh> items = Doh.get(App.get());
        if (doh == null)
            return items;
        items.removeAll(doh);
        items.addAll(doh);
        return items;
    }

    public void setDoh(List<Doh> doh) {
        this.doh = doh;
    }

    public List<Rule> getRules() {
        return rules == null ? Collections.emptyList() : rules;
    }

    public void setRules(List<Rule> rules) {
        this.rules = rules;
    }

    public List<Site> getSites() {
        return sites == null ? Collections.emptyList() : sites;
    }

    public List<Parse> getParses() {
        return parses == null ? Collections.emptyList() : parses;
    }

    public List<Parse> getParses(int type) {
        List<Parse> items = new ArrayList<>();
        for (Parse item : getParses())
            if (item.getType() == type)
                items.add(item);
        return items;
    }

    public List<Parse> getParses(int type, String flag) {
        List<Parse> items = new ArrayList<>();
        for (Parse item : getParses(type))
            if (item.getExt().getFlag().contains(flag))
                items.add(item);
        if (items.isEmpty())
            items.addAll(getParses(type));
        return items;
    }

    public void setHeaders(List<JsonElement> items) {
        OkHttp.responseInterceptor().setHeaders(items);
    }

    public List<String> getFlags() {
        return flags == null ? Collections.emptyList() : flags;
    }

    private void setFlags(List<String> flags) {
        this.flags.addAll(flags);
    }

    public void setHosts(List<String> hosts) {
        OkHttp.dns().addAll(hosts);
    }

    public void setProxy(List<String> hosts) {
        OkHttp.selector().addAll(hosts);
    }

    public List<String> getAds() {
        return ads == null ? Collections.emptyList() : ads;
    }

    private void setAds(List<String> ads) {
        this.ads = ads;
    }

    public Config getConfig() {
        if (config != null) {
            return config;
        }

        // ✅ 获取数据库中的配置
        Config vodConfig = Config.vod();
        if (vodConfig != null && !vodConfig.isEmpty()) {
            return vodConfig;
        }

        // ✅ 如果没有配置，创建一个默认配置
        Log.w("ONETV_VOD_CONFIG", "⚠️ 没有找到有效的VOD配置，创建默认配置");
        Config defaultConfig = Config.create(0);
        defaultConfig.setUrl(""); // 设置空URL，避免null
        defaultConfig.setName("默认配置");
        return defaultConfig;
    }

    public Parse getParse() {
        return parse == null ? new Parse() : parse;
    }

    public Site getHome() {
        return home == null ? new Site() : home;
    }

    public String getWall() {
        return TextUtils.isEmpty(wall) ? "" : wall;
    }

    public Parse getParse(String name) {
        int index = getParses().indexOf(Parse.get(name));
        return index == -1 ? null : getParses().get(index);
    }

    public Site getSite(String key) {
        int index = getSites().indexOf(Site.get(key));
        return index == -1 ? new Site() : getSites().get(index);
    }

    public void setParse(Parse parse) {
        this.parse = parse;
        this.parse.setActivated(true);
        config.parse(parse.getName()).save();
        for (Parse item : getParses())
            item.setActivated(parse);
    }

    public void setHome(Site home) {
        this.home = home;
        this.home.setActivated(true);
        config.home(home.getKey()).save();
        for (Site item : getSites())
            item.setActivated(home);
    }

    private void setWall(String wall) {
        this.wall = wall;
        boolean load = !TextUtils.isEmpty(wall) && WallConfig.get().needSync(wall);
        if (load)
            WallConfig.get().config(Config.find(wall, config.getName(), 2).update());
    }
}
