package top.cywin.onetv.movie.adapter;

import android.util.Log;
import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.ArrayList;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import androidx.navigation.NavController;
import top.cywin.onetv.movie.api.config.VodConfig;
import top.cywin.onetv.movie.model.SiteViewModel;
import top.cywin.onetv.movie.navigation.MovieRoutes;
// 导入原版FongMi_TV事件类
import top.cywin.onetv.movie.event.ErrorEvent;
import top.cywin.onetv.movie.event.MovieIdTransformEvent;

// 导入UIEvents.kt中的Kotlin事件类
import top.cywin.onetv.movie.event.ConfigUpdateEvent;
import top.cywin.onetv.movie.event.ContentDetailEvent;
import top.cywin.onetv.movie.event.SearchResultEvent;
import top.cywin.onetv.movie.event.SearchStartEvent;
import top.cywin.onetv.movie.event.SearchErrorEvent;
import top.cywin.onetv.movie.event.PlayUrlParseStartEvent;
import top.cywin.onetv.movie.event.PlayUrlParseErrorEvent;
import top.cywin.onetv.movie.event.FavoriteUpdateEvent;
import top.cywin.onetv.movie.event.FavoriteListEvent;
import top.cywin.onetv.movie.event.HistoryUpdateEvent;
import top.cywin.onetv.movie.event.HistoryListEvent;
import top.cywin.onetv.movie.event.SiteListEvent;
import top.cywin.onetv.movie.event.SiteChangeEvent;
import top.cywin.onetv.movie.event.CloudDriveEvent;
import top.cywin.onetv.movie.event.LiveChannelEvent;
import top.cywin.onetv.movie.event.NavigationEvent;
import top.cywin.onetv.movie.event.NavigateToSearchResultsEvent;
import top.cywin.onetv.movie.event.LivePlayEvent;
import top.cywin.onetv.movie.event.SettingsUpdateEvent;
import top.cywin.onetv.movie.event.ApiTestEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Repository适配器 - 完整的FongMi_TV接口适配器
 * 提供Compose UI与FongMi_TV系统之间的完整接口适配
 * 
 * 🎯 重要架构原则：
 * - ✅ 适配器只做调用转发 - 不实现业务逻辑
 * - ✅ 所有解析逻辑在FongMi_TV - 保持原有解析系统
 * - ✅ 所有业务逻辑在FongMi_TV - Keep、History、VodConfig等
 * - ❌ 适配器不重新实现功能 - 避免代码重复
 */
public class RepositoryAdapter {

    private static final String TAG = "RepositoryAdapter";

    // ✅ 添加SiteViewModel实例 - 使用MovieApp的单例实例
    private top.cywin.onetv.movie.model.SiteViewModel siteViewModel;

    // 🔥 原版FongMi_TV直接导航支持
    private NavController navController;

    // 🔥 关键修复：防止重复导航的状态管理
    private String lastNavigatedVodId = null;
    private String lastNavigatedSiteKey = null;
    private long lastNavigationTime = 0;
    private static final long NAVIGATION_COOLDOWN = 1000; // 1秒冷却时间

    // ✅ 区分搜索来源的标志
    private boolean isFromRecommendClick = false; // true=推荐电影点击，false=用户主动搜索

    // 🔧 修复搜索结果累加问题的标志
    private boolean hasNavigatedToSearchResults = false;
    private String currentSearchKeyword = null;

    public RepositoryAdapter() {
        // 🔥 延迟初始化SiteViewModel，避免循环依赖
        // 在第一次使用时再获取MovieApp实例
        this.siteViewModel = null;
        Log.d(TAG, "🔥 [播放地址解析修复] RepositoryAdapter构造完成，SiteViewModel将延迟初始化");

        // 🔧 修复：重新注册EventBus，现在有占位监听方法
        EventBus.getDefault().register(this);

        Log.d(TAG, "🏗️ RepositoryAdapter 初始化完成，EventBus已注册");
    }

    /**
     * 延迟初始化SiteViewModel，避免循环依赖
     */
    private void ensureSiteViewModelInitialized() {
        if (siteViewModel == null) {
            try {
                top.cywin.onetv.movie.MovieApp movieApp = top.cywin.onetv.movie.MovieApp.Companion.getInstance();
                this.siteViewModel = movieApp.getSiteViewModel();
                Log.d(TAG, "🔥 [播放地址解析修复] 延迟初始化SiteViewModel: " + (siteViewModel != null ? "成功" : "失败"));
            } catch (Exception e) {
                Log.e(TAG, "❌ [播放地址解析] 延迟初始化SiteViewModel失败: " + e.getMessage());
            }
        }
    }

    /**
     * 🔥 原版FongMi_TV直接导航支持：设置NavController
     */
    public void setNavController(NavController navController) {
        this.navController = navController;
        Log.d(TAG, "🔥 [原版直接导航] NavController已设置: " + (navController != null ? "成功" : "null"));
    }

    /**
     * 销毁适配器，取消EventBus注册
     */
    public void destroy() {
        try {
            EventBus.getDefault().unregister(this);
            Log.d(TAG, "🏗️ RepositoryAdapter 已销毁，EventBus已取消注册");
        } catch (Exception e) {
            Log.w(TAG, "⚠️ EventBus取消注册失败", e);
        }
    }

    // ===== 配置管理接口 =====

    /**
     * 加载配置文件
     */
    public void loadConfig() {
        Log.d(TAG, "🔄 [RepositoryAdapter] 开始加载配置文件");
        try {
            // ✅ 检查VodConfig实例
            top.cywin.onetv.movie.api.config.VodConfig vodConfig = VodConfig.get();
            Log.d(TAG, "🔄 [RepositoryAdapter] VodConfig实例: " + (vodConfig != null ? "存在" : "null"));

            // ✅ 检查Config对象
            top.cywin.onetv.movie.bean.Config config = vodConfig.getConfig();
            Log.d(TAG, "🔄 [RepositoryAdapter] Config对象: " + (config != null ? "存在" : "null"));

            // ✅ 检查是否有有效的配置URL
            String configUrl = VodConfig.getUrl();
            Log.d(TAG, "🔄 [RepositoryAdapter] 配置URL: " + (configUrl != null ? configUrl : "null"));

            // ✅ 添加缓存检查 - 如果配置已存在且站点不为空，直接返回成功
            if (config != null && !configUrl.isEmpty() && !vodConfig.getSites().isEmpty()) {
                Log.d(TAG, "✅ [RepositoryAdapter] 配置已缓存，站点数: " + vodConfig.getSites().size() + "，直接返回");
                EventBus.getDefault().post(new ConfigUpdateEvent(vodConfig, true, null));
                return;
            }

            // ✅ 修复配置URL检查逻辑 - 如果没有URL，部署默认配置
            if (configUrl == null || configUrl.isEmpty()) {
                Log.w(TAG, "⚠️ [RepositoryAdapter] 没有配置URL，部署默认OneTV官方接口");
                Log.d(TAG, "🚀 [RepositoryAdapter] 开始部署OneTV官方影视接口");

                // 部署默认的OneTV官方接口
                top.cywin.onetv.movie.config.VodConfigDeployer.deployOnetvApiConfig(null,
                        new top.cywin.onetv.movie.impl.Callback() {
                            @Override
                            public void success() {
                                Log.d(TAG, "✅ [RepositoryAdapter] OneTV官方接口部署成功(无参数)");
                                EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), true, null));
                            }

                            @Override
                            public void success(String result) {
                                Log.d(TAG, "✅ [RepositoryAdapter] OneTV官方接口部署成功(有参数): " + result);
                                EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), true, null));
                            }

                            @Override
                            public void error(String msg) {
                                Log.e(TAG, "❌ [RepositoryAdapter] OneTV官方接口部署失败: " + msg);
                                Log.d(TAG, "📡 [RepositoryAdapter] 发送WELCOME_SCREEN事件");
                                EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, msg));
                            }
                        });
                return;
            }

            Log.d(TAG, "🔄 使用配置URL: " + configUrl);

            // ✅ 创建Config对象
            top.cywin.onetv.movie.bean.Config newConfig = top.cywin.onetv.movie.bean.Config.find(configUrl, 0);
            Log.d(TAG, "🏗️ 创建Config对象: " + (newConfig != null ? "成功" : "失败"));

            if (newConfig != null) {
                Log.d(TAG, "📊 Config详情 - URL: " + newConfig.getUrl() + ", Name: " + newConfig.getName());
            }

            // ✅ 使用正确的VodConfig.load方法
            Log.d(TAG, "🔄 [RepositoryAdapter] 创建Callback对象");
            VodConfig.load(newConfig, new top.cywin.onetv.movie.impl.Callback() {
                @Override
                public void success() {
                    Log.d(TAG, "🎉 [RepositoryAdapter] SUCCESS()回调被调用！");
                    handleSuccess("无参数success回调");
                }

                @Override
                public void success(String result) {
                    Log.d(TAG, "🎉 [RepositoryAdapter] SUCCESS(String)回调被调用！");
                    Log.d(TAG, "📊 回调结果: " + (result != null ? result : "null"));
                    handleSuccess(result);
                }

                private void handleSuccess(String notice) {
                    Log.d(TAG, "✅ 配置文件加载成功");
                    Log.d(TAG, "📊 当前站点数量: " + VodConfig.get().getSites().size());
                    Log.d(TAG, "📊 当前解析器数量: " + VodConfig.get().getParses().size());
                    Log.d(TAG, "🚀 发送ConfigUpdateEvent(success=true)");
                    EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), true, null));
                    Log.d(TAG, "✅ ConfigUpdateEvent发送完成");
                }

                @Override
                public void error(String msg) {
                    Log.e(TAG, "💥 [RepositoryAdapter] ERROR回调被调用！");
                    Log.e(TAG, "❌ 配置文件加载失败: " + msg);
                    // ✅ 如果加载失败，显示欢迎界面
                    Log.w(TAG, "⚠️ 配置加载失败，显示欢迎界面");
                    Log.d(TAG, "🚀 发送ConfigUpdateEvent(success=false)");
                    EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, msg));
                    Log.d(TAG, "✅ ConfigUpdateEvent发送完成");
                }
            });
            Log.d(TAG, "✅ [RepositoryAdapter] VodConfig.load调用完成，等待回调");
        } catch (Exception e) {
            Log.e(TAG, "❌ 配置文件加载失败", e);
            // ✅ 如果出现异常，显示欢迎界面
            Log.w(TAG, "⚠️ 配置加载异常，显示欢迎界面");
            EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, e.getMessage()));
        }
    }

    /**
     * 解析配置URL
     */
    public void parseConfig(String configUrl) {
        Log.d(TAG, "🔄 解析配置URL: " + configUrl);
        try {
            // ✅ 创建Config对象并加载
            top.cywin.onetv.movie.bean.Config config = top.cywin.onetv.movie.bean.Config.find(configUrl, 0);
            VodConfig.load(config, new top.cywin.onetv.movie.impl.Callback() {
                @Override
                public void success() {
                    Log.d(TAG, "✅ 配置URL解析完成(无参数)");
                    EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), true, null));
                }

                @Override
                public void success(String result) {
                    Log.d(TAG, "✅ 配置URL解析完成(有参数): " + result);
                    EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), true, null));
                }

                @Override
                public void error(String msg) {
                    Log.e(TAG, "❌ 配置URL解析失败: " + msg);
                    EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, msg));
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "❌ 配置URL解析失败", e);
            EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, e.getMessage()));
        }
    }

    /**
     * 刷新配置
     */
    public void refreshConfig() {
        Log.d(TAG, "🔄 刷新配置");
        try {
            VodConfig vodConfig = VodConfig.get();
            vodConfig.clear();
            vodConfig.load(new top.cywin.onetv.movie.impl.Callback() {
                @Override
                public void success() {
                    Log.d(TAG, "✅ 配置刷新完成(无参数)");
                    EventBus.getDefault().post(new ConfigUpdateEvent(vodConfig, true, null));
                }

                @Override
                public void success(String result) {
                    Log.d(TAG, "✅ 配置刷新完成(有参数): " + result);
                    EventBus.getDefault().post(new ConfigUpdateEvent(vodConfig, true, null));
                }

                @Override
                public void error(String msg) {
                    Log.e(TAG, "❌ 配置刷新失败: " + msg);
                    EventBus.getDefault().post(new ConfigUpdateEvent(vodConfig, false, msg));
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "❌ 配置刷新失败", e);
            EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, e.getMessage()));
        }
    }

    /**
     * 重连Repository
     */
    public void reconnectRepositories() {
        Log.d(TAG, "🔄 重连Repository");
        try {
            VodConfig.get().init();
            Log.d(TAG, "✅ Repository重连成功");
        } catch (Exception e) {
            Log.e(TAG, "❌ Repository重连失败", e);
        }
    }

    // ===== 内容获取接口 =====

    /**
     * 获取首页内容
     */
    public void getHomeContent() {
        Log.d(TAG, "🔄 获取首页内容");
        try {
            siteViewModel.homeContent();
            Log.d(TAG, "✅ 首页内容请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 首页内容获取失败", e);
            EventBus.getDefault().post(new ErrorEvent("首页内容获取失败: " + e.getMessage(), ErrorEvent.Type.NETWORK, null));
        }
    }

    /**
     * 获取分类列表
     */
    public void getCategories() {
        Log.d(TAG, "🔄 获取分类列表");
        try {
            siteViewModel.homeContent();
            Log.d(TAG, "✅ 分类列表请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 分类列表获取失败", e);
            EventBus.getDefault().post(new ErrorEvent("分类列表获取失败: " + e.getMessage(), ErrorEvent.Type.NETWORK, null));
        }
    }

    /**
     * 获取分类内容
     */
    public void getContentList(String typeId, int page, Map<String, String> filters) {
        Log.d(TAG, "🔄 获取内容列表: typeId=" + typeId + ", page=" + page);
        try {
            siteViewModel.categoryContent(typeId, page, true, filters);
            Log.d(TAG, "✅ 内容列表请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 内容列表获取失败", e);
            EventBus.getDefault().post(new ErrorEvent("内容列表获取失败: " + e.getMessage(), ErrorEvent.Type.NETWORK, null));
        }
    }

    /**
     * 电影点击处理 - 完全按照原版FongMi_TV的VideoActivity.checkId()分类处理逻辑
     */
    public void handleMovieClick(String vodId, String movieName, String siteKey) {
        Log.d(TAG, "🎬 [FongMi_TV兼容] 处理电影点击: vodId=" + vodId + ", movieName=" + movieName + ", siteKey=" + siteKey);

        // 🔥 关键：这不是ID转换，而是ID分类处理
        // 原版FongMi_TV的VideoActivity.checkId()有两种处理路径：

        if (vodId == null || vodId.isEmpty() || vodId.startsWith("msearch:")) {
            Log.d(TAG, "🔍 [FongMi_TV兼容] 路径1：搜索模式 - msearch类型或空ID: " + vodId);
            Log.d(TAG, "📺 [FongMi_TV兼容] 原版逻辑: setEmpty(false) → 显示电影名称 → 启动搜索功能");

            // 路径1：搜索模式处理
            handleSearchMode(vodId, movieName, siteKey);

        } else if (vodId.startsWith("push://")) {
            Log.d(TAG, "🔍 [FongMi_TV兼容] Push协议，特殊处理: " + vodId);
            handlePushProtocol(vodId, movieName, siteKey);
        } else {
            Log.d(TAG, "🔍 [FongMi_TV兼容] 路径2：直接模式 - 真实电影ID: " + vodId);
            Log.d(TAG, "📺 [FongMi_TV兼容] 原版逻辑: getDetail() → 直接获取电影详情");

            // 路径2：直接模式处理
            handleDirectMode(vodId, movieName, siteKey);
        }
    }

    /**
     * 路径1：搜索模式处理 - 对应原版FongMi_TV的setEmpty(false)逻辑
     *
     * 实际例子：用户点击"南京照相馆" (ID: msearch:home)
     * → VideoActivity启动 → checkId()检测到msearch:前缀 → setEmpty(false)
     */
    private void handleSearchMode(String vodId, String movieName, String siteKey) {
        Log.d(TAG, "🔍 [FongMi_TV兼容] 路径1：搜索模式处理");
        Log.d(TAG, "📺 [FongMi_TV兼容] 实际例子：用户点击\"" + movieName + "\" (ID: " + vodId + ")");
        Log.d(TAG, "📺 [FongMi_TV兼容] 原版setEmpty(false)逻辑:");
        Log.d(TAG, "📺 [FongMi_TV兼容] 1. 不关闭Activity");
        Log.d(TAG, "📺 [FongMi_TV兼容] 2. 显示电影标题: \"" + movieName + "\"");
        Log.d(TAG, "📺 [FongMi_TV兼容] 3. 启动搜索功能");

        if (movieName == null || movieName.isEmpty()) {
            Log.w(TAG, "⚠️ [FongMi_TV兼容] 电影名称为空，无法启动搜索模式");
            EventBus.getDefault().post(new ContentDetailEvent(null, false, "电影名称为空，无法搜索"));
            return;
        }

        // 🔥 关键：按照原版FongMi_TV的搜索模式逻辑
        // setEmpty(false) → 显示电影名称 → checkSearch(false) → initSearch() → startSearch()
        Log.d(TAG, "🔍 [FongMi_TV兼容] 在多个站点搜索: \"" + movieName + "\"");
        startSearchMode(movieName, siteKey);
    }

    /**
     * 启动搜索模式 - 对应原版FongMi_TV的checkSearch + initSearch + startSearch逻辑
     */
    private void startSearchMode(String movieName, String siteKey) {
        Log.d(TAG, "🌐 [FongMi_TV兼容] 启动搜索模式: " + movieName);
        Log.d(TAG, "🔍 [FongMi_TV兼容] 对应原版: checkSearch(false) → initSearch() → startSearch()");

        // 🔧 修复搜索结果累加问题：重置搜索状态标志
        hasNavigatedToSearchResults = false;
        currentSearchKeyword = movieName;
        Log.d(TAG, "🔧 [修复逻辑] 重置搜索状态标志，开始新搜索: " + movieName);

        // 🔥 新逻辑：立即发送导航事件，然后开始搜索
        Log.d(TAG, "🚀 [修复逻辑] 立即跳转到搜索结果列表界面");
        EventBus.getDefault().post(new NavigateToSearchResultsEvent(movieName));

        try {
            // 调用SiteViewModel进行多站点搜索
            Log.d(TAG, "🔍 [FongMi_TV兼容] 调用SiteViewModel.searchContent: " + movieName);
            // 🔥 关键修复：使用quick=false，与原版FongMi_TV保持一致
            siteViewModel.searchContent(movieName, false);

            Log.d(TAG, "✅ [FongMi_TV兼容] 搜索请求已发送，结果将动态更新到搜索结果列表");

        } catch (Exception e) {
            Log.e(TAG, "❌ [FongMi_TV兼容] 搜索模式启动失败", e);
            EventBus.getDefault().post(new ContentDetailEvent(null, false, "搜索失败: " + e.getMessage()));
        }
    }

    /**
     * 处理Push协议 - 提取真实URL并设置push_agent站点
     */
    private void handlePushProtocol(String pushUrl, String movieName, String siteKey) {
        Log.d(TAG, "🔄 [FongMi_TV兼容] 处理Push协议: " + pushUrl);

        // 提取真实URL（移除push://前缀）
        String realUrl = pushUrl.substring(7);
        String pushSiteKey = "push_agent";

        Log.d(TAG, "📊 [FongMi_TV兼容] Push协议转换: " + pushUrl + " -> " + realUrl + ", 站点: " + pushSiteKey);

        // 发送转换完成事件
        EventBus.getDefault().post(new MovieIdTransformEvent(realUrl, realUrl, pushSiteKey, movieName));
    }

    /**
     * 路径2：直接模式处理 - 对应原版FongMi_TV的getDetail()逻辑
     *
     * 实际例子：用户选择具体的电影版本 (真实ID: 55094)
     * → 重新启动VideoActivity → checkId()检测到真实ID → getDetail()
     */
    private void handleDirectMode(String vodId, String movieName, String siteKey) {
        Log.d(TAG, "✅ [FongMi_TV兼容] 路径2：直接模式处理");
        Log.d(TAG, "📺 [FongMi_TV兼容] 实际例子：使用真实ID获取详情");
        Log.d(TAG, "📺 [FongMi_TV兼容] 电影: \"" + movieName + "\" (真实ID: " + vodId + ")");
        Log.d(TAG, "📺 [FongMi_TV兼容] 原版getDetail()逻辑:");
        Log.d(TAG, "📺 [FongMi_TV兼容] 1. checkId()检测到真实ID: " + vodId);
        Log.d(TAG, "📺 [FongMi_TV兼容] 2. 直接调用getDetail()");
        Log.d(TAG, "📺 [FongMi_TV兼容] 3. detailContent() → 获取电影详情");
        Log.d(TAG, "📺 [FongMi_TV兼容] 4. setDetail() → 显示详情页面");
        Log.d(TAG, "🎯 [电影ID跟踪] 直接模式处理真实电影ID: " + vodId);

        // 🔥 关键：直接模式不需要搜索，直接获取详情
        // 对应原版: getDetail() → detailContent() → setDetail()
        getContentDetail(vodId, siteKey);
    }

    // 🔥 重要：删除错误的ID转换逻辑
    // 原版FongMi_TV中，msearch类型ID不需要转换，应该直接显示空页面
    // 真实电影ID也不需要转换，应该直接获取详情
    // 因此删除performIdTransformation和startMultiSiteSearch方法

    /**
     * 获取内容详情 - 完整的FongMi_TV兼容错误处理
     */
    public void getContentDetail(String vodId, String siteKey) {
        Log.d(TAG, "🔄 [FongMi_TV兼容] 获取内容详情: vodId=" + vodId + ", siteKey=" + siteKey);

        // 参数验证
        if (vodId == null || vodId.isEmpty()) {
            Log.e(TAG, "❌ [FongMi_TV兼容] 无效的电影ID");
            EventBus.getDefault().post(new ContentDetailEvent(null, false, "电影ID不能为空"));
            return;
        }

        // 🔥 删除临时ID检查逻辑
        // 原版FongMi_TV中不存在临时ID概念，所有ID都是真实的功能标识

        if (vodId.startsWith("msearch:")) {
            Log.w(TAG, "⚠️ [FongMi_TV兼容] msearch类型ID无法直接获取详情: " + vodId);
            handleMsearchIdError(vodId, siteKey);
            return;
        }

        try {
            // 站点选择逻辑
            if (siteKey != null && !siteKey.isEmpty()) {
                Log.d(TAG, "🔑 [FongMi_TV兼容] 使用指定站点: " + siteKey);
                siteViewModel.detailContent(siteKey, vodId);
            } else {
                Log.d(TAG, "🏠 [FongMi_TV兼容] 使用默认站点");
                siteViewModel.detailContent(vodId);
            }
            Log.d(TAG, "✅ [FongMi_TV兼容] 内容详情请求已发送");

        } catch (IllegalArgumentException e) {
            Log.e(TAG, "❌ [FongMi_TV兼容] 参数错误: " + e.getMessage());
            handleParameterError(vodId, siteKey, e);
        } catch (RuntimeException e) {
            Log.e(TAG, "❌ [FongMi_TV兼容] 运行时错误: " + e.getMessage());
            handleRuntimeError(vodId, siteKey, e);
        } catch (Exception e) {
            Log.e(TAG, "❌ [FongMi_TV兼容] 未知错误: " + e.getMessage(), e);
            handleUnknownError(vodId, siteKey, e);
        }
    }

    // 🔥 删除handleTempIdError方法
    // 原版FongMi_TV中不存在临时ID概念

    /**
     * 处理msearch类型ID错误
     */
    private void handleMsearchIdError(String msearchId, String siteKey) {
        String errorMsg = "搜索类型ID无法直接获取详情，请从搜索结果中选择";
        Log.w(TAG, "⚠️ [FongMi_TV兼容] " + errorMsg + ": " + msearchId);
        EventBus.getDefault().post(new ContentDetailEvent(null, false, errorMsg));
    }

    /**
     * 处理参数错误
     */
    private void handleParameterError(String vodId, String siteKey, IllegalArgumentException e) {
        String errorMsg = "参数错误: " + e.getMessage();
        Log.e(TAG, "❌ [FongMi_TV兼容] " + errorMsg);
        EventBus.getDefault().post(new ContentDetailEvent(null, false, errorMsg));
    }

    /**
     * 处理运行时错误
     */
    private void handleRuntimeError(String vodId, String siteKey, RuntimeException e) {
        String errorMsg = "系统错误: " + e.getMessage();
        Log.e(TAG, "❌ [FongMi_TV兼容] " + errorMsg);
        EventBus.getDefault().post(new ContentDetailEvent(null, false, errorMsg));
    }

    /**
     * 处理未知错误
     */
    private void handleUnknownError(String vodId, String siteKey, Exception e) {
        String errorMsg = "获取电影详情失败: " + e.getMessage();
        Log.e(TAG, "❌ [FongMi_TV兼容] " + errorMsg);
        EventBus.getDefault().post(new ContentDetailEvent(null, false, errorMsg));
    }

    /**
     * 搜索内容
     */
    public void searchContent(String keyword, String siteKey) {
        Log.d(TAG, "🔄 搜索内容: keyword=" + keyword);
        try {
            EventBus.getDefault().post(new SearchStartEvent(keyword, siteKey));
            // 🔥 关键修复：使用quick=false，与原版FongMi_TV保持一致
            siteViewModel.searchContent(keyword, false);
            Log.d(TAG, "✅ 搜索请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 搜索失败", e);
            String errorMsg = e.getMessage() != null ? e.getMessage() : "搜索异常";
            EventBus.getDefault().post(new SearchErrorEvent(keyword, errorMsg));
        }
    }

    /**
     * 搜索内容（重载方法，用于搜索结果列表页面）
     */
    public void searchContent(String keyword) {
        searchContent(keyword, "");
    }

    /**
     * 占位EventBus监听方法 - 确保EventBus注册不会失败
     * 现在搜索结果直接从SiteViewModel发送到UI，不需要在RepositoryAdapter中转发
     */
    @org.greenrobot.eventbus.Subscribe(threadMode = org.greenrobot.eventbus.ThreadMode.MAIN)
    public void onPlaceholderEvent(Object event) {
        // 占位方法，不做任何处理
        // 这个方法确保EventBus能够找到@Subscribe注解的方法
    }

    /**
     * 模拟Intent参数替换 - 对应原版FongMi_TV的INTENT_PARAM_REPLACE逻辑
     */
    private void simulateIntentParamReplace(String realId, String movieName, String siteKey) {
        Log.d(TAG, "🔄 [FongMi_TV兼容] 模拟Intent参数替换");
        Log.d(TAG, "📺 [FongMi_TV兼容] 对应原版[INTENT_PARAM_REPLACE]: ID msearch:home→" + realId + ", 站点→" + siteKey);
        Log.d(TAG, "🎯 [FongMi_TV兼容] 模拟重新启动VideoActivity，使用真实ID: " + realId);
        Log.d(TAG, "🔄 [FongMi_TV兼容] 这次checkId()会检测到真实ID，走getDetail()路径");

        // 直接调用直接模式处理，模拟重新启动VideoActivity的效果
        handleDirectMode(realId, movieName, siteKey);
    }

    /**
     * 获取推荐内容
     */
    public void getRecommendContent() {
        Log.d(TAG, "🔄 获取推荐内容");
        try {
            // ✅ 修复：使用homeContent()获取推荐内容，而不是空关键词搜索
            // 因为很多站点不支持空关键词搜索，但homeContent()会返回首页推荐内容
            siteViewModel.homeContent();
            Log.d(TAG, "✅ 推荐内容请求已发送（通过homeContent）");
        } catch (Exception e) {
            Log.e(TAG, "❌ 推荐内容获取失败", e);
            EventBus.getDefault().post(new ErrorEvent("推荐内容获取失败: " + e.getMessage(), ErrorEvent.Type.NETWORK, null));
        }
    }

    // ===== 播放相关接口 =====

    /**
     * 解析播放地址
     */
    public void parsePlayUrl(String url, String siteKey, String flag) {
        Log.d(TAG, "🔄 [播放地址解析] 请求解析播放地址");
        Log.d(TAG, "🔄 [播放地址解析] 参数: url=" + url + ", siteKey=" + siteKey + ", flag=" + flag);
        Log.d(TAG, "🔥 [播放地址解析修复] SiteViewModel实例: " + (siteViewModel != null ? "存在" : "null"));

        try {
            EventBus.getDefault().post(new PlayUrlParseStartEvent("", url, flag));

            // 🔥 关键修复：使用延迟初始化确保SiteViewModel实例存在
            ensureSiteViewModelInitialized();

            // 🔥 修复：使用正确的参数顺序调用SiteViewModel.playerContent(key, flag, id)
            Log.d(TAG, "🔥 [播放地址解析] 即将调用siteViewModel.playerContent");
            siteViewModel.playerContent(siteKey, flag, url);
            Log.d(TAG, "✅ [播放地址解析] 播放地址解析请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ [播放地址解析] 播放地址解析失败", e);
            EventBus.getDefault().post(new PlayUrlParseErrorEvent("", e.getMessage()));
        }
    }

    /**
     * 切换播放线路
     */
    public void switchLine(String flag, String url) {
        Log.d(TAG, "🔄 切换播放线路: flag=" + flag + ", url=" + url);
        try {
            // ✅ 重新解析播放地址
            parsePlayUrl(url, "", flag);
            Log.d(TAG, "✅ 线路切换请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 线路切换失败", e);
            EventBus.getDefault().post(new ErrorEvent("", ErrorEvent.Type.PARSE, "线路切换失败: " + e.getMessage()));
        }
    }

    // ===== 收藏管理接口 =====

    /**
     * 添加收藏
     */
    public void addToFavorites(top.cywin.onetv.movie.bean.Vod movie) {
        Log.d(TAG, "🔄 添加收藏: " + movie.getVodName());
        try {
            // ✅ 手动创建Keep对象
            top.cywin.onetv.movie.bean.Keep keep = new top.cywin.onetv.movie.bean.Keep();
            keep.setKey(
                    movie.getSite().getKey() + top.cywin.onetv.movie.database.AppDatabase.SYMBOL + movie.getVodId());
            keep.setCid(VodConfig.getCid());
            keep.setSiteName(movie.getSite().getName());
            keep.setVodPic(movie.getVodPic());
            keep.setVodName(movie.getVodName());
            keep.setCreateTime(System.currentTimeMillis());
            keep.setType(0); // 0 for VOD
            keep.save();
            Log.d(TAG, "✅ 收藏添加成功");
            EventBus.getDefault().post(new FavoriteUpdateEvent(movie.getVodId(), true, true));
        } catch (Exception e) {
            Log.e(TAG, "❌ 收藏添加失败", e);
            EventBus.getDefault().post(new FavoriteUpdateEvent(movie.getVodId(), true, false));
        }
    }

    /**
     * 移除收藏
     */
    public void removeFromFavorites(String vodId, String siteKey) {
        Log.d(TAG, "🔄 移除收藏: " + vodId);
        try {
            // ✅ 直接调用FongMi_TV现有的Keep系统
            top.cywin.onetv.movie.bean.Keep.delete(vodId);
            Log.d(TAG, "✅ 收藏移除成功");
            EventBus.getDefault().post(new FavoriteUpdateEvent(vodId, false, true));
        } catch (Exception e) {
            Log.e(TAG, "❌ 收藏移除失败", e);
            EventBus.getDefault().post(new FavoriteUpdateEvent(vodId, false, false));
        }
    }

    /**
     * 检查收藏状态
     */
    public boolean isFavorite(String vodId, String siteKey) {
        try {
            // ✅ 直接调用FongMi_TV现有的Keep系统
            return top.cywin.onetv.movie.bean.Keep.exist(vodId);
        } catch (Exception e) {
            Log.e(TAG, "❌ 检查收藏状态失败", e);
            return false;
        }
    }

    // ===== 历史记录接口 =====

    /**
     * 获取观看历史列表
     */
    public void getWatchHistoryList() {
        Log.d(TAG, "🔄 获取观看历史列表");
        try {
            // ✅ 直接调用FongMi_TV现有的History系统
            List<top.cywin.onetv.movie.bean.History> histories = top.cywin.onetv.movie.bean.History.get();
            Log.d(TAG, "✅ 获取到历史记录: " + histories.size() + "条");

            // 通过EventBus发送历史记录事件
            EventBus.getDefault().post(new HistoryListEvent(histories, true, null));
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取观看历史列表失败", e);
            EventBus.getDefault().post(new HistoryListEvent(new ArrayList<>(), false, e.getMessage()));
        }
    }

    /**
     * 获取观看历史
     */
    public top.cywin.onetv.movie.bean.History getWatchHistory(String vodId, String siteKey) {
        try {
            // ✅ 直接调用FongMi_TV现有的History系统
            return top.cywin.onetv.movie.bean.History.find(vodId);
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取观看历史失败", e);
            return null;
        }
    }

    /**
     * 保存观看历史
     */
    public void saveWatchHistory(String vodId, String vodName, long position, long duration) {
        Log.d(TAG, "🔄 保存观看历史: " + vodId);
        try {
            // ✅ 直接调用FongMi_TV现有的History系统
            String siteKey = getSite() != null ? getSite().getKey() : "default";
            String historyKey = siteKey + top.cywin.onetv.movie.database.AppDatabase.SYMBOL + vodId;
            top.cywin.onetv.movie.bean.History history = top.cywin.onetv.movie.bean.History.find(historyKey);
            if (history == null) {
                history = new top.cywin.onetv.movie.bean.History();
                history.setKey(historyKey);
                history.setVodName(vodName);
                history.setCid(VodConfig.getCid());
            }
            history.setPosition(position);
            history.setDuration(duration);
            history.setCreateTime(System.currentTimeMillis());
            history.save();

            Log.d(TAG, "✅ 观看历史保存成功");
            EventBus.getDefault().post(new HistoryUpdateEvent(vodId, position, duration, true));
        } catch (Exception e) {
            Log.e(TAG, "❌ 观看历史保存失败", e);
            EventBus.getDefault().post(new HistoryUpdateEvent(vodId, position, duration, false));
        }
    }

    // ===== 收藏功能接口 =====

    /**
     * 获取收藏列表
     */
    public void getFavoriteList() {
        Log.d(TAG, "🔄 获取收藏列表");
        try {
            // ✅ 直接调用FongMi_TV现有的Keep系统
            List<top.cywin.onetv.movie.bean.Keep> favorites = top.cywin.onetv.movie.bean.Keep.getVod();
            Log.d(TAG, "✅ 获取到收藏记录: " + favorites.size() + "条");

            // 通过EventBus发送收藏列表事件
            EventBus.getDefault().post(new FavoriteListEvent(favorites, true, null));
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取收藏列表失败", e);
            EventBus.getDefault().post(new FavoriteListEvent(new ArrayList<>(), false, e.getMessage()));
        }
    }

    /**
     * 添加收藏
     */
    public void addToFavorite(String vodId, String vodName, String vodPic) {
        Log.d(TAG, "🔄 添加收藏: " + vodName);
        try {
            top.cywin.onetv.movie.bean.Site currentSite = getSite();
            if (currentSite == null) {
                Log.e(TAG, "❌ 当前站点为空，无法添加收藏");
                return;
            }

            String keepKey = currentSite.getKey() + top.cywin.onetv.movie.database.AppDatabase.SYMBOL + vodId;
            top.cywin.onetv.movie.bean.Keep keep = new top.cywin.onetv.movie.bean.Keep();
            keep.setKey(keepKey);
            keep.setSiteName(currentSite.getName());
            keep.setVodName(vodName);
            keep.setVodPic(vodPic);
            keep.setType(0); // 0表示点播收藏
            keep.setCreateTime(System.currentTimeMillis());
            keep.save(VodConfig.getCid());

            Log.d(TAG, "✅ 收藏添加成功");
            EventBus.getDefault().post(new FavoriteUpdateEvent(vodId, true, true));
        } catch (Exception e) {
            Log.e(TAG, "❌ 添加收藏失败", e);
            EventBus.getDefault().post(new FavoriteUpdateEvent(vodId, true, false));
        }
    }

    /**
     * 移除收藏
     */
    public void removeFromFavorite(String vodId) {
        Log.d(TAG, "🔄 移除收藏: " + vodId);
        try {
            top.cywin.onetv.movie.bean.Site currentSite = getSite();
            if (currentSite == null) {
                Log.e(TAG, "❌ 当前站点为空，无法移除收藏");
                return;
            }

            String keepKey = currentSite.getKey() + top.cywin.onetv.movie.database.AppDatabase.SYMBOL + vodId;
            top.cywin.onetv.movie.bean.Keep.delete(keepKey);

            Log.d(TAG, "✅ 收藏移除成功");
            EventBus.getDefault().post(new FavoriteUpdateEvent(vodId, false, true));
        } catch (Exception e) {
            Log.e(TAG, "❌ 移除收藏失败", e);
            EventBus.getDefault().post(new FavoriteUpdateEvent(vodId, false, false));
        }
    }

    // ===== 站点管理接口 =====

    /**
     * 获取站点列表
     */
    public void getSiteList() {
        Log.d(TAG, "🔄 获取站点列表");
        try {
            List<top.cywin.onetv.movie.bean.Site> sites = VodConfig.get().getSites();
            Log.d(TAG, "✅ 获取到站点: " + sites.size() + "个");

            // 通过EventBus发送站点列表事件
            EventBus.getDefault().post(new SiteListEvent(sites, true, null));
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取站点列表失败", e);
            EventBus.getDefault().post(new SiteListEvent(new ArrayList<>(), false, e.getMessage()));
        }
    }

    /**
     * 获取当前站点
     */
    public top.cywin.onetv.movie.bean.Site getCurrentSite() {
        try {
            return VodConfig.get().getHome();
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取当前站点失败", e);
            return null;
        }
    }

    /**
     * 切换站点
     */
    public void switchSite(String siteKey) {
        Log.d(TAG, "🔄 切换站点: " + siteKey);
        try {
            top.cywin.onetv.movie.bean.Site site = VodConfig.get().getSite(siteKey);
            if (site != null) {
                VodConfig.get().setHome(site);
                Log.d(TAG, "✅ 站点切换成功: " + site.getName());
                EventBus.getDefault().post(new SiteChangeEvent(site, true));

                // 重新加载首页内容
                getHomeContent();
            } else {
                Log.e(TAG, "❌ 站点不存在: " + siteKey);
                EventBus.getDefault().post(new SiteChangeEvent(null, false));
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ 站点切换失败", e);
            EventBus.getDefault().post(new SiteChangeEvent(null, false));
        }
    }

    // ===== 云盘相关接口 =====

    /**
     * 加载云盘配置列表
     */
    public void loadCloudDriveConfigs() {
        Log.d(TAG, "🔄 加载云盘配置列表");
        try {
            // 这里需要根据实际的FongMi_TV云盘实现进行调用
            // List<CloudDriveConfig> configs = CloudDriveManager.getConfigs();
            // EventBus.getDefault().post(new CloudDriveConfigEvent(configs, true));
            Log.d(TAG, "✅ 云盘配置列表请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 云盘配置列表获取失败", e);
            EventBus.getDefault().post(new ErrorEvent("云盘配置列表获取失败: " + e.getMessage(), ErrorEvent.Type.NETWORK));
        }
    }

    /**
     * 获取云盘文件列表
     */
    public void getCloudFiles(String driveId, String path) {
        Log.d(TAG, "🔄 获取云盘文件列表: driveId=" + driveId + ", path=" + path);
        try {
            // 这里需要根据实际的FongMi_TV云盘实现进行调用
            // CloudDriveManager.getFiles(driveId, path, callback);
            Log.d(TAG, "✅ 云盘文件列表请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 云盘文件列表获取失败", e);
            EventBus.getDefault().post(new CloudDriveEvent(driveId, null, path, false));
        }
    }

    // ===== 直播相关接口 =====

    /**
     * 获取直播频道列表
     */
    public void getLiveChannels(String group) {
        Log.d(TAG, "🔄 获取直播频道列表: group=" + group);
        try {
            // 这里需要根据实际的FongMi_TV直播实现进行调用
            // LiveViewModel.get().getChannels(group);
            Log.d(TAG, "✅ 直播频道列表请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 直播频道列表获取失败", e);
            EventBus.getDefault().post(new LiveChannelEvent(null, group, false));
        }
    }

    /**
     * 解析直播播放地址
     */
    public void parseLivePlayUrl(String channelUrl, String channelName) {
        Log.d(TAG, "🔄 解析直播播放地址: " + channelName);
        try {
            // 这里需要根据实际的FongMi_TV直播实现进行调用
            // LiveViewModel.get().parsePlayUrl(channelUrl, callback);
            Log.d(TAG, "✅ 直播播放地址解析请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "❌ 直播播放地址解析失败", e);
            EventBus.getDefault().post(new LivePlayEvent("", channelName, false));
        }
    }

    // ===== 设置相关接口 =====

    /**
     * 更新设置
     */
    public void updateSetting(String key, Object value) {
        Log.d(TAG, "🔄 更新设置: " + key + " = " + value);
        try {
            // 这里需要根据实际的FongMi_TV设置实现进行调用
            // Setting.put(key, value);
            Log.d(TAG, "✅ 设置更新成功");
            EventBus.getDefault().post(new SettingsUpdateEvent(key, value, true));
        } catch (Exception e) {
            Log.e(TAG, "❌ 设置更新失败", e);
            EventBus.getDefault().post(new SettingsUpdateEvent(key, value, false));
        }
    }

    // ===== 网络相关接口 =====

    /**
     * 测试API连接
     */
    public void testApiConnection(String url) {
        Log.d(TAG, "🔄 测试API连接: " + url);
        try {
            long startTime = System.currentTimeMillis();
            // 这里需要根据实际的FongMi_TV网络实现进行调用
            // OkHttp.get().newCall(request).execute();
            long responseTime = System.currentTimeMillis() - startTime;
            Log.d(TAG, "✅ API连接测试成功");
            EventBus.getDefault().post(new ApiTestEvent(url, true, responseTime, null));
        } catch (Exception e) {
            Log.e(TAG, "❌ API连接测试失败", e);
            EventBus.getDefault().post(new ApiTestEvent(url, false, 0, e.getMessage()));
        }
    }

    /**
     * 检查系统状态
     */
    public boolean isSystemReady() {
        try {
            VodConfig vodConfig = VodConfig.get();
            return vodConfig != null && !vodConfig.getSites().isEmpty();
        } catch (Exception e) {
            Log.e(TAG, "❌ 系统状态检查失败", e);
            return false;
        }
    }

    // ===== 配置管理方法 =====

    /**
     * 获取VodConfig实例
     */
    public top.cywin.onetv.movie.api.config.VodConfig getVodConfig() {
        return top.cywin.onetv.movie.api.config.VodConfig.get();
    }

    /**
     * 测试连接
     */
    public boolean testConnection(String url, String apiKey) {
        try {
            // ✅ 使用FongMi_TV现有的网络测试逻辑
            long startTime = System.currentTimeMillis();
            String response = com.github.catvod.net.OkHttp.string(url);
            long responseTime = System.currentTimeMillis() - startTime;
            boolean result = response != null && !response.isEmpty();
            EventBus.getDefault().post(new ApiTestEvent(url, result, responseTime, result ? null : "连接失败"));
            return result;
        } catch (Exception e) {
            Log.e(TAG, "连接测试失败", e);
            EventBus.getDefault().post(new ApiTestEvent(url, false, 0, e.getMessage()));
            return false;
        }
    }

    /**
     * 清除配置缓存
     */
    public void clearConfigCache() {
        try {
            top.cywin.onetv.movie.api.config.VodConfig vodConfig = top.cywin.onetv.movie.api.config.VodConfig.get();
            vodConfig.clear();
            Log.d(TAG, "✅ 配置缓存清除成功");
        } catch (Exception e) {
            Log.e(TAG, "❌ 配置缓存清除失败", e);
        }
    }

    /**
     * 解析路由配置
     */
    public void parseRouteConfig(String configUrl) {
        try {
            // ✅ 创建Config对象并加载
            top.cywin.onetv.movie.bean.Config config = top.cywin.onetv.movie.bean.Config.find(configUrl, 0);
            VodConfig.load(config, new top.cywin.onetv.movie.impl.Callback() {
                @Override
                public void success() {
                    Log.d(TAG, "✅ 路由配置解析成功");
                    EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), true, null));
                }

                @Override
                public void error(String msg) {
                    Log.e(TAG, "❌ 路由配置解析失败: " + msg);
                    EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, msg));
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "❌ 路由配置解析失败", e);
            EventBus.getDefault().post(new ConfigUpdateEvent(VodConfig.get(), false, e.getMessage()));
        }
    }

    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        try {
            // ✅ 清除FongMi_TV的所有缓存
            top.cywin.onetv.movie.api.config.VodConfig vodConfig = top.cywin.onetv.movie.api.config.VodConfig.get();
            vodConfig.clear();

            // 清除数据库缓存
            top.cywin.onetv.movie.database.AppDatabase.get().clearAllTables();

            Log.d(TAG, "✅ 所有缓存清除成功");
        } catch (Exception e) {
            Log.e(TAG, "❌ 清除所有缓存失败", e);
        }
    }

    // ===== 播放历史管理方法 =====

    /**
     * 保存播放历史
     */
    public void savePlayHistory(String vodId, String siteKey, int episodeIndex, long position, long duration) {
        try {
            top.cywin.onetv.movie.bean.History history = new top.cywin.onetv.movie.bean.History();
            history.setKey(vodId);
            history.setCid(Integer.parseInt(siteKey));
            history.setPosition(position);
            history.setDuration(duration);
            history.setCreateTime(System.currentTimeMillis());
            history.save();

            Log.d(TAG, "✅ 播放历史保存成功");
        } catch (Exception e) {
            Log.e(TAG, "❌ 播放历史保存失败", e);
        }
    }

    /**
     * 获取当前VOD ID
     */
    public String getVodId() {
        try {
            top.cywin.onetv.movie.api.config.VodConfig vodConfig = top.cywin.onetv.movie.api.config.VodConfig.get();
            return vodConfig.getHome().getKey();
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取VOD ID失败", e);
            return "";
        }
    }

    /**
     * 获取当前站点
     */
    public top.cywin.onetv.movie.bean.Site getSite() {
        try {
            top.cywin.onetv.movie.api.config.VodConfig vodConfig = top.cywin.onetv.movie.api.config.VodConfig.get();
            return vodConfig.getHome();
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取当前站点失败", e);
            return null;
        }
    }

    /**
     * 获取分类名称
     */
    public String getCategoryName(String typeId) {
        try {
            // 通过SiteViewModel获取分类信息
            top.cywin.onetv.movie.MovieApp movieApp = top.cywin.onetv.movie.MovieApp.Companion.getInstance();
            top.cywin.onetv.movie.model.SiteViewModel siteViewModel = movieApp.getSiteViewModel();
            // categoryContent方法返回void，不能赋值给Result
            // 使用简化处理方式

            // 简化处理，直接返回typeId作为分类名称
            return typeId;
        } catch (Exception e) {
            Log.e(TAG, "❌ 获取分类名称失败", e);
            return "未知分类";
        }
    }

    /**
     * 🔥 原版FongMi_TV直接导航：监听ContentDetailEvent并直接跳转
     * 原版流程：电影点击 → VideoActivity.getDetail() → 直接页面跳转
     * 我们的流程：电影点击 → RepositoryAdapter → 直接navController.navigate()
     * 🔥 关键修复：防止重复导航造成无限循环
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onContentDetailEvent(ContentDetailEvent event) {
        Log.d(TAG, "📡 [原版直接导航] RepositoryAdapter收到ContentDetailEvent: success=" + event.getSuccess());

        if (event.getSuccess() && event.getVod() != null) {
            String vodId = event.getVod().getVodId();
            String siteKey = event.getVod().getSiteKey();
            long currentTime = System.currentTimeMillis();

            // 🔥 关键修复：检查是否为重复导航
            if (isRepeatedNavigation(vodId, siteKey, currentTime)) {
                Log.d(TAG, "🚫 [重复导航修复] 忽略重复导航请求: vodId=" + vodId + ", siteKey=" + siteKey);
                return;
            }

            // 🔥 新增修复：检查当前是否已经在详情页面
            if (navController != null) {
                try {
                    // 获取当前路由
                    String currentRoute = navController.getCurrentDestination() != null
                            ? navController.getCurrentDestination().getRoute()
                            : "";

                    // 如果当前已经在详情页面，不再执行导航
                    if (currentRoute != null && currentRoute.contains("movie_detail")) {
                        Log.d(TAG, "🚫 [搜索结果修复] 当前已在详情页面，跳过RepositoryAdapter导航");
                        Log.d(TAG, "📍 [搜索结果修复] 当前路由: " + currentRoute);
                        return;
                    }
                } catch (Exception e) {
                    Log.w(TAG, "⚠️ [搜索结果修复] 获取当前路由失败，继续执行导航", e);
                }
            }

            Log.d(TAG, "🔥 [原版直接导航] 采用原版FongMi_TV直接跳转模式！");
            Log.d(TAG, "✅ [原版直接导航] 详情获取成功，直接跳转到详情页");
            Log.d(TAG, "🎯 [电影ID跟踪] 详情数据: vodId=" + vodId + ", name=" + event.getVod().getVodName());

            // 🔥 原版FongMi_TV直接导航：直接调用navController.navigate()
            if (navController != null) {
                try {
                    String route = MovieRoutes.INSTANCE.detail(vodId, siteKey);
                    Log.d(TAG, "🔗 [原版直接导航] 导航路由: " + route);

                    navController.navigate(route);

                    // 🔥 关键修复：记录导航状态，防止重复导航
                    updateNavigationState(vodId, siteKey, currentTime);

                    Log.d(TAG, "✅ [原版直接导航] 直接导航成功！");
                    Log.d(TAG, "🎯 [电影ID跟踪] 成功跳转到详情页面");
                } catch (Exception e) {
                    Log.e(TAG, "❌ [原版直接导航] 直接导航失败", e);
                }
            } else {
                Log.w(TAG, "⚠️ [原版直接导航] NavController未设置，无法导航");
            }

        } else {
            Log.w(TAG, "⚠️ [原版直接导航] 详情获取失败: " + event.getErrorMessage());
        }
    }

    /**
     * 🔥 关键修复：检查是否为重复导航
     */
    private boolean isRepeatedNavigation(String vodId, String siteKey, long currentTime) {
        // 检查是否为相同的导航请求
        boolean sameRequest = vodId != null && vodId.equals(lastNavigatedVodId)
                && siteKey != null && siteKey.equals(lastNavigatedSiteKey);

        // 检查是否在冷却时间内
        boolean withinCooldown = (currentTime - lastNavigationTime) < NAVIGATION_COOLDOWN;

        return sameRequest && withinCooldown;
    }

    /**
     * 🔥 关键修复：更新导航状态
     */
    private void updateNavigationState(String vodId, String siteKey, long currentTime) {
        lastNavigatedVodId = vodId;
        lastNavigatedSiteKey = siteKey;
        lastNavigationTime = currentTime;
        Log.d(TAG, "🔥 [重复导航修复] 更新导航状态: vodId=" + vodId + ", siteKey=" + siteKey);
    }

}
