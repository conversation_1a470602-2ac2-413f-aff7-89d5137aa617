# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# 添加JDK 17模块访问权限以解决Glide注解处理器问题
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8 \
  --add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED \
  --add-opens=jdk.compiler/com.sun.tools.javac.comp=ALL-UNNAMED \
  --add-opens=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED \
  --add-opens=jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED \
  --add-opens=jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED \
  --add-opens=jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED \
  --add-opens=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED \
  --add-opens=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED \
  --add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app"s APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true

# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true

# Suppress warning about unsupported compileSdk version
android.suppressUnsupportedCompileSdk=35

# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official

# Kotlin compiler settings for 2.1.21
# kotlin.experimental.tryK2=true  # 已弃用，移除
kotlin.daemon.jvmargs=-Xmx4g -XX:+UseParallelGC

# Enable parallel execution (与FongMi_TV保持一致，暂时禁用)
org.gradle.parallel=false

APP_VERSION_NAME=2.0.0
APP_VERSION_CODE=2
APP_APPLICATION_ID=top.cywin.onetv.tv