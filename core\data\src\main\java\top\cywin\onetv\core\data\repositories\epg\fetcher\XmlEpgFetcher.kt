package top.cywin.onetv.core.data.repositories.epg.fetcher

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.Response

/**
 * 节目单xml数据获取
 */
class XmlEpgFetcher : EpgFetcher {
    override fun isSupport(url: String): Bo<PERSON>an {
        return url.endsWith(".xml")
    }

    override suspend fun fetch(response: Response) = withContext(Dispatchers.IO) {
        response.body!!.string()
    }
}