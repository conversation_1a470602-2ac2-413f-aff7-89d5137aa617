package top.cywin.onetv.movie.quickjs.utils;

import android.net.Uri;

import com.github.catvod.net.OkHttp;
import com.github.catvod.utils.Asset;
import com.github.catvod.utils.Path;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentHashMap;

public class Module {

    private final ConcurrentHashMap<String, String> cache;

    private static class Loader {
        static volatile Module INSTANCE = new Module();
    }

    public static Module get() {
        return Loader.INSTANCE;
    }

    public Module() {
        this.cache = new ConcurrentHashMap<>();
    }

    public String fetch(String name) {
        // 🔧 修复：使用containsKey而不是contains
        if (cache.containsKey(name)) {
            android.util.Log.d("ONETV_MODULE", "✅ 从缓存获取: " + name);
            return cache.get(name);
        }

        android.util.Log.d("ONETV_MODULE", "🔄 开始获取内容: " + name);

        if (name.startsWith("http")) {
            String content = request(name);
            cache.put(name, content);
            android.util.Log.d("ONETV_MODULE", "📥 HTTP内容获取完成，长度: " + content.length());
        }
        if (name.startsWith("assets")) {
            String content = Asset.read(name);
            cache.put(name, content);
            android.util.Log.d("ONETV_MODULE", "📁 Assets内容获取完成，长度: " + content.length());
        }
        if (name.startsWith("lib/")) {
            String content = Asset.read("js/" + name);
            cache.put(name, content);
            android.util.Log.d("ONETV_MODULE", "📚 Lib内容获取完成，长度: " + content.length());
        }

        String result = cache.get(name);
        android.util.Log.d("ONETV_MODULE", "✅ 最终内容长度: " + (result != null ? result.length() : "null"));
        return result;
    }

    private String request(String url) {
        try {
            android.util.Log.d("ONETV_MODULE", "🌐 开始HTTP请求: " + url);
            Uri uri = Uri.parse(url);
            byte[] data = OkHttp.bytes(url);

            if (data == null || data.length == 0) {
                android.util.Log.w("ONETV_MODULE", "⚠️ HTTP响应为空: " + url);
                return cache(url);
            }

            android.util.Log.d("ONETV_MODULE", "✅ HTTP请求成功，数据长度: " + data.length);

            File file = Path.js(uri.getLastPathSegment());
            boolean cache = !"127.0.0.1".equals(uri.getHost());
            if (cache) new Thread(() -> Path.write(file, data)).start();

            String content = new String(data, StandardCharsets.UTF_8);
            android.util.Log.d("ONETV_MODULE", "✅ 内容转换完成，字符长度: " + content.length());
            return content;
        } catch (Exception e) {
            android.util.Log.e("ONETV_MODULE", "❌ HTTP请求失败: " + url, e);
            return cache(url);
        }
    }

    private String cache(String url) {
        try {
            android.util.Log.d("ONETV_MODULE", "🔍 尝试从缓存获取: " + url);
            Uri uri = Uri.parse(url);
            File file = Path.js(uri.getLastPathSegment());

            if (file.exists()) {
                String content = Path.read(file);
                android.util.Log.d("ONETV_MODULE", "✅ 缓存文件存在，内容长度: " + content.length());
                return content;
            } else {
                android.util.Log.w("ONETV_MODULE", "⚠️ 缓存文件不存在: " + file.getAbsolutePath());
                return "";
            }
        } catch (Exception e) {
            android.util.Log.e("ONETV_MODULE", "❌ 缓存读取失败: " + url, e);
            return "";
        }
    }
}
