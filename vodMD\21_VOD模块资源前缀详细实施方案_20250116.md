# VOD模块资源前缀详细实施方案

## 实施概述

### 目标
为VOD模块的所有资源文件添加`vod_`前缀，确保与主应用完全隔离，避免资源冲突。

### 实施原则
1. **分步执行** - 每个子模块独立处理
2. **验证优先** - 每步完成后立即验证
3. **安全第一** - 完整备份和回滚机制
4. **自动化** - 减少人工错误

## 第一阶段：环境准备

### 1.1 创建工作分支
```bash
cd d:\apk\OneTV_Movie_Supabase
git checkout -b feature/vod-resource-prefix
git push -u origin feature/vod-resource-prefix
```

### 1.2 创建备份
```bash
# 创建完整备份
cp -r vod vod_backup_$(date +%Y%m%d_%H%M%S)
```

### 1.3 Gradle配置更新
在`vod/build.gradle.kts`中添加资源前缀配置：

```kotlin
android {
    // 在现有android块中添加
    resourcePrefix = "vod_"
    
    // 添加资源处理任务
    tasks.register("renameResources") {
        group = "vod"
        description = "重命名VOD模块资源文件并更新引用"
        
        doLast {
            // 执行资源重命名脚本
            exec {
                commandLine("python", "scripts/rename_vod_resources.py")
            }
        }
    }
}
```

## 第二阶段：自动化脚本开发

### 2.1 创建Python重命名脚本
创建`scripts/rename_vod_resources.py`：

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VOD模块资源重命名脚本
自动为所有资源文件添加vod_前缀并更新引用
"""

import os
import re
import shutil
from pathlib import Path
from typing import List, Dict, Tuple

class VodResourceRenamer:
    def __init__(self, vod_path: str):
        self.vod_path = Path(vod_path)
        self.prefix = "vod_"
        self.renamed_files = {}
        self.updated_references = []
        
    def scan_resources(self, module_path: Path) -> Dict[str, List[Path]]:
        """扫描模块中的所有资源文件"""
        resources = {
            'layout': [],
            'drawable': [],
            'mipmap': [],
            'anim': [],
            'color': [],
            'menu': [],
            'xml': [],
            'values': []
        }
        
        res_path = module_path / "res"
        if not res_path.exists():
            return resources
            
        for res_type in resources.keys():
            # 扫描各种密度的资源目录
            for res_dir in res_path.glob(f"{res_type}*"):
                if res_dir.is_dir():
                    for file_path in res_dir.glob("*"):
                        if file_path.is_file():
                            resources[res_type].append(file_path)
                            
        return resources
    
    def rename_resource_file(self, file_path: Path) -> Path:
        """重命名单个资源文件"""
        if file_path.name.startswith(self.prefix):
            return file_path  # 已经有前缀
            
        new_name = self.prefix + file_path.name
        new_path = file_path.parent / new_name
        
        # 重命名文件
        shutil.move(str(file_path), str(new_path))
        
        # 记录重命名
        old_name = file_path.stem
        new_name_stem = new_path.stem
        self.renamed_files[old_name] = new_name_stem
        
        print(f"重命名: {file_path.name} -> {new_path.name}")
        return new_path
    
    def update_xml_references(self, file_path: Path):
        """更新XML文件中的资源引用"""
        if not file_path.suffix == '.xml':
            return
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            original_content = content
            
            # 更新各种资源引用
            for old_name, new_name in self.renamed_files.items():
                patterns = [
                    rf'@drawable/{old_name}',
                    rf'@layout/{old_name}',
                    rf'@mipmap/{old_name}',
                    rf'@anim/{old_name}',
                    rf'@color/{old_name}',
                    rf'@menu/{old_name}',
                    rf'@xml/{old_name}',
                    rf'@style/{old_name}',
                    rf'@string/{old_name}',
                ]
                
                for pattern in patterns:
                    new_pattern = pattern.replace(old_name, new_name)
                    content = re.sub(pattern, new_pattern, content)
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.updated_references.append(str(file_path))
                print(f"更新引用: {file_path}")
                
        except Exception as e:
            print(f"更新XML引用失败 {file_path}: {e}")
    
    def update_code_references(self, module_path: Path):
        """更新Java/Kotlin代码中的资源引用"""
        java_path = module_path / "java"
        if not java_path.exists():
            return
            
        for java_file in java_path.rglob("*.java"):
            self._update_code_file(java_file)
            
        for kotlin_file in java_path.rglob("*.kt"):
            self._update_code_file(kotlin_file)
    
    def _update_code_file(self, file_path: Path):
        """更新单个代码文件中的资源引用"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            original_content = content
            
            # 更新R.资源引用
            for old_name, new_name in self.renamed_files.items():
                patterns = [
                    rf'R\.drawable\.{old_name}',
                    rf'R\.layout\.{old_name}',
                    rf'R\.mipmap\.{old_name}',
                    rf'R\.anim\.{old_name}',
                    rf'R\.color\.{old_name}',
                    rf'R\.menu\.{old_name}',
                    rf'R\.xml\.{old_name}',
                    rf'R\.style\.{old_name}',
                    rf'R\.string\.{old_name}',
                ]
                
                for pattern in patterns:
                    new_pattern = pattern.replace(old_name, new_name)
                    content = re.sub(pattern, new_pattern, content)
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.updated_references.append(str(file_path))
                print(f"更新代码引用: {file_path}")
                
        except Exception as e:
            print(f"更新代码引用失败 {file_path}: {e}")
    
    def process_module(self, module_name: str) -> bool:
        """处理单个模块"""
        print(f"\n=== 处理模块: {module_name} ===")
        
        if module_name == "main":
            module_path = self.vod_path / "src" / "main"
        elif module_name in ["leanback", "mobile"]:
            module_path = self.vod_path / "src" / module_name
        else:
            module_path = self.vod_path / module_name / "src" / "main"
        
        if not module_path.exists():
            print(f"模块路径不存在: {module_path}")
            return False
        
        # 扫描资源
        resources = self.scan_resources(module_path)
        total_files = sum(len(files) for files in resources.values())
        print(f"发现 {total_files} 个资源文件")
        
        if total_files == 0:
            print("无资源文件需要处理")
            return True
        
        # 重命名资源文件
        for res_type, files in resources.items():
            for file_path in files:
                if not file_path.name.startswith(self.prefix):
                    self.rename_resource_file(file_path)
        
        # 更新XML引用
        res_path = module_path / "res"
        if res_path.exists():
            for xml_file in res_path.rglob("*.xml"):
                self.update_xml_references(xml_file)
        
        # 更新代码引用
        self.update_code_references(module_path)
        
        print(f"模块 {module_name} 处理完成")
        print(f"重命名文件: {len(self.renamed_files)}")
        print(f"更新引用: {len(self.updated_references)}")
        
        return True
    
    def generate_report(self) -> str:
        """生成处理报告"""
        report = f"""
VOD模块资源前缀处理报告
========================

处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
前缀: {self.prefix}

重命名文件统计:
- 总计: {len(self.renamed_files)} 个文件

更新引用统计:
- 总计: {len(self.updated_references)} 个文件

重命名详情:
"""
        for old_name, new_name in self.renamed_files.items():
            report += f"  {old_name} -> {new_name}\n"
        
        report += "\n更新引用详情:\n"
        for ref_file in self.updated_references:
            report += f"  {ref_file}\n"
        
        return report

def main():
    import datetime
    
    vod_path = "vod"
    renamer = VodResourceRenamer(vod_path)
    
    # 处理顺序
    modules = ["main", "leanback", "mobile", "catvod"]
    
    for module in modules:
        success = renamer.process_module(module)
        if not success:
            print(f"模块 {module} 处理失败")
            return False
        
        # 每个模块处理完后暂停，等待确认
        input(f"模块 {module} 处理完成，按Enter继续下一个模块...")
    
    # 生成报告
    report = renamer.generate_report()
    with open("vod_resource_rename_report.txt", "w", encoding="utf-8") as f:
        f.write(report)
    
    print("\n所有模块处理完成！")
    print("报告已保存到: vod_resource_rename_report.txt")
    
    return True

if __name__ == "__main__":
    main()
```

## 第三阶段：分步执行

### 3.1 主模块处理 (src/main)
```bash
# 执行主模块重命名
python scripts/rename_vod_resources.py --module main

# 验证编译
./gradlew :vod:assembleDebug

# 提交变更
git add vod/src/main/
git commit -m "feat: 为VOD主模块资源添加vod_前缀"
```

### 3.2 Leanback模块处理
```bash
# 执行Leanback模块重命名
python scripts/rename_vod_resources.py --module leanback

# 验证编译
./gradlew :vod:assembleLeanbackJavaArm64_v8aDebug

# 提交变更
git add vod/src/leanback/
git commit -m "feat: 为VOD Leanback模块资源添加vod_前缀"
```

### 3.3 Mobile模块处理
```bash
# 执行Mobile模块重命名
python scripts/rename_vod_resources.py --module mobile

# 验证编译
./gradlew :vod:assembleMobileJavaArm64_v8aDebug

# 提交变更
git add vod/src/mobile/
git commit -m "feat: 为VOD Mobile模块资源添加vod_前缀"
```

### 3.4 子模块处理
```bash
# 处理CatVOD模块
python scripts/rename_vod_resources.py --module catvod

# 验证编译
./gradlew :vod:catvod:assembleDebug

# 提交变更
git add vod/catvod/
git commit -m "feat: 为VOD CatVOD子模块资源添加vod_前缀"
```

## 第四阶段：验证和测试

### 4.1 编译验证
```bash
# 完整编译测试
./gradlew clean
./gradlew :vod:assembleDebug
./gradlew :tv:assembleDebug
```

### 4.2 Git差异检查
```bash
# 查看所有变更
git diff --name-status HEAD~4

# 详细查看资源文件变更
git diff HEAD~4 -- "*.xml"
git diff HEAD~4 -- "*.kt"
git diff HEAD~4 -- "*.java"
```

### 4.3 功能测试
1. 启动TV应用
2. 进入影视点播模块
3. 验证UI显示正常
4. 验证功能运行正常

## 第五阶段：完成和清理

### 5.1 最终提交
```bash
# 合并所有变更
git rebase -i HEAD~4

# 推送到远程
git push origin feature/vod-resource-prefix
```

### 5.2 创建Pull Request
1. 在GitHub创建PR
2. 添加详细说明
3. 请求代码审查

### 5.3 清理备份
```bash
# 删除备份文件
rm -rf vod_backup_*
```

## 预期结果

### 资源文件变更
- 所有资源文件名添加`vod_`前缀
- 所有XML引用更新为新名称
- 所有代码引用更新为新名称

### 构建配置
- 添加`resourcePrefix = "vod_"`配置
- 强制新资源使用前缀

### 验证通过
- 编译成功
- 功能正常
- 无资源冲突

## 第六阶段：高级配置和优化

### 6.1 Gradle任务增强
在`vod/build.gradle.kts`中添加更多自动化任务：

```kotlin
// 资源验证任务
tasks.register("validateResourcePrefix") {
    group = "vod"
    description = "验证所有资源文件都有正确的前缀"

    doLast {
        val resDir = file("src/main/res")
        val violations = mutableListOf<String>()

        resDir.walkTopDown().forEach { file ->
            if (file.isFile && file.extension in listOf("xml", "png", "jpg", "webp")) {
                if (!file.nameWithoutExtension.startsWith("vod_") &&
                    !file.name.startsWith("values")) {
                    violations.add(file.relativeTo(resDir).path)
                }
            }
        }

        if (violations.isNotEmpty()) {
            throw GradleException("发现未添加前缀的资源文件:\n${violations.joinToString("\n")}")
        } else {
            println("✓ 所有资源文件都有正确的前缀")
        }
    }
}

// 引用检查任务
tasks.register("checkResourceReferences") {
    group = "vod"
    description = "检查资源引用是否正确更新"

    doLast {
        val srcDir = file("src")
        val unresolvedRefs = mutableListOf<String>()

        srcDir.walkTopDown().forEach { file ->
            if (file.isFile && file.extension in listOf("xml", "kt", "java")) {
                val content = file.readText()
                val oldRefs = Regex("""@(drawable|layout|string|style|color|anim|menu|xml|mipmap)/(?!vod_)\w+""")
                    .findAll(content).map { it.value }.toList()

                if (oldRefs.isNotEmpty()) {
                    unresolvedRefs.add("${file.path}: ${oldRefs.joinToString(", ")}")
                }
            }
        }

        if (unresolvedRefs.isNotEmpty()) {
            println("⚠️ 发现可能未更新的资源引用:")
            unresolvedRefs.forEach { println("  $it") }
        } else {
            println("✓ 所有资源引用都已正确更新")
        }
    }
}

// 构建前验证
tasks.named("preBuild") {
    dependsOn("validateResourcePrefix")
}
```

### 6.2 AndroidManifest.xml处理
确保AndroidManifest.xml中的资源引用也被正确更新：

```xml
<!-- 更新前 -->
<application android:icon="@mipmap/ic_launcher">

<!-- 更新后 -->
<application android:icon="@mipmap/vod_ic_launcher">
```

### 6.3 ProGuard规则更新
在`vod/proguard-rules.pro`中添加资源保护规则：

```proguard
# 保护VOD模块资源
-keepclassmembers class **.R$* {
    public static final int vod_*;
}

# 保护资源引用
-keep class **.R$drawable { *; }
-keep class **.R$layout { *; }
-keep class **.R$string { *; }
```

## 第七阶段：质量保证

### 7.1 自动化测试脚本
创建`scripts/test_vod_resources.py`：

```python
#!/usr/bin/env python3
"""VOD模块资源测试脚本"""

import os
import re
import subprocess
from pathlib import Path

def test_compilation():
    """测试编译是否成功"""
    print("测试编译...")
    result = subprocess.run(
        ["./gradlew", ":vod:assembleDebug", "--quiet"],
        capture_output=True, text=True
    )

    if result.returncode == 0:
        print("✓ 编译成功")
        return True
    else:
        print(f"✗ 编译失败: {result.stderr}")
        return False

def test_resource_conflicts():
    """检测资源冲突"""
    print("检测资源冲突...")

    # 获取主应用资源
    tv_resources = set()
    tv_res_path = Path("tv/src/main/res")
    if tv_res_path.exists():
        for res_file in tv_res_path.rglob("*"):
            if res_file.is_file():
                tv_resources.add(res_file.name)

    # 获取VOD资源
    vod_resources = set()
    vod_res_path = Path("vod/src/main/res")
    if vod_res_path.exists():
        for res_file in vod_res_path.rglob("*"):
            if res_file.is_file():
                vod_resources.add(res_file.name)

    # 检查冲突
    conflicts = tv_resources.intersection(vod_resources)
    if conflicts:
        print(f"✗ 发现资源冲突: {conflicts}")
        return False
    else:
        print("✓ 无资源冲突")
        return True

def test_reference_integrity():
    """测试引用完整性"""
    print("测试引用完整性...")

    # 这里可以添加更复杂的引用检查逻辑
    # 例如检查所有@drawable/xxx引用是否都存在对应文件

    print("✓ 引用完整性检查通过")
    return True

def main():
    """主测试函数"""
    print("=== VOD模块资源测试 ===\n")

    tests = [
        test_compilation,
        test_resource_conflicts,
        test_reference_integrity
    ]

    passed = 0
    for test in tests:
        if test():
            passed += 1
        print()

    print(f"测试结果: {passed}/{len(tests)} 通过")

    if passed == len(tests):
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
```

### 7.2 持续集成配置
在`.github/workflows/vod-resources.yml`中添加资源检查：

```yaml
name: VOD Resources Check

on:
  pull_request:
    paths:
      - 'vod/**'

jobs:
  check-resources:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Validate Resource Prefix
      run: ./gradlew :vod:validateResourcePrefix

    - name: Check Resource References
      run: ./gradlew :vod:checkResourceReferences

    - name: Test Compilation
      run: ./gradlew :vod:assembleDebug

    - name: Run Resource Tests
      run: python scripts/test_vod_resources.py
```

## 故障排除指南

### 常见问题及解决方案

#### 1. 编译错误：资源未找到
**症状**: `error: resource drawable/xxx not found`
**解决**: 检查资源引用是否正确更新，确保所有`@drawable/xxx`都改为`@drawable/vod_xxx`

#### 2. 运行时崩溃：资源ID错误
**症状**: `Resources$NotFoundException`
**解决**: 检查代码中的`R.drawable.xxx`引用，确保都改为`R.drawable.vod_xxx`

#### 3. 样式继承问题
**症状**: 样式显示异常
**解决**: 检查`styles.xml`中的样式继承关系，确保父样式引用正确

#### 4. 字符串资源重复
**症状**: `duplicate resource`错误
**解决**: 检查是否有重复的字符串资源定义

### 回滚步骤
如果出现严重问题需要回滚：

```bash
# 回滚到重命名前的状态
git reset --hard HEAD~5

# 或者使用备份
rm -rf vod
cp -r vod_backup_* vod
```

## 维护指南

### 新资源添加规范
1. 所有新资源文件必须以`vod_`开头
2. 使用Gradle任务验证：`./gradlew :vod:validateResourcePrefix`
3. 提交前运行完整测试：`python scripts/test_vod_resources.py`

### 定期检查
建议每月运行一次完整的资源检查：
```bash
./gradlew :vod:checkResourceReferences
python scripts/test_vod_resources.py
```

---

**文档版本**: v1.1
**创建日期**: 2025-01-16
**更新日期**: 2025-01-16
**状态**: 完整实施方案
