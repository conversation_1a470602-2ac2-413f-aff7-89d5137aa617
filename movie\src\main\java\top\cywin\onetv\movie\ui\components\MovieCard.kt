package top.cywin.onetv.movie.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import top.cywin.onetv.movie.ui.model.MovieItem
import top.cywin.onetv.movie.utils.ImgUtil
import androidx.compose.ui.platform.LocalContext

/**
 * 创建带Headers的ImageRequest
 * 🔧 修复：手动解析@Referer和@User-Agent参数，直接添加到ImageRequest
 */
@Composable
private fun createImageRequest(url: String?): ImageRequest? {
    val context = LocalContext.current
    if (url.isNullOrEmpty()) return null

    try {
        // 移除可能的换行符和其他空白字符
        val trimmedUrl = url.trim().replace("\n", "").replace("\r", "")

        if (!trimmedUrl.startsWith("http")) {
            android.util.Log.w("MOVIE_IMAGE", "⚠️ 无效的图片URL: $trimmedUrl")
            return null
        }

        // 🔧 修复：手动解析URL参数
        val baseUrl = trimmedUrl.split("@")[0]
        val requestBuilder = ImageRequest.Builder(context).data(baseUrl)

        // 解析@Referer参数
        val refererMatch = Regex("@Referer=([^@]+)").find(trimmedUrl)
        refererMatch?.let { match ->
            val referer = java.net.URLDecoder.decode(match.groupValues[1], "UTF-8")
            requestBuilder.addHeader("Referer", referer)
            android.util.Log.d("MOVIE_IMAGE", "🔗 添加Referer: $referer")
        }

        // 解析@User-Agent参数
        val userAgentMatch = Regex("@User-Agent=([^@]+)").find(trimmedUrl)
        userAgentMatch?.let { match ->
            val userAgent = java.net.URLDecoder.decode(match.groupValues[1], "UTF-8")
            requestBuilder.addHeader("User-Agent", userAgent)
            android.util.Log.d("MOVIE_IMAGE", "🤖 添加User-Agent: $userAgent")
        }

        // 如果没有找到Headers，添加默认的
        if (refererMatch == null && userAgentMatch == null) {
            requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36")
            requestBuilder.addHeader("Referer", "https://api.douban.com/")
            android.util.Log.d("MOVIE_IMAGE", "🛡️ 添加默认Headers")
        }

        val request = requestBuilder.build()
        android.util.Log.d("MOVIE_IMAGE", "🧹 创建ImageRequest: $url -> $baseUrl")
        return request
    } catch (e: Exception) {
        android.util.Log.e("MOVIE_IMAGE", "❌ 创建ImageRequest失败: $url", e)
        return null
    }
}

/**
 * OneTV Movie电影卡片组件 - 按照FongMi_TV整合指南重构
 */
@Composable
fun MovieCard(
    movie: MovieItem,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    showProgress: Boolean = false,
    progress: Float = 0f,
    showSiteInfo: Boolean = false // 新增参数：是否显示站点信息
) {
    // 🔥 修复：使用Box作为根容器，实现站点信息叠放
    Box(modifier = modifier.fillMaxWidth()) {
        // 主要内容Column
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {

            // TVBOX风格：保留Card边框，电影名称叠加在海报上
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable {
                        android.util.Log.d(
                            "ONETV_MOVIE_CARD",
                            "🎬 MovieCard被点击: ${movie.vodName}"
                        )
                        android.util.Log.d("ONETV_MOVIE_CARD", "📊 影片ID: ${movie.vodId}")
                        android.util.Log.d("ONETV_MOVIE_CARD", "🏷️ 站点Key: ${movie.siteKey}")
                        android.util.Log.d("ONETV_MOVIE_CARD", "🎯 [电影ID跟踪] 真实电影ID: ${movie.vodId}")
                        try {
                            // 检查电影数据的有效性
                            if (movie.vodId.isBlank() || movie.vodName.isBlank()) {
                                android.util.Log.w(
                                    "ONETV_MOVIE_CARD",
                                    "⚠️ 电影数据不完整，跳过点击: ${movie.vodName}"
                                )
                                return@clickable
                            }

                            // 检查是否为明显的无效数据
                            if (movie.vodId == "no_data" || movie.vodName.contains("无数据") || movie.vodName.contains(
                                    "不要点"
                                )
                            ) {
                                android.util.Log.w(
                                    "ONETV_MOVIE_CARD",
                                    "⚠️ 检测到无效数据，跳过点击: ${movie.vodName}"
                                )
                                return@clickable
                            }

                            // 对电影ID进行URL编码处理
                            val encodedVodId = java.net.URLEncoder.encode(movie.vodId, "UTF-8")
                            android.util.Log.d("ONETV_MOVIE_CARD", "🔄 编码后的ID: $encodedVodId")

                            onClick()
                        } catch (e: Exception) {
                            android.util.Log.e(
                                "ONETV_MOVIE_CARD",
                                "❌ 电影点击处理异常: ${movie.vodName}",
                                e
                            )
                            // 不崩溃，只是记录错误
                        }
                    },
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                shape = RoundedCornerShape(8.dp)
            ) {
                // 电影海报背景
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(0.7f)
                        .clip(RoundedCornerShape(8.dp))
                ) {
                    AsyncImage(
                        model = createImageRequest(movie.vodPic),
                        contentDescription = movie.vodName,
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop,
                        error = painterResource(android.R.drawable.ic_menu_gallery),
                        placeholder = painterResource(android.R.drawable.ic_menu_gallery),
                        onSuccess = {
                            android.util.Log.d(
                                "MOVIE_IMAGE",
                                "✅ 图片加载成功: ${movie.vodName} - ${movie.vodPic}"
                            )
                        },
                        onError = { error ->
                            android.util.Log.e(
                                "MOVIE_IMAGE",
                                "❌ 图片加载失败: ${movie.vodName} - ${movie.vodPic}"
                            )
                            android.util.Log.e(
                                "MOVIE_IMAGE",
                                "❌ 错误详情: ${error.result.throwable?.message}"
                            )
                        }
                    )

                    // 渐变遮罩层（底部）
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(60.dp)
                            .align(Alignment.BottomCenter)
                            .background(
                                Brush.verticalGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        Color.Black.copy(alpha = 0.8f)
                                    )
                                )
                            )
                    )

                    // 🔥 重新设计的底部信息区域
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.BottomCenter)
                            .padding(horizontal = 8.dp, vertical = 8.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        // 电影信息标签（评分、集数等）- 放在电影名称上方
                        if (movie.vodRemarks.isNotEmpty()) {
                            Box(
                                modifier = Modifier
                                    .background(
                                        Color.Black.copy(alpha = 0.7f),
                                        RoundedCornerShape(4.dp)
                                    )
                                    .padding(horizontal = 6.dp, vertical = 2.dp)
                            ) {
                                Text(
                                    text = movie.vodRemarks,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }

                        // 电影名称（在电影信息下方）
                        Text(
                            text = movie.vodName,
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium,
                            color = Color.White,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }

                    // 🔥 站点信息叠放层 - 叠放在海报内部的上方
                    if (showSiteInfo) {
                        Box(
                            modifier = Modifier
                                .align(Alignment.TopCenter)  // 叠放在海报Box的顶部中央
                                .fillMaxWidth()
                                .background(
                                    color = Color.Black.copy(alpha = 0.7f),  // 半透明黑色背景
                                    shape = RoundedCornerShape(bottomStart = 4.dp, bottomEnd = 4.dp)
                                )
                                .padding(horizontal = 6.dp, vertical = 2.dp)
                        ) {
                            Text(
                                text = try {
                                    val site = top.cywin.onetv.movie.api.config.VodConfig.get().getSite(movie.siteKey)
                                    site?.name ?: movie.siteKey.replace("onetv_", "").replace("_", " ")
                                } catch (e: Exception) {
                                    android.util.Log.w("MovieCard", "获取站点名称失败: ${movie.siteKey}", e)
                                    movie.siteKey.replace("onetv_", "").replace("_", " ")
                                },
                                style = MaterialTheme.typography.labelSmall,
                                color = Color.White,
                                fontSize = 10.sp,
                                fontWeight = FontWeight.Medium,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.align(Alignment.Center)
                            )
                        }
                    }

                    // 播放进度条
                    if (showProgress && progress > 0) {
                        LinearProgressIndicator(
                            progress = { progress },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(3.dp)
                                .align(Alignment.BottomCenter),
                            color = MaterialTheme.colorScheme.primary,
                            trackColor = Color.White.copy(alpha = 0.3f)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun MovieListCard(
    movie: MovieItem,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    showProgress: Boolean = false,
    progress: Float = 0f
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 电影海报
            Box(
                modifier = Modifier
                    .width(80.dp)
                    .height(120.dp)
            ) {
                AsyncImage(
                    model = createImageRequest(movie.vodPic),
                    contentDescription = movie.vodName,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop,
                    error = null,
                    placeholder = null
                )

                // 播放进度条
                if (showProgress && progress > 0) {
                    LinearProgressIndicator(
                        progress = { progress },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(3.dp)
                            .align(Alignment.BottomCenter),
                        color = MaterialTheme.colorScheme.primary,
                        trackColor = Color.White.copy(alpha = 0.3f)
                    )
                }
            }

            // 电影信息
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = movie.vodName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )

                if (movie.vodRemarks.isNotEmpty()) {
                    Text(
                        text = movie.vodRemarks,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                if (movie.vodContent.isNotEmpty()) {
                    Text(
                        text = movie.vodContent,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 3,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                Row(
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    if (movie.vodYear.isNotEmpty()) {
                        Text(
                            text = "${movie.vodYear}年",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    if (movie.vodArea.isNotEmpty()) {
                        Text(
                            text = "地区: ${movie.vodArea}",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        }
    }
}


