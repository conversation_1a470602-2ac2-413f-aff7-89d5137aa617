# Film 模块结构调整实施计划（渐进式）

**文档版本**: v1.0  
**创建日期**: 2025-07-13  
**基于报告**: 《17_Film模块结构对比分析报告_更新版_20250713》  
**调整方法**: 参考《15_Film模块结构调整实施计划_20250712.md》  

## 🎯 总体目标

完成 Film 模块剩余的待实现功能，实现 100% FongMi/TV 标准兼容，采用渐进式调整方法确保系统稳定性。

## 📋 待实现功能清单

### 🔥 高优先级任务
1. **原生代码完善** - cpp/ 路径中的待实现文件
2. **目录结构优化** - specialized 整合到 custom
3. **数据模型重构** - models/ 路径按 FongMi/TV 标准调整
4. **数据层完善** - api/database 按 FongMi/TV 标准实现
5. **Schema 实现** - Room 数据库 Schema
6. **网络拦截器** - 3个待实现的拦截器

## 🚀 分阶段实施计划

### 第一阶段：目录结构优化（第1天）

#### 1.1 specialized 目录整合到 custom
**目标**: 将 specialized 目录中的文件移动到 custom 目录，符合 FongMi/TV 标准

**操作步骤**:
```bash
# 1. 移动文件
film/src/main/java/top/cywin/onetv/film/spider/custom/
├── YydsAli1Spider.kt         # 从 specialized 移动
├── CokemvSpider.kt           # 从 specialized 移动
├── AueteSpider.kt            # 从 specialized 移动
└── SpecializedSpider.kt      # 重命名为 CustomSpider.kt
```

**实施方法**:
1. 移动文件到 custom 目录
2. 更新包名引用
3. 删除空的 specialized 目录
4. 更新相关导入语句

#### 1.2 验证测试
- 编译测试确保无错误
- 检查所有引用是否正确更新

### 第二阶段：数据模型重构（第2天）

#### 2.1 models 目录按 FongMi/TV 标准重构
**目标**: 将合并的模型文件拆分为独立文件，符合 FongMi/TV 标准

**当前状态**:
```
models/
├── VodSite.kt                # ✅ 保持不变
├── XPathConfig.kt            # ✅ 保持不变
├── VodModels.kt              # 🔄 需要拆分
├── PlayModels.kt             # 🔄 需要拆分
└── ConfigModels.kt           # 🔄 需要拆分
```

**目标状态**:
```
models/
├── VodSite.kt                # ✅ 已创建
├── XPathConfig.kt            # ✅ 已创建
├── VodResponse.kt            # 🆕 从 VodModels.kt 提取
├── VodItem.kt                # 🆕 从 VodModels.kt 提取
├── VodConfigResponse.kt      # 🆕 从 ConfigModels.kt 提取
├── AppYsResponse.kt          # 🆕 从 VodModels.kt 提取
├── PlayerResult.kt           # 🆕 从 PlayModels.kt 提取
├── VodModels.kt              # 🔄 保留剩余通用模型
├── PlayModels.kt             # 🔄 保留剩余播放模型
└── ConfigModels.kt           # 🔄 保留剩余配置模型
```

**实施方法**:
1. 分析现有合并文件的内容
2. 按照 FongMi/TV 标准提取独立模型
3. 创建新的独立文件
4. 更新所有引用
5. 保留原文件中的其他模型

#### 2.2 验证测试
- 编译测试确保模型正确分离
- 检查所有数据模型引用

### 第三阶段：数据层完善（第3天）

#### 3.1 API 服务层实现
**目标**: 实现标准的 API 服务接口

```kotlin
// data/api/FilmApiService.kt
interface FilmApiService {
    suspend fun getConfig(url: String): ConfigResponse
    suspend fun getHomeContent(siteKey: String): VodResponse
    suspend fun getCategoryContent(siteKey: String, tid: String, pg: String): VodResponse
    suspend fun getDetailContent(siteKey: String, ids: List<String>): VodResponse
    suspend fun getSearchContent(siteKey: String, keyword: String): VodResponse
    suspend fun getPlayerContent(siteKey: String, flag: String, id: String): PlayerResult
}

// data/api/ConfigApiService.kt
interface ConfigApiService {
    suspend fun loadConfig(url: String): String
    suspend fun parseConfig(content: String): List<VodSite>
    suspend fun validateConfig(sites: List<VodSite>): Boolean
}
```

#### 3.2 数据库层实现
**目标**: 实现 Room 数据库支持

```kotlin
// data/database/FilmDatabase.kt
@Database(
    entities = [
        VodSiteEntity::class,
        VodItemEntity::class,
        SearchHistoryEntity::class,
        WatchHistoryEntity::class
    ],
    version = 1,
    exportSchema = true
)
abstract class FilmDatabase : RoomDatabase() {
    abstract fun vodSiteDao(): VodSiteDao
    abstract fun vodItemDao(): VodItemDao
    abstract fun searchHistoryDao(): SearchHistoryDao
    abstract fun watchHistoryDao(): WatchHistoryDao
}
```

#### 3.3 DAO 层实现
```kotlin
// data/database/dao/VodSiteDao.kt
@Dao
interface VodSiteDao {
    @Query("SELECT * FROM vod_sites")
    suspend fun getAllSites(): List<VodSiteEntity>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSites(sites: List<VodSiteEntity>)
    
    @Delete
    suspend fun deleteSite(site: VodSiteEntity)
}
```

#### 3.4 实体类实现
```kotlin
// data/database/entities/VodSiteEntity.kt
@Entity(tableName = "vod_sites")
data class VodSiteEntity(
    @PrimaryKey val key: String,
    val name: String,
    val api: String,
    val type: Int,
    val searchable: Int,
    val quickSearch: Int,
    val filterable: Int
)
```

### 第四阶段：Schema 和网络层完善（第4天）

#### 4.1 Room Schema 实现
**目标**: 创建数据库版本管理

```
schemas/
└── top.cywin.onetv.film.data.database.FilmDatabase/
    └── 1.json                # 数据库 Schema 版本 1
```

#### 4.2 网络拦截器实现
**目标**: 实现3个待实现的网络拦截器

```kotlin
// network/HeaderInterceptor.kt
class HeaderInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request().newBuilder()
            .addHeader("User-Agent", "Mozilla/5.0...")
            .addHeader("Accept", "*/*")
            .build()
        return chain.proceed(request)
    }
}

// network/ProxyInterceptor.kt
class ProxyInterceptor(private val proxyManager: ProxyManager) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        // 代理处理逻辑
    }
}

// network/CacheInterceptor.kt
class CacheInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        // 缓存处理逻辑
    }
}
```

### 第五阶段：原生代码完善（第5天）

#### 5.1 C++ 桥接文件实现
**目标**: 实现3个待实现的 C++ 桥接文件

```cpp
// src/main/cpp/jsoup-bridge.cpp
// Jsoup HTML 解析桥接
#include <jni.h>
#include <string>

extern "C" JNIEXPORT jstring JNICALL
Java_top_cywin_onetv_film_engine_XPathEngine_parseHtml(
    JNIEnv *env, jobject thiz, jstring html, jstring xpath) {
    // Jsoup 解析实现
}

// src/main/cpp/http-bridge.cpp  
// HTTP 请求桥接
extern "C" JNIEXPORT jstring JNICALL
Java_top_cywin_onetv_film_network_EnhancedOkHttpManager_nativeRequest(
    JNIEnv *env, jobject thiz, jstring url, jstring headers) {
    // HTTP 请求实现
}

// src/main/cpp/spider-bridge.cpp
// Spider 解析桥接
extern "C" JNIEXPORT jstring JNICALL
Java_top_cywin_onetv_film_spider_base_Spider_nativeParse(
    JNIEnv *env, jobject thiz, jstring content, jstring rules) {
    // Spider 解析实现
}
```

#### 5.2 CMakeLists.txt 更新
```cmake
# 添加新的源文件
add_library(
    film-native
    SHARED
    quickjs-onetv.cpp
    jsoup-bridge.cpp
    http-bridge.cpp
    spider-bridge.cpp
)
```

## 🔧 实施方法和注意事项

### 渐进式调整原则
1. **分步实施** - 每个阶段独立完成和测试
2. **保持兼容** - 调整过程中保持现有功能正常
3. **及时测试** - 每个阶段完成后立即测试
4. **回滚准备** - 每个阶段前备份关键文件

### 风险控制
1. **备份策略** - 调整前创建分支备份
2. **测试验证** - 每步调整后编译测试
3. **依赖检查** - 确保所有引用正确更新
4. **功能验证** - 核心功能保持正常

### 质量保证
1. **代码规范** - 遵循项目编码标准
2. **文档更新** - 及时更新相关文档
3. **性能考虑** - 确保调整不影响性能
4. **兼容性** - 保持与现有系统兼容

## 📊 预期成果

### 完成后的改进
1. **标准兼容性**: 100% 符合 FongMi/TV 标准
2. **代码组织**: 更清晰的模块化结构
3. **功能完整性**: 所有核心功能完整实现
4. **可维护性**: 更好的代码可维护性

### 成功指标
- [ ] 所有待实现文件创建完成
- [ ] 编译无错误无警告
- [ ] 核心功能测试通过
- [ ] 目录结构符合标准
- [ ] 文档更新完整

## 🎯 下一步行动

1. **立即开始第一阶段** - 目录结构优化
2. **准备测试环境** - 确保测试工具就绪
3. **创建备份分支** - 保证回滚能力
4. **制定详细时间表** - 每个阶段的具体时间安排

**🚀 让我们以破釜沉舟的勇气，完成 Film 模块的最后冲刺！**
