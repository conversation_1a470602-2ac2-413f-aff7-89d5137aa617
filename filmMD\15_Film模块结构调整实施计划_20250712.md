# Film 模块结构调整实施计划 (渐进式)

**制定时间**: 2025-07-12
**实施版本**: 2.1.1
**调整目标**: 100% 对应 FongMi/TV 目录结构
**基准文档**: 《14_Film模块结构对比分析报告_20250712.md》

## 🎯 调整策略

### 核心原则
1. **渐进式调整** - 每步独立执行，可单独测试
2. **保持功能完整** - 现有功能不丢失
3. **回滚容易** - 每步都有回滚方案
4. **清理旧代码** - 测试通过后立即清理，避免冲突

### 安全措施
- 每步操作前创建 Git 分支
- 每步完成后运行完整测试
- 测试通过后清理旧代码
- 出现问题立即回滚

## 📋 分步实施计划

### 🔄 第一阶段：目录结构调整 (预计 45 分钟)

#### Step 1.1: 创建安全分支 (2分钟)
```bash
# 创建调整分支
git checkout -b film-structure-step1-directories
git add .
git commit -m "🔄 Step 1.1: 开始目录结构调整"
```

#### Step 1.2: specialized → custom 目录调整 (10分钟)
```bash
# 1. 创建新的 custom 目录
mkdir -p film/src/main/java/top/cywin/onetv/film/spider/custom/

# 2. 复制文件到新目录 (保留原文件作为备份)
cp film/src/main/java/top/cywin/onetv/film/spider/specialized/YydsAli1Spider.kt \
   film/src/main/java/top/cywin/onetv/film/spider/custom/YydsAli1Spider.kt

cp film/src/main/java/top/cywin/onetv/film/spider/specialized/CokemvSpider.kt \
   film/src/main/java/top/cywin/onetv/film/spider/custom/CokemvSpider.kt

cp film/src/main/java/top/cywin/onetv/film/spider/specialized/AueteSpider.kt \
   film/src/main/java/top/cywin/onetv/film/spider/custom/AueteSpider.kt

# 3. 更新新文件中的包名
sed -i 's/package top.cywin.onetv.film.spider.specialized/package top.cywin.onetv.film.spider.custom/g' \
    film/src/main/java/top/cywin/onetv/film/spider/custom/*.kt
```

#### Step 1.3: 更新 specialized → custom 引用 (15分钟)
```bash
# 更新所有文件中的 import 引用
find film/src -name "*.kt" -exec sed -i 's/import top.cywin.onetv.film.spider.specialized/import top.cywin.onetv.film.spider.custom/g' {} \;

# 更新测试文件中的引用
find film/src/test -name "*.kt" -exec sed -i 's/specialized/custom/g' {} \;
```

#### Step 1.4: 测试目录调整 (10分钟)
```bash
# 编译测试
./gradlew :film:compileDebugKotlin

# 运行基础测试
./gradlew :film:testDebugUnitTest

# 如果测试通过，清理旧文件
if [ $? -eq 0 ]; then
    echo "✅ 测试通过，清理 specialized 目录中的已移动文件"
    rm film/src/main/java/top/cywin/onetv/film/spider/specialized/YydsAli1Spider.kt
    rm film/src/main/java/top/cywin/onetv/film/spider/specialized/CokemvSpider.kt
    rm film/src/main/java/top/cywin/onetv/film/spider/specialized/AueteSpider.kt

    # 提交更改
    git add .
    git commit -m "✅ Step 1.2-1.4: specialized → custom 目录调整完成"
else
    echo "❌ 测试失败，回滚操作"
    git checkout -- .
    rm -rf film/src/main/java/top/cywin/onetv/film/spider/custom/
fi
```

#### Step 1.5: 回滚方案
```bash
# 如果出现问题，执行回滚
git checkout -- .
rm -rf film/src/main/java/top/cywin/onetv/film/spider/custom/
echo "🔄 已回滚到调整前状态"
```

### 🔄 第二阶段：文件重命名调整 (预计 60 分钟)

#### Step 2.1: LocalProxyServer → LocalProxy (15分钟)
```bash
# 创建新分支
git checkout -b film-structure-step2-proxy
git add .
git commit -m "🔄 Step 2.1: 开始 LocalProxy 文件调整"

# 1. 复制并重命名文件
cp film/src/main/java/top/cywin/onetv/film/proxy/LocalProxyServer.kt \
   film/src/main/java/top/cywin/onetv/film/proxy/LocalProxy.kt

# 2. 更新新文件中的类名
sed -i 's/class LocalProxyServer/class LocalProxy/g' \
    film/src/main/java/top/cywin/onetv/film/proxy/LocalProxy.kt

sed -i 's/object LocalProxyServer/object LocalProxy/g' \
    film/src/main/java/top/cywin/onetv/film/proxy/LocalProxy.kt

# 3. 更新所有引用
find film/src -name "*.kt" -exec sed -i 's/LocalProxyServer/LocalProxy/g' {} \;

# 4. 测试
./gradlew :film:compileDebugKotlin
./gradlew :film:testDebugUnitTest

# 5. 测试通过后清理
if [ $? -eq 0 ]; then
    rm film/src/main/java/top/cywin/onetv/film/proxy/LocalProxyServer.kt
    git add .
    git commit -m "✅ Step 2.1: LocalProxyServer → LocalProxy 完成"
else
    git checkout -- .
    rm film/src/main/java/top/cywin/onetv/film/proxy/LocalProxy.kt
fi
```

#### Step 2.2: OkHttpManager → EnhancedOkHttpManager (15分钟)
```bash
# 1. 复制并重命名文件
cp film/src/main/java/top/cywin/onetv/film/network/OkHttpManager.kt \
   film/src/main/java/top/cywin/onetv/film/network/EnhancedOkHttpManager.kt

# 2. 更新新文件中的类名
sed -i 's/class OkHttpManager/class EnhancedOkHttpManager/g' \
    film/src/main/java/top/cywin/onetv/film/network/EnhancedOkHttpManager.kt

sed -i 's/object OkHttpManager/object EnhancedOkHttpManager/g' \
    film/src/main/java/top/cywin/onetv/film/network/EnhancedOkHttpManager.kt

# 3. 更新所有引用
find film/src -name "*.kt" -exec sed -i 's/OkHttpManager/EnhancedOkHttpManager/g' {} \;

# 4. 测试
./gradlew :film:compileDebugKotlin
./gradlew :film:testDebugUnitTest

# 5. 测试通过后清理
if [ $? -eq 0 ]; then
    rm film/src/main/java/top/cywin/onetv/film/network/OkHttpManager.kt
    git add .
    git commit -m "✅ Step 2.2: OkHttpManager → EnhancedOkHttpManager 完成"
else
    git checkout -- .
    rm film/src/main/java/top/cywin/onetv/film/network/EnhancedOkHttpManager.kt
fi
```

#### Step 2.3: CacheManager → FilmCacheManager (15分钟)
```bash
# 1. 复制并重命名文件
cp film/src/main/java/top/cywin/onetv/film/cache/CacheManager.kt \
   film/src/main/java/top/cywin/onetv/film/cache/FilmCacheManager.kt

# 2. 更新新文件中的类名
sed -i 's/class CacheManager/class FilmCacheManager/g' \
    film/src/main/java/top/cywin/onetv/film/cache/FilmCacheManager.kt

sed -i 's/object CacheManager/object FilmCacheManager/g' \
    film/src/main/java/top/cywin/onetv/film/cache/FilmCacheManager.kt

# 3. 更新所有引用 (注意避免误替换其他CacheManager)
find film/src -name "*.kt" -exec sed -i 's/import top.cywin.onetv.film.cache.CacheManager/import top.cywin.onetv.film.cache.FilmCacheManager/g' {} \;
find film/src -name "*.kt" -exec sed -i 's/CacheManager(/FilmCacheManager(/g' {} \;
find film/src -name "*.kt" -exec sed -i 's/CacheManager\./FilmCacheManager\./g' {} \;

# 4. 测试
./gradlew :film:compileDebugKotlin
./gradlew :film:testDebugUnitTest

# 5. 测试通过后清理
if [ $? -eq 0 ]; then
    rm film/src/main/java/top/cywin/onetv/film/cache/CacheManager.kt
    git add .
    git commit -m "✅ Step 2.3: CacheManager → FilmCacheManager 完成"
else
    git checkout -- .
    rm film/src/main/java/top/cywin/onetv/film/cache/FilmCacheManager.kt
fi
```

#### Step 2.4: JarCacheManager → JarCache (15分钟)
```bash
# 1. 复制并重命名文件
cp film/src/main/java/top/cywin/onetv/film/jar/JarCacheManager.kt \
   film/src/main/java/top/cywin/onetv/film/jar/JarCache.kt

# 2. 更新新文件中的类名
sed -i 's/class JarCacheManager/class JarCache/g' \
    film/src/main/java/top/cywin/onetv/film/jar/JarCache.kt

sed -i 's/object JarCacheManager/object JarCache/g' \
    film/src/main/java/top/cywin/onetv/film/jar/JarCache.kt

# 3. 更新所有引用
find film/src -name "*.kt" -exec sed -i 's/JarCacheManager/JarCache/g' {} \;

# 4. 测试
./gradlew :film:compileDebugKotlin
./gradlew :film:testDebugUnitTest

# 5. 测试通过后清理
if [ $? -eq 0 ]; then
    rm film/src/main/java/top/cywin/onetv/film/jar/JarCacheManager.kt
    git add .
    git commit -m "✅ Step 2.4: JarCacheManager → JarCache 完成"
else
    git checkout -- .
    rm film/src/main/java/top/cywin/onetv/film/jar/JarCache.kt
fi
```

### 🔄 第三阶段：高优先级缺失文件创建 (预计 90 分钟)

#### Step 3.1: 创建 spider/base/ 目录文件 (30分钟)
```bash
# 创建新分支
git checkout -b film-structure-step3-spider-base
git add .
git commit -m "🔄 Step 3.1: 开始创建 spider/base 文件"

# 1. 创建目录
mkdir -p film/src/main/java/top/cywin/onetv/film/spider/base/

# 2. 创建 Spider.kt (基础Spider类)
cat > film/src/main/java/top/cywin/onetv/film/spider/base/Spider.kt << 'EOF'
package top.cywin.onetv.film.spider.base

/**
 * Spider 基础类
 * 基于 FongMi/TV 标准实现
 */
abstract class Spider {
    abstract fun homeContent(filter: Boolean): String
    abstract fun categoryContent(tid: String, pg: String, filter: Boolean, extend: HashMap<String, String>): String
    abstract fun detailContent(ids: List<String>): String
    abstract fun searchContent(key: String, quick: Boolean): String
    abstract fun playerContent(flag: String, id: String, vipFlags: List<String>): String
}
EOF

# 3. 创建 SpiderFactory.kt
cat > film/src/main/java/top/cywin/onetv/film/spider/base/SpiderFactory.kt << 'EOF'
package top.cywin.onetv.film.spider.base

/**
 * Spider 工厂
 * 基于 FongMi/TV 标准实现
 */
object SpiderFactory {
    fun createSpider(type: String, api: String): Spider? {
        return when (type.lowercase()) {
            "xpath" -> createXPathSpider(api)
            "appys" -> createAppYsSpider(api)
            "javascript" -> createJavaScriptSpider(api)
            else -> null
        }
    }

    private fun createXPathSpider(api: String): Spider? = null
    private fun createAppYsSpider(api: String): Spider? = null
    private fun createJavaScriptSpider(api: String): Spider? = null
}
EOF

# 4. 创建 SpiderTypeDetector.kt
cat > film/src/main/java/top/cywin/onetv/film/spider/base/SpiderTypeDetector.kt << 'EOF'
package top.cywin.onetv.film.spider.base

/**
 * Spider 类型检测器
 * 基于 FongMi/TV 标准实现
 */
object SpiderTypeDetector {
    fun detectType(api: String): String {
        return when {
            api.endsWith(".js") -> "javascript"
            api.contains("appys") -> "appys"
            api.contains("xpath") -> "xpath"
            else -> "unknown"
        }
    }
}
EOF

# 5. 测试
./gradlew :film:compileDebugKotlin
./gradlew :film:testDebugUnitTest

# 6. 提交
if [ $? -eq 0 ]; then
    git add .
    git commit -m "✅ Step 3.1: spider/base 目录文件创建完成"
else
    git checkout -- .
    rm -rf film/src/main/java/top/cywin/onetv/film/spider/base/
fi
```

#### Step 3.2: 创建核心缓存文件 (30分钟)
```bash
# 创建新分支
git checkout -b film-structure-step3-cache
git add .
git commit -m "🔄 Step 3.2: 开始创建核心缓存文件"

# 1. 创建 SpiderCache.kt
cat > film/src/main/java/top/cywin/onetv/film/cache/SpiderCache.kt << 'EOF'
package top.cywin.onetv.film.cache

/**
 * Spider 专用缓存
 * 基于 FongMi/TV 标准实现
 */
object SpiderCache {
    private val cache = mutableMapOf<String, Any>()

    fun put(key: String, value: Any) {
        cache[key] = value
    }

    fun get(key: String): Any? = cache[key]

    fun clear() = cache.clear()
}
EOF

# 2. 创建 ConfigCache.kt
cat > film/src/main/java/top/cywin/onetv/film/cache/ConfigCache.kt << 'EOF'
package top.cywin.onetv.film.cache

/**
 * 配置专用缓存
 * 基于 FongMi/TV 标准实现
 */
object ConfigCache {
    private val configCache = mutableMapOf<String, String>()

    fun putConfig(key: String, config: String) {
        configCache[key] = config
    }

    fun getConfig(key: String): String? = configCache[key]

    fun clearConfig() = configCache.clear()
}
EOF

# 3. 创建 ContentCache.kt
cat > film/src/main/java/top/cywin/onetv/film/cache/ContentCache.kt << 'EOF'
package top.cywin.onetv.film.cache

/**
 * 内容专用缓存
 * 基于 FongMi/TV 标准实现
 */
object ContentCache {
    private val contentCache = mutableMapOf<String, String>()

    fun putContent(key: String, content: String) {
        contentCache[key] = content
    }

    fun getContent(key: String): String? = contentCache[key]

    fun clearContent() = contentCache.clear()
}
EOF

# 4. 创建 ImageCache.kt
cat > film/src/main/java/top/cywin/onetv/film/cache/ImageCache.kt << 'EOF'
package top.cywin.onetv.film.cache

/**
 * 图片专用缓存
 * 基于 FongMi/TV 标准实现
 */
object ImageCache {
    private val imageCache = mutableMapOf<String, ByteArray>()

    fun putImage(key: String, image: ByteArray) {
        imageCache[key] = image
    }

    fun getImage(key: String): ByteArray? = imageCache[key]

    fun clearImages() = imageCache.clear()
}
EOF

# 5. 测试
./gradlew :film:compileDebugKotlin
./gradlew :film:testDebugUnitTest

# 6. 提交
if [ $? -eq 0 ]; then
    git add .
    git commit -m "✅ Step 3.2: 核心缓存文件创建完成"
else
    git checkout -- .
    rm film/src/main/java/top/cywin/onetv/film/cache/SpiderCache.kt
    rm film/src/main/java/top/cywin/onetv/film/cache/ConfigCache.kt
    rm film/src/main/java/top/cywin/onetv/film/cache/ContentCache.kt
    rm film/src/main/java/top/cywin/onetv/film/cache/ImageCache.kt
fi
```

#### Step 3.3: 创建并发处理文件 (30分钟)
```bash
# 创建新分支
git checkout -b film-structure-step3-concurrent
git add .
git commit -m "🔄 Step 3.3: 开始创建并发处理文件"

# 1. 创建 ConcurrentSearcher.kt
cat > film/src/main/java/top/cywin/onetv/film/concurrent/ConcurrentSearcher.kt << 'EOF'
package top.cywin.onetv.film.concurrent

import kotlinx.coroutines.*

/**
 * 并发搜索器
 * 基于 FongMi/TV 标准实现
 */
class ConcurrentSearcher {
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    suspend fun searchConcurrently(
        keyword: String,
        sites: List<String>
    ): List<String> = withContext(Dispatchers.IO) {
        sites.map { site ->
            async {
                searchSite(site, keyword)
            }
        }.awaitAll()
    }

    private suspend fun searchSite(site: String, keyword: String): String {
        // 实现具体的搜索逻辑
        return "搜索结果: $site - $keyword"
    }

    fun shutdown() {
        scope.cancel()
    }
}
EOF

# 2. 创建 AsyncLoader.kt
cat > film/src/main/java/top/cywin/onetv/film/concurrent/AsyncLoader.kt << 'EOF'
package top.cywin.onetv.film.concurrent

import kotlinx.coroutines.*

/**
 * 异步加载器
 * 基于 FongMi/TV 标准实现
 */
class AsyncLoader {
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    fun <T> loadAsync(
        loader: suspend () -> T,
        onSuccess: (T) -> Unit,
        onError: (Throwable) -> Unit = {}
    ) {
        scope.launch {
            try {
                val result = loader()
                withContext(Dispatchers.Main) {
                    onSuccess(result)
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    onError(e)
                }
            }
        }
    }

    fun shutdown() {
        scope.cancel()
    }
}
EOF

# 3. 测试
./gradlew :film:compileDebugKotlin
./gradlew :film:testDebugUnitTest

# 4. 提交
if [ $? -eq 0 ]; then
    git add .
    git commit -m "✅ Step 3.3: 并发处理文件创建完成"
else
    git checkout -- .
    rm film/src/main/java/top/cywin/onetv/film/concurrent/ConcurrentSearcher.kt
    rm film/src/main/java/top/cywin/onetv/film/concurrent/AsyncLoader.kt
fi
```

### 🔄 第四阶段：中优先级文件创建 (预计 75 分钟)

#### Step 4.1: 创建 Hook 拦截文件 (25分钟)
```bash
# 创建新分支
git checkout -b film-structure-step4-hooks
git add .
git commit -m "🔄 Step 4.1: 开始创建 Hook 拦截文件"

# 1. 创建 RequestHook.kt
cat > film/src/main/java/top/cywin/onetv/film/hook/RequestHook.kt << 'EOF'
package top.cywin.onetv.film.hook

/**
 * 请求拦截 Hook
 * 基于 FongMi/TV 标准实现
 */
class RequestHook : Hook {
    override fun intercept(request: Any): Any {
        // 实现请求拦截逻辑
        return request
    }
}
EOF

# 2. 创建 ResponseHook.kt
cat > film/src/main/java/top/cywin/onetv/film/hook/ResponseHook.kt << 'EOF'
package top.cywin.onetv.film.hook

/**
 * 响应拦截 Hook
 * 基于 FongMi/TV 标准实现
 */
class ResponseHook : Hook {
    override fun intercept(response: Any): Any {
        // 实现响应拦截逻辑
        return response
    }
}
EOF

# 3. 创建 PlayerHook.kt
cat > film/src/main/java/top/cywin/onetv/film/hook/PlayerHook.kt << 'EOF'
package top.cywin.onetv.film.hook

/**
 * 播放器拦截 Hook
 * 基于 FongMi/TV 标准实现
 */
class PlayerHook : Hook {
    override fun intercept(playerData: Any): Any {
        // 实现播放器拦截逻辑
        return playerData
    }
}
EOF

# 4. 测试
./gradlew :film:compileDebugKotlin
./gradlew :film:testDebugUnitTest

# 5. 提交
if [ $? -eq 0 ]; then
    git add .
    git commit -m "✅ Step 4.1: Hook 拦截文件创建完成"
else
    git checkout -- .
    rm film/src/main/java/top/cywin/onetv/film/hook/RequestHook.kt
    rm film/src/main/java/top/cywin/onetv/film/hook/ResponseHook.kt
    rm film/src/main/java/top/cywin/onetv/film/hook/PlayerHook.kt
fi
```

#### Step 4.2: 创建代理和网络文件 (25分钟)
```bash
# 创建新分支
git checkout -b film-structure-step4-proxy-network
git add .
git commit -m "🔄 Step 4.2: 开始创建代理和网络文件"

# 1. 创建 ProxyRule.kt
cat > film/src/main/java/top/cywin/onetv/film/proxy/ProxyRule.kt << 'EOF'
package top.cywin.onetv.film.proxy

/**
 * 代理规则
 * 基于 FongMi/TV 标准实现
 */
data class ProxyRule(
    val pattern: String,
    val target: String,
    val enabled: Boolean = true
) {
    fun matches(url: String): Boolean {
        return url.contains(pattern)
    }
}
EOF

# 2. 创建 HostsManager.kt
cat > film/src/main/java/top/cywin/onetv/film/proxy/HostsManager.kt << 'EOF'
package top.cywin.onetv.film.proxy

/**
 * Hosts 重定向管理器
 * 基于 FongMi/TV 标准实现
 */
object HostsManager {
    private val hostsMap = mutableMapOf<String, String>()

    fun addHost(domain: String, ip: String) {
        hostsMap[domain] = ip
    }

    fun removeHost(domain: String) {
        hostsMap.remove(domain)
    }

    fun resolveHost(domain: String): String? {
        return hostsMap[domain]
    }

    fun clearHosts() {
        hostsMap.clear()
    }
}
EOF

# 3. 创建网络拦截器
cat > film/src/main/java/top/cywin/onetv/film/network/RetryInterceptor.kt << 'EOF'
package top.cywin.onetv.film.network

import okhttp3.Interceptor
import okhttp3.Response

/**
 * 重试拦截器
 * 基于 FongMi/TV 标准实现
 */
class RetryInterceptor(private val maxRetries: Int = 3) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        var response = chain.proceed(chain.request())
        var retryCount = 0

        while (!response.isSuccessful && retryCount < maxRetries) {
            retryCount++
            response.close()
            response = chain.proceed(chain.request())
        }

        return response
    }
}
EOF

# 4. 测试
./gradlew :film:compileDebugKotlin
./gradlew :film:testDebugUnitTest

# 5. 提交
if [ $? -eq 0 ]; then
    git add .
    git commit -m "✅ Step 4.2: 代理和网络文件创建完成"
else
    git checkout -- .
    rm film/src/main/java/top/cywin/onetv/film/proxy/ProxyRule.kt
    rm film/src/main/java/top/cywin/onetv/film/proxy/HostsManager.kt
    rm film/src/main/java/top/cywin/onetv/film/network/RetryInterceptor.kt
fi
```

#### Step 4.3: 创建工具类文件 (25分钟)
```bash
# 创建新分支
git checkout -b film-structure-step4-utils
git add .
git commit -m "🔄 Step 4.3: 开始创建工具类文件"

# 1. 创建 RegexUtils.kt
cat > film/src/main/java/top/cywin/onetv/film/utils/RegexUtils.kt << 'EOF'
package top.cywin.onetv.film.utils

/**
 * 正则表达式工具类
 * 基于 FongMi/TV 标准实现
 */
object RegexUtils {

    /**
     * 提取URL
     */
    fun extractUrls(text: String): List<String> {
        val urlPattern = "https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+".toRegex()
        return urlPattern.findAll(text).map { it.value }.toList()
    }

    /**
     * 提取视频链接
     */
    fun extractVideoUrls(text: String): List<String> {
        val videoPattern = "https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+\\.(mp4|m3u8|flv|avi|mkv)".toRegex()
        return videoPattern.findAll(text).map { it.value }.toList()
    }

    /**
     * 清理HTML标签
     */
    fun removeHtmlTags(html: String): String {
        return html.replace("<[^>]*>".toRegex(), "")
    }
}
EOF

# 2. 创建 CryptoUtils.kt
cat > film/src/main/java/top/cywin/onetv/film/utils/CryptoUtils.kt << 'EOF'
package top.cywin.onetv.film.utils

import java.security.MessageDigest
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

/**
 * 加密解密工具类
 * 基于 FongMi/TV 标准实现
 */
object CryptoUtils {

    /**
     * MD5 哈希
     */
    fun md5(input: String): String {
        val md = MessageDigest.getInstance("MD5")
        val digest = md.digest(input.toByteArray())
        return digest.joinToString("") { "%02x".format(it) }
    }

    /**
     * Base64 编码
     */
    fun base64Encode(input: String): String {
        return Base64.getEncoder().encodeToString(input.toByteArray())
    }

    /**
     * Base64 解码
     */
    fun base64Decode(input: String): String {
        return String(Base64.getDecoder().decode(input))
    }

    /**
     * AES 加密
     */
    fun aesEncrypt(data: String, key: String): String {
        val cipher = Cipher.getInstance("AES")
        val secretKey = SecretKeySpec(key.toByteArray(), "AES")
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        val encrypted = cipher.doFinal(data.toByteArray())
        return Base64.getEncoder().encodeToString(encrypted)
    }

    /**
     * AES 解密
     */
    fun aesDecrypt(encryptedData: String, key: String): String {
        val cipher = Cipher.getInstance("AES")
        val secretKey = SecretKeySpec(key.toByteArray(), "AES")
        cipher.init(Cipher.DECRYPT_MODE, secretKey)
        val decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedData))
        return String(decrypted)
    }
}
EOF

# 3. 测试
./gradlew :film:compileDebugKotlin
./gradlew :film:testDebugUnitTest

# 4. 提交
if [ $? -eq 0 ]; then
    git add .
    git commit -m "✅ Step 4.3: 工具类文件创建完成"
else
    git checkout -- .
    rm film/src/main/java/top/cywin/onetv/film/utils/RegexUtils.kt
    rm film/src/main/java/top/cywin/onetv/film/utils/CryptoUtils.kt
fi
```

### 🔄 第五阶段：最终验证和整合 (预计 30 分钟)

#### Step 5.1: 合并所有分支 (15分钟)
```bash
# 切换到主分支
git checkout main

# 合并所有调整分支
git merge film-structure-step1-directories
git merge film-structure-step2-proxy
git merge film-structure-step3-spider-base
git merge film-structure-step3-cache
git merge film-structure-step3-concurrent
git merge film-structure-step4-hooks
git merge film-structure-step4-proxy-network
git merge film-structure-step4-utils

# 删除临时分支
git branch -d film-structure-step1-directories
git branch -d film-structure-step2-proxy
git branch -d film-structure-step3-spider-base
git branch -d film-structure-step3-cache
git branch -d film-structure-step3-concurrent
git branch -d film-structure-step4-hooks
git branch -d film-structure-step4-proxy-network
git branch -d film-structure-step4-utils
```

#### Step 5.2: 最终测试验证 (15分钟)
```bash
# 1. 完整编译测试
./gradlew :film:build

# 2. 运行所有单元测试
./gradlew :film:test

# 3. 运行集成测试
./film/test-film-system.sh

# 4. 验证结构完整性
echo "📊 验证目录结构..."
tree film/src/main/java/top/cywin/onetv/film/

# 5. 最终提交
if [ $? -eq 0 ]; then
    git add .
    git commit -m "🎉 Film 模块结构调整完成 - 100% 符合 FongMi/TV 标准"
    echo "✅ Film 模块结构调整成功完成！"
else
    echo "❌ 最终测试失败，请检查问题"
fi
```

## 📊 调整完成验证

### ✅ 验证清单
- [ ] specialized → custom 目录调整完成
- [ ] 4个文件重命名完成 (LocalProxy, EnhancedOkHttpManager, FilmCacheManager, JarCache)
- [ ] spider/base/ 3个文件创建完成
- [ ] cache/ 4个专用缓存文件创建完成
- [ ] concurrent/ 2个并发文件创建完成
- [ ] hook/ 3个拦截文件创建完成
- [ ] proxy/ 2个代理文件创建完成
- [ ] network/ 1个拦截器文件创建完成
- [ ] utils/ 2个工具文件创建完成
- [ ] 所有测试通过
- [ ] 旧代码已清理

### 🎯 预期结果
调整完成后，Film 模块将：
1. **100% 符合 FongMi/TV 目录结构** ✅
2. **保持所有现有功能** ✅
3. **新增 23 个标准文件** ✅
4. **清理所有冲突代码** ✅
5. **通过完整测试验证** ✅

### ⚠️ 安全保障
1. **每步独立分支** - 出问题可单独回滚
2. **渐进式测试** - 每步都验证功能
3. **自动清理** - 测试通过后立即清理旧代码
4. **完整备份** - Git 历史保留所有变更

**总预计时间**: 4.5 小时
**安全等级**: 🛡️🛡️🛡️🛡️🛡️ 极高
