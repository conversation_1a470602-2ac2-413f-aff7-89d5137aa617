# FongMi/TV 解析系统移植重构方案

## 项目概述

**目标**: 将 FongMi/TV 项目的完整解析逻辑移植到 OneTV 点播系统  
**原则**: 保持直播系统不变，仅重构点播模块  
**范围**: 完整的 TVBOX 解析生态系统移植  
**日期**: 2025-07-12  

## 1. 现状分析

### 1.1 当前点播系统问题
- **简化版 TVBOX**: 缺少完整的解析器生态
- **解析能力有限**: 无法处理复杂的 TVBOX 接口
- **兼容性不足**: 与标准 TVBOX 配置不完全兼容
- **功能缺失**: 缺少多引擎解析、智能回退等核心功能

### 1.2 直播系统现状（保持不变）
```kotlin
// 直播系统架构（不修改）
core/data/repositories/iptv/
├── IptvRepository.kt           // 直播仓库
├── GuestIptvRepository.kt      // 游客直播仓库
└── parser/
    ├── IptvParser.kt           // 直播解析接口
    ├── M3uIptvParser.kt        // M3U解析器
    ├── TxtIptvParser.kt        // TXT解析器
    └── DefaultIptvParser.kt    // 默认解析器
```

### 1.3 当前点播系统架构
```kotlin
movie/src/main/java/top/cywin/onetv/movie/
├── data/
│   ├── models/                 // 数据模型（需要扩展）
│   ├── parser/                 // 简化解析器（需要重构）
│   ├── repository/             // 仓库层（需要增强）
│   └── api/                    // API服务（需要扩展）
├── codegen/                    // KotlinPoet代码生成（保留）
└── MovieApp.kt                 // 应用入口（需要更新）
```

## 2. 移植架构设计

### 2.1 核心模块架构
```
movie/src/main/java/top/cywin/onetv/movie/
├── spider/                     // 🆕 Spider解析引擎
│   ├── Spider.kt              // 基础Spider接口
│   ├── XPathSpider.kt         // XPath解析器
│   ├── AppYsSpider.kt         // AppYs解析器
│   ├── JavaScriptSpider.kt    // JavaScript解析器
│   └── SpiderManager.kt       // Spider管理器
├── engine/                     // 🆕 解析引擎
│   ├── QuickJSEngine.kt       // JavaScript引擎
│   ├── XPathEngine.kt         // XPath引擎
│   └── EngineManager.kt       // 引擎管理器
├── parser/                     // 🔄 增强解析器
│   ├── ParseManager.kt        // 解析管理器（重构）
│   ├── ConfigParser.kt        // 配置解析器
│   ├── ContentParser.kt       // 内容解析器
│   └── PlayerParser.kt        // 播放器解析器
├── network/                    // 🆕 网络层
│   ├── OkHttpManager.kt       // HTTP客户端
│   ├── ProxyManager.kt        // 代理管理
│   └── HeaderManager.kt       // 请求头管理
├── utils/                      // 🆕 工具类
│   ├── JsoupUtils.kt          // HTML解析工具
│   ├── RegexUtils.kt          // 正则表达式工具
│   └── UrlUtils.kt            // URL处理工具
└── cache/                      // 🔄 缓存系统（增强）
    ├── SpiderCache.kt         // Spider缓存
    ├── ConfigCache.kt         // 配置缓存
    └── ContentCache.kt        // 内容缓存
```

### 2.2 数据模型扩展
```kotlin
// 扩展现有数据模型以支持完整TVBOX功能
data class VodSite(
    // 现有字段保持不变
    val key: String,
    val name: String,
    val type: Int,
    val api: String,
    
    // 新增FongMi/TV完整字段
    val searchable: Int = 1,
    val quickSearch: Int = 1,
    val filterable: Int = 1,
    val changeable: Int = 1,
    val indexs: Int = 0,
    val timeout: Int = 15,
    val playerType: Int = 0,
    val playUrl: String = "",
    val categories: List<String> = emptyList(),
    val jar: String = "",
    val click: String = "",
    val style: Map<String, Any> = emptyMap()
)
```

## 3. 移植实施计划

### 3.1 第一阶段：基础架构移植（1-2天）

#### 3.1.1 Spider引擎移植
```kotlin
// 移植FongMi/TV的Spider基础架构
abstract class Spider {
    abstract suspend fun homeContent(filter: Boolean): String
    abstract suspend fun categoryContent(tid: String, pg: String, filter: Boolean, extend: Map<String, String>): String
    abstract suspend fun detailContent(ids: List<String>): String
    abstract suspend fun searchContent(key: String, quick: Boolean): String
    abstract suspend fun playerContent(flag: String, id: String, vipFlags: List<String>): String
    
    // 工具方法
    protected fun req(url: String, headers: Map<String, String> = emptyMap()): String
    protected fun pdfh(html: String, rule: String): String
    protected fun pdfa(html: String, rule: String): List<String>
}
```

#### 3.1.2 JavaScript引擎集成
```kotlin
// 集成QuickJS引擎
class QuickJSEngine {
    private external fun createContext(): Long
    private external fun destroyContext(context: Long)
    private external fun evaluateScript(context: Long, script: String): String
    private external fun callFunction(context: Long, function: String, args: Array<Any>): String
    
    companion object {
        init {
            System.loadLibrary("quickjs")
        }
    }
}
```

#### 3.1.3 网络层重构
```kotlin
// 增强网络请求能力
class OkHttpManager {
    private val client: OkHttpClient
    
    init {
        client = OkHttpClient.Builder()
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(15, TimeUnit.SECONDS)
            .writeTimeout(15, TimeUnit.SECONDS)
            .addInterceptor(RetryInterceptor())
            .addInterceptor(HeaderInterceptor())
            .addInterceptor(ProxyInterceptor())
            .build()
    }
    
    suspend fun getString(url: String, headers: Map<String, String> = emptyMap()): String
    suspend fun postString(url: String, body: String, headers: Map<String, String> = emptyMap()): String
}
```

### 3.2 第二阶段：解析器移植（2-3天）

#### 3.2.1 XPath解析器系列
```kotlin
// XPathMacFilter解析器
class XPathMacFilterSpider(private val config: VodSite) : Spider() {
    private val extConfig: XPathConfig by lazy {
        loadExtConfig(config.ext)
    }
    
    override suspend fun homeContent(filter: Boolean): String {
        val html = req(config.api)
        val categories = pdfa(html, extConfig.categorySelector)
        
        return buildJsonResponse {
            "class" to categories.map { element ->
                mapOf(
                    "type_id" to pdfh(element, extConfig.categoryIdRule),
                    "type_name" to pdfh(element, extConfig.categoryNameRule)
                )
            }
        }
    }
    
    override suspend fun categoryContent(tid: String, pg: String, filter: Boolean, extend: Map<String, String>): String {
        val url = buildCategoryUrl(tid, pg, extend)
        val html = req(url)
        val items = pdfa(html, extConfig.listSelector)
        
        return buildJsonResponse {
            "list" to items.map { item ->
                mapOf(
                    "vod_id" to pdfh(item, extConfig.idRule),
                    "vod_name" to pdfh(item, extConfig.nameRule),
                    "vod_pic" to pdfh(item, extConfig.picRule),
                    "vod_remarks" to pdfh(item, extConfig.remarkRule)
                )
            },
            "page" to pg.toInt(),
            "pagecount" to extractPageCount(html)
        }
    }
}
```

#### 3.2.2 AppYs解析器
```kotlin
// AppYs API解析器
class AppYsSpider(private val config: VodSite) : Spider() {
    override suspend fun homeContent(filter: Boolean): String {
        val apiUrl = "${config.api}?ac=list"
        val response = req(apiUrl)
        val apiResult = Json.decodeFromString<AppYsResponse>(response)
        
        return buildJsonResponse {
            "class" to apiResult.`class`
        }
    }
    
    override suspend fun detailContent(ids: List<String>): String {
        val vodId = ids.first()
        val apiUrl = "${config.api}?ac=detail&ids=$vodId"
        val response = req(apiUrl)
        val apiResult = Json.decodeFromString<AppYsDetailResponse>(response)
        
        return processAppYsDetail(apiResult)
    }
}
```

#### 3.2.3 JavaScript解析器
```kotlin
// JavaScript解析器
class JavaScriptSpider(private val config: VodSite) : Spider() {
    private val jsEngine = QuickJSEngine()
    private val scriptContent: String by lazy {
        loadJavaScriptContent(config.api)
    }
    
    override suspend fun homeContent(filter: Boolean): String {
        return jsEngine.callFunction("homeContent", arrayOf(filter))
    }
    
    override suspend fun categoryContent(tid: String, pg: String, filter: Boolean, extend: Map<String, String>): String {
        return jsEngine.callFunction("categoryContent", arrayOf(tid, pg, filter, extend))
    }
}
```

### 3.3 第三阶段：智能处理机制（1-2天）

#### 3.3.1 多引擎回退机制
```kotlin
class SpiderManager {
    private val engines = listOf(
        JavaScriptEngine(),
        XPathEngine(),
        AppYsEngine(),
        DefaultEngine()
    )
    
    suspend fun parseContent(site: VodSite, operation: String, params: Map<String, Any>): Result<String> {
        for (engine in engines) {
            if (engine.canHandle(site)) {
                try {
                    val result = engine.execute(site, operation, params)
                    if (result.isSuccess) {
                        return result
                    }
                } catch (e: Exception) {
                    Log.w("SpiderManager", "Engine ${engine.name} failed, trying next", e)
                    continue
                }
            }
        }
        return Result.failure(Exception("All engines failed"))
    }
}
```

#### 3.3.2 智能类型检测
```kotlin
class SiteTypeDetector {
    fun detectSiteType(site: VodSite): SiteType {
        return when {
            site.api.endsWith(".js") -> SiteType.JAVASCRIPT
            site.api.contains("app") -> SiteType.APP_YS
            site.type == 3 -> when (site.api) {
                "csp_XPath" -> SiteType.XPATH
                "csp_XPathMac" -> SiteType.XPATH_MAC
                "csp_XPathMacFilter" -> SiteType.XPATH_MAC_FILTER
                "csp_AppYs" -> SiteType.APP_YS
                else -> SiteType.CUSTOM_SPIDER
            }
            else -> SiteType.UNKNOWN
        }
    }
}
```

### 3.4 第四阶段：配置系统增强（1天）

#### 3.4.1 配置解析增强
```kotlin
class ConfigParser {
    suspend fun parseConfig(configUrl: String): Result<VodConfigResponse> {
        return try {
            val configContent = httpManager.getString(configUrl)
            val config = when {
                configContent.startsWith("{") -> parseJsonConfig(configContent)
                configContent.startsWith("http") -> parseUrlConfig(configContent)
                else -> parseTextConfig(configContent)
            }
            Result.success(config)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    private fun parseJsonConfig(content: String): VodConfigResponse {
        return Json.decodeFromString<VodConfigResponse>(content)
    }
    
    private suspend fun parseUrlConfig(content: String): VodConfigResponse {
        // 处理配置文件URL列表
        val urls = content.lines().filter { it.isNotBlank() }
        return mergeConfigs(urls.map { parseConfig(it).getOrThrow() })
    }
}
```

## 4. 关键技术实现

### 4.1 保持直播系统隔离
```kotlin
// 确保点播系统修改不影响直播系统
object ModuleIsolation {
    // 直播系统包路径（不修改）
    const val LIVE_PACKAGE = "top.cywin.onetv.core.data.repositories.iptv"
    
    // 点播系统包路径（可修改）
    const val VOD_PACKAGE = "top.cywin.onetv.movie"
    
    // 共享组件包路径（谨慎修改）
    const val SHARED_PACKAGE = "top.cywin.onetv.core"
}
```

### 4.2 依赖注入适配
```kotlin
// 在MovieApp中集成新的解析系统
class MovieApp {
    // 新增解析引擎管理器
    val spiderManager by lazy {
        SpiderManager(
            httpManager = httpManager,
            jsEngine = QuickJSEngine(),
            cacheManager = cacheManager
        )
    }
    
    // 增强解析管理器
    val enhancedParseManager by lazy {
        EnhancedParseManager(
            spiderManager = spiderManager,
            configManager = vodConfigManager,
            cacheManager = cacheManager
        )
    }
    
    // 更新仓库层
    val enhancedVodRepository by lazy {
        EnhancedVodRepository(
            parseManager = enhancedParseManager,
            spiderManager = spiderManager,
            // 其他现有依赖保持不变
            context = applicationContext,
            appConfigManager = appConfigManager,
            cacheManager = cacheManager
        )
    }
}
```

### 4.3 向后兼容性
```kotlin
// 保持现有API兼容性
class VodRepository {
    // 现有方法保持不变，内部使用增强的解析器
    suspend fun getContentList(
        typeId: String,
        page: Int = 1,
        siteKey: String = "",
        filters: Map<String, String> = emptyMap()
    ): Result<VodResponse> {
        // 使用新的解析系统，但保持接口不变
        return enhancedParseManager.getContentList(typeId, page, siteKey, filters)
    }
}
```

## 5. 测试验证策略

### 5.1 单元测试
```kotlin
// Spider解析器测试
class SpiderTest {
    @Test
    fun testXPathSpider() {
        val spider = XPathMacFilterSpider(testSiteConfig)
        val result = runBlocking { spider.homeContent(false) }
        assertThat(result).isNotEmpty()
    }
    
    @Test
    fun testJavaScriptSpider() {
        val spider = JavaScriptSpider(testJsSiteConfig)
        val result = runBlocking { spider.categoryContent("1", "1", false, emptyMap()) }
        assertThat(result).contains("list")
    }
}
```

### 5.2 集成测试
```kotlin
// 完整解析流程测试
class IntegrationTest {
    @Test
    fun testCompleteParsingFlow() {
        val configUrl = "https://example.com/config.json"
        val result = runBlocking {
            vodRepository.loadConfig(configUrl)
                .flatMap { vodRepository.getContentList("1", 1) }
        }
        assertThat(result.isSuccess).isTrue()
    }
}
```

## 6. 风险控制

### 6.1 回滚策略
- 保留现有解析器作为备用
- 使用特性开关控制新解析器启用
- 提供降级机制

### 6.2 性能监控
- 解析耗时监控
- 内存使用监控
- 成功率统计

### 6.3 兼容性保证
- 保持现有API接口不变
- 确保直播系统完全隔离
- 提供配置迁移工具

## 7. 实施时间表

| 阶段 | 任务 | 预计时间 | 关键里程碑 |
|------|------|----------|------------|
| 第一阶段 | 基础架构移植 | 1-2天 | Spider引擎可用 |
| 第二阶段 | 解析器移植 | 2-3天 | 主要解析器工作 |
| 第三阶段 | 智能处理机制 | 1-2天 | 多引擎回退可用 |
| 第四阶段 | 配置系统增强 | 1天 | 完整配置解析 |
| 第五阶段 | 测试验证 | 1-2天 | 全面测试通过 |

**总计**: 6-10天

## 8. 成功标准

1. **功能完整性**: 支持所有FongMi/TV解析功能
2. **兼容性**: 与标准TVBOX配置100%兼容
3. **性能**: 解析速度不低于原系统
4. **稳定性**: 解析成功率>95%
5. **隔离性**: 直播系统完全不受影响

## 9. 详细技术实现

### 9.1 核心解析器实现

#### 9.1.1 Spider基础类
```kotlin
// movie/src/main/java/top/cywin/onetv/movie/spider/Spider.kt
abstract class Spider {
    protected val TAG = "ONETV_MOVIE_SPIDER"
    protected lateinit var site: VodSite
    protected lateinit var httpManager: OkHttpManager

    open fun init(site: VodSite, httpManager: OkHttpManager) {
        this.site = site
        this.httpManager = httpManager
    }

    // 核心解析方法
    abstract suspend fun homeContent(filter: Boolean): String
    abstract suspend fun categoryContent(tid: String, pg: String, filter: Boolean, extend: Map<String, String>): String
    abstract suspend fun detailContent(ids: List<String>): String
    abstract suspend fun searchContent(key: String, quick: Boolean): String
    abstract suspend fun playerContent(flag: String, id: String, vipFlags: List<String>): String

    // 工具方法
    protected suspend fun req(url: String, headers: Map<String, String> = emptyMap()): String {
        return httpManager.getString(url, headers + site.header)
    }

    protected fun pdfh(html: String, rule: String): String {
        return JsoupUtils.parseRule(html, rule)
    }

    protected fun pdfa(html: String, rule: String): List<String> {
        return JsoupUtils.parseRuleArray(html, rule)
    }

    protected fun buildJsonResponse(builder: JsonObjectBuilder.() -> Unit): String {
        return JsonObjectBuilder().apply(builder).build()
    }
}
```

#### 9.1.2 XPath解析器实现
```kotlin
// movie/src/main/java/top/cywin/onetv/movie/spider/XPathMacFilterSpider.kt
class XPathMacFilterSpider : Spider() {
    private lateinit var extConfig: XPathConfig

    override fun init(site: VodSite, httpManager: OkHttpManager) {
        super.init(site, httpManager)
        extConfig = loadExtConfig(site.ext)
    }

    override suspend fun homeContent(filter: Boolean): String {
        return try {
            val html = req(site.api)
            val categories = pdfa(html, extConfig.categorySelector)

            buildJsonResponse {
                "class" to categories.map { element ->
                    mapOf(
                        "type_id" to pdfh(element, extConfig.categoryIdRule),
                        "type_name" to pdfh(element, extConfig.categoryNameRule)
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "XPath homeContent failed", e)
            buildErrorResponse(e.message)
        }
    }

    override suspend fun categoryContent(tid: String, pg: String, filter: Boolean, extend: Map<String, String>): String {
        return try {
            val url = buildCategoryUrl(tid, pg, extend)
            val html = req(url)
            val items = pdfa(html, extConfig.listSelector)

            buildJsonResponse {
                "list" to items.map { item ->
                    mapOf(
                        "vod_id" to pdfh(item, extConfig.idRule),
                        "vod_name" to pdfh(item, extConfig.nameRule),
                        "vod_pic" to pdfh(item, extConfig.picRule),
                        "vod_remarks" to pdfh(item, extConfig.remarkRule)
                    )
                },
                "page" to pg.toInt(),
                "pagecount" to extractPageCount(html),
                "limit" to 20,
                "total" to 999
            }
        } catch (e: Exception) {
            Log.e(TAG, "XPath categoryContent failed", e)
            buildErrorResponse(e.message)
        }
    }

    override suspend fun detailContent(ids: List<String>): String {
        return try {
            val vodId = ids.first()
            val url = buildDetailUrl(vodId)
            val html = req(url)

            val playList = extractPlayList(html)

            buildJsonResponse {
                "list" to listOf(
                    mapOf(
                        "vod_id" to vodId,
                        "vod_name" to pdfh(html, extConfig.detailNameRule),
                        "vod_pic" to pdfh(html, extConfig.detailPicRule),
                        "vod_content" to pdfh(html, extConfig.detailContentRule),
                        "vod_year" to pdfh(html, extConfig.detailYearRule),
                        "vod_area" to pdfh(html, extConfig.detailAreaRule),
                        "vod_actor" to pdfh(html, extConfig.detailActorRule),
                        "vod_director" to pdfh(html, extConfig.detailDirectorRule),
                        "vod_play_from" to playList.keys.joinToString("$$$"),
                        "vod_play_url" to playList.values.joinToString("$$$")
                    )
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "XPath detailContent failed", e)
            buildErrorResponse(e.message)
        }
    }

    private fun extractPlayList(html: String): Map<String, String> {
        val playGroups = pdfa(html, extConfig.playGroupSelector)
        val result = mutableMapOf<String, String>()

        playGroups.forEachIndexed { index, group ->
            val groupName = pdfh(group, extConfig.playGroupNameRule).ifEmpty { "播放组${index + 1}" }
            val episodes = pdfa(group, extConfig.playEpisodeSelector)

            val episodeList = episodes.map { episode ->
                val name = pdfh(episode, extConfig.playEpisodeNameRule)
                val url = pdfh(episode, extConfig.playEpisodeUrlRule)
                "$name$${url}"
            }.joinToString("#")

            result[groupName] = episodeList
        }

        return result
    }
}
```

#### 9.1.3 JavaScript解析器实现
```kotlin
// movie/src/main/java/top/cywin/onetv/movie/spider/JavaScriptSpider.kt
class JavaScriptSpider : Spider() {
    private lateinit var jsEngine: QuickJSEngine
    private var scriptContent: String = ""

    override fun init(site: VodSite, httpManager: OkHttpManager) {
        super.init(site, httpManager)
        jsEngine = QuickJSEngine()
        initializeJavaScript()
    }

    private suspend fun initializeJavaScript() {
        try {
            // 加载JavaScript内容
            scriptContent = if (site.api.startsWith("http")) {
                req(site.api)
            } else {
                // 从JAR包或本地加载
                loadLocalScript(site.api)
            }

            // 初始化JavaScript环境
            jsEngine.evaluateScript("""
                var HOST = '${extractHost(site.api)}';
                var MOBILE_UA = 'Mozilla/5.0 (Linux; Android 11; M2007J3SC Build/RKQ1.200826.002; wv) AppleWebKit/537.36';
                var PC_UA = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
                var UA = MOBILE_UA;

                // 注入工具函数
                var req = function(url, options) {
                    return Java.callStaticMethod('${httpManager.javaClass.name}', 'getString', url, options || {});
                };

                var pdfh = function(html, rule) {
                    return Java.callStaticMethod('${JsoupUtils.javaClass.name}', 'parseRule', html, rule);
                };

                var pdfa = function(html, rule) {
                    return Java.callStaticMethod('${JsoupUtils.javaClass.name}', 'parseRuleArray', html, rule);
                };
            """)

            // 执行站点脚本
            jsEngine.evaluateScript(scriptContent)

        } catch (e: Exception) {
            Log.e(TAG, "JavaScript initialization failed", e)
        }
    }

    override suspend fun homeContent(filter: Boolean): String {
        return try {
            jsEngine.callFunction("homeContent", arrayOf(filter))
        } catch (e: Exception) {
            Log.e(TAG, "JavaScript homeContent failed", e)
            buildErrorResponse(e.message)
        }
    }

    override suspend fun categoryContent(tid: String, pg: String, filter: Boolean, extend: Map<String, String>): String {
        return try {
            jsEngine.callFunction("categoryContent", arrayOf(tid, pg, filter, extend))
        } catch (e: Exception) {
            Log.e(TAG, "JavaScript categoryContent failed", e)
            buildErrorResponse(e.message)
        }
    }

    override suspend fun detailContent(ids: List<String>): String {
        return try {
            jsEngine.callFunction("detailContent", arrayOf(ids))
        } catch (e: Exception) {
            Log.e(TAG, "JavaScript detailContent failed", e)
            buildErrorResponse(e.message)
        }
    }

    override suspend fun searchContent(key: String, quick: Boolean): String {
        return try {
            jsEngine.callFunction("searchContent", arrayOf(key, quick))
        } catch (e: Exception) {
            Log.e(TAG, "JavaScript searchContent failed", e)
            buildErrorResponse(e.message)
        }
    }

    override suspend fun playerContent(flag: String, id: String, vipFlags: List<String>): String {
        return try {
            jsEngine.callFunction("playerContent", arrayOf(flag, id, vipFlags))
        } catch (e: Exception) {
            Log.e(TAG, "JavaScript playerContent failed", e)
            buildErrorResponse(e.message)
        }
    }
}
```

### 9.2 QuickJS引擎集成

#### 9.2.1 QuickJS JNI接口
```kotlin
// movie/src/main/java/top/cywin/onetv/movie/engine/QuickJSEngine.kt
class QuickJSEngine {
    private var jsContext: Long = 0

    init {
        jsContext = createJSContext()
        if (jsContext == 0L) {
            throw RuntimeException("Failed to create QuickJS context")
        }
    }

    fun evaluateScript(script: String): String {
        if (jsContext == 0L) throw IllegalStateException("QuickJS context not initialized")
        return nativeEvaluateScript(jsContext, script)
    }

    fun callFunction(functionName: String, args: Array<Any>): String {
        if (jsContext == 0L) throw IllegalStateException("QuickJS context not initialized")
        val argsJson = Json.encodeToString(args)
        return nativeCallFunction(jsContext, functionName, argsJson)
    }

    fun destroy() {
        if (jsContext != 0L) {
            destroyJSContext(jsContext)
            jsContext = 0
        }
    }

    // Native方法声明
    private external fun createJSContext(): Long
    private external fun destroyJSContext(context: Long)
    private external fun nativeEvaluateScript(context: Long, script: String): String
    private external fun nativeCallFunction(context: Long, functionName: String, argsJson: String): String

    companion object {
        init {
            try {
                System.loadLibrary("quickjs-onetv")
            } catch (e: UnsatisfiedLinkError) {
                Log.e("QuickJSEngine", "Failed to load QuickJS library", e)
            }
        }
    }
}
```

#### 9.2.2 C++实现（quickjs-android.cpp）
```cpp
// movie/src/main/cpp/quickjs-android.cpp
#include <jni.h>
#include <string>
#include <quickjs.h>

extern "C" {

JNIEXPORT jlong JNICALL
Java_top_cywin_onetv_movie_engine_QuickJSEngine_createJSContext(JNIEnv *env, jobject thiz) {
    JSRuntime *rt = JS_NewRuntime();
    if (!rt) return 0;

    JSContext *ctx = JS_NewContext(rt);
    if (!ctx) {
        JS_FreeRuntime(rt);
        return 0;
    }

    // 设置内存限制
    JS_SetMemoryLimit(rt, 64 * 1024 * 1024); // 64MB

    // 设置最大栈大小
    JS_SetMaxStackSize(rt, 256 * 1024); // 256KB

    return reinterpret_cast<jlong>(ctx);
}

JNIEXPORT void JNICALL
Java_top_cywin_onetv_movie_engine_QuickJSEngine_destroyJSContext(JNIEnv *env, jobject thiz, jlong context) {
    JSContext *ctx = reinterpret_cast<JSContext*>(context);
    if (ctx) {
        JSRuntime *rt = JS_GetRuntime(ctx);
        JS_FreeContext(ctx);
        JS_FreeRuntime(rt);
    }
}

JNIEXPORT jstring JNICALL
Java_top_cywin_onetv_movie_engine_QuickJSEngine_nativeEvaluateScript(JNIEnv *env, jobject thiz, jlong context, jstring script) {
    JSContext *ctx = reinterpret_cast<JSContext*>(context);
    const char *scriptStr = env->GetStringUTFChars(script, nullptr);

    JSValue result = JS_Eval(ctx, scriptStr, strlen(scriptStr), "<eval>", JS_EVAL_TYPE_GLOBAL);

    env->ReleaseStringUTFChars(script, scriptStr);

    if (JS_IsException(result)) {
        JSValue exception = JS_GetException(ctx);
        const char *errorStr = JS_ToCString(ctx, exception);
        jstring errorResult = env->NewStringUTF(errorStr);
        JS_FreeCString(ctx, errorStr);
        JS_FreeValue(ctx, exception);
        JS_FreeValue(ctx, result);
        return errorResult;
    }

    const char *resultStr = JS_ToCString(ctx, result);
    jstring jResult = env->NewStringUTF(resultStr);
    JS_FreeCString(ctx, resultStr);
    JS_FreeValue(ctx, result);

    return jResult;
}

JNIEXPORT jstring JNICALL
Java_top_cywin_onetv_movie_engine_QuickJSEngine_nativeCallFunction(JNIEnv *env, jobject thiz, jlong context, jstring function_name, jstring args_json) {
    JSContext *ctx = reinterpret_cast<JSContext*>(context);
    const char *funcName = env->GetStringUTFChars(function_name, nullptr);
    const char *argsStr = env->GetStringUTFChars(args_json, nullptr);

    // 获取全局对象
    JSValue global = JS_GetGlobalObject(ctx);

    // 获取函数
    JSValue func = JS_GetPropertyStr(ctx, global, funcName);

    if (!JS_IsFunction(ctx, func)) {
        env->ReleaseStringUTFChars(function_name, funcName);
        env->ReleaseStringUTFChars(args_json, argsStr);
        JS_FreeValue(ctx, func);
        JS_FreeValue(ctx, global);
        return env->NewStringUTF("Function not found");
    }

    // 解析参数
    JSValue args = JS_ParseJSON(ctx, argsStr, strlen(argsStr), "<args>");

    // 调用函数
    JSValue result = JS_Call(ctx, func, global, 1, &args);

    env->ReleaseStringUTFChars(function_name, funcName);
    env->ReleaseStringUTFChars(args_json, argsStr);
    JS_FreeValue(ctx, args);
    JS_FreeValue(ctx, func);
    JS_FreeValue(ctx, global);

    if (JS_IsException(result)) {
        JSValue exception = JS_GetException(ctx);
        const char *errorStr = JS_ToCString(ctx, exception);
        jstring errorResult = env->NewStringUTF(errorStr);
        JS_FreeCString(ctx, errorStr);
        JS_FreeValue(ctx, exception);
        JS_FreeValue(ctx, result);
        return errorResult;
    }

    const char *resultStr = JS_ToCString(ctx, result);
    jstring jResult = env->NewStringUTF(resultStr);
    JS_FreeCString(ctx, resultStr);
    JS_FreeValue(ctx, result);

    return jResult;
}

}
```

### 9.3 网络层增强

#### 9.3.1 OkHttp管理器
```kotlin
// movie/src/main/java/top/cywin/onetv/movie/network/OkHttpManager.kt
class OkHttpManager {
    private val client: OkHttpClient
    private val proxyManager = ProxyManager()
    private val headerManager = HeaderManager()

    init {
        client = OkHttpClient.Builder()
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(15, TimeUnit.SECONDS)
            .writeTimeout(15, TimeUnit.SECONDS)
            .addInterceptor(RetryInterceptor(maxRetries = 3))
            .addInterceptor(HeaderInterceptor(headerManager))
            .addInterceptor(ProxyInterceptor(proxyManager))
            .addInterceptor(LoggingInterceptor())
            .build()
    }

    suspend fun getString(url: String, headers: Map<String, String> = emptyMap()): String {
        return withContext(Dispatchers.IO) {
            try {
                val request = Request.Builder()
                    .url(url)
                    .apply {
                        headers.forEach { (key, value) ->
                            addHeader(key, value)
                        }
                    }
                    .build()

                val response = client.newCall(request).execute()
                if (!response.isSuccessful) {
                    throw HttpException(response.code, response.message)
                }

                response.body?.string() ?: ""
            } catch (e: Exception) {
                Log.e("OkHttpManager", "Request failed: $url", e)
                throw e
            }
        }
    }

    suspend fun postString(url: String, body: String, headers: Map<String, String> = emptyMap()): String {
        return withContext(Dispatchers.IO) {
            try {
                val requestBody = body.toRequestBody("application/json".toMediaType())
                val request = Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .apply {
                        headers.forEach { (key, value) ->
                            addHeader(key, value)
                        }
                    }
                    .build()

                val response = client.newCall(request).execute()
                if (!response.isSuccessful) {
                    throw HttpException(response.code, response.message)
                }

                response.body?.string() ?: ""
            } catch (e: Exception) {
                Log.e("OkHttpManager", "POST request failed: $url", e)
                throw e
            }
        }
    }
}
```

### 9.4 工具类实现

#### 9.4.1 Jsoup工具类
```kotlin
// movie/src/main/java/top/cywin/onetv/movie/utils/JsoupUtils.kt
object JsoupUtils {

    fun parseRule(html: String, rule: String): String {
        return try {
            val doc = Jsoup.parse(html)
            parseRuleInternal(doc, rule)
        } catch (e: Exception) {
            Log.e("JsoupUtils", "Parse rule failed: $rule", e)
            ""
        }
    }

    fun parseRuleArray(html: String, rule: String): List<String> {
        return try {
            val doc = Jsoup.parse(html)
            val elements = doc.select(extractSelector(rule))
            elements.map { element ->
                parseElementByRule(element, rule)
            }
        } catch (e: Exception) {
            Log.e("JsoupUtils", "Parse rule array failed: $rule", e)
            emptyList()
        }
    }

    private fun parseRuleInternal(doc: Document, rule: String): String {
        val parts = rule.split("&&")
        val selector = parts[0]
        val attr = if (parts.size > 1) parts[1] else "text"

        val elements = doc.select(selector)
        if (elements.isEmpty()) return ""

        val element = elements.first()
        return parseElementByRule(element, rule)
    }

    private fun parseElementByRule(element: Element, rule: String): String {
        val parts = rule.split("&&")
        val attr = if (parts.size > 1) parts[1] else "text"

        return when (attr.lowercase()) {
            "text" -> element.text()
            "html" -> element.html()
            "outerhtml" -> element.outerHtml()
            "href" -> element.attr("href")
            "src" -> element.attr("src")
            else -> element.attr(attr)
        }
    }

    private fun extractSelector(rule: String): String {
        return rule.split("&&")[0]
    }
}
```

#### 9.4.2 URL工具类
```kotlin
// movie/src/main/java/top/cywin/onetv/movie/utils/UrlUtils.kt
object UrlUtils {

    fun buildUrl(baseUrl: String, path: String): String {
        return when {
            path.startsWith("http") -> path
            path.startsWith("//") -> "https:$path"
            path.startsWith("/") -> "${extractDomain(baseUrl)}$path"
            else -> "${baseUrl.trimEnd('/')}/$path"
        }
    }

    fun extractDomain(url: String): String {
        return try {
            val uri = URI(url)
            "${uri.scheme}://${uri.host}${if (uri.port != -1) ":${uri.port}" else ""}"
        } catch (e: Exception) {
            url.substringBefore("/", url)
        }
    }

    fun extractHost(url: String): String {
        return try {
            URI(url).host ?: ""
        } catch (e: Exception) {
            ""
        }
    }

    fun addParams(url: String, params: Map<String, String>): String {
        if (params.isEmpty()) return url

        val separator = if (url.contains("?")) "&" else "?"
        val paramString = params.entries.joinToString("&") { (key, value) ->
            "${URLEncoder.encode(key, "UTF-8")}=${URLEncoder.encode(value, "UTF-8")}"
        }

        return "$url$separator$paramString"
    }
}
```

### 9.5 Spider管理器实现

#### 9.5.1 Spider工厂
```kotlin
// movie/src/main/java/top/cywin/onetv/movie/spider/SpiderFactory.kt
object SpiderFactory {

    fun createSpider(site: VodSite, httpManager: OkHttpManager): Spider {
        val spider = when (detectSpiderType(site)) {
            SpiderType.XPATH -> XPathSpider()
            SpiderType.XPATH_MAC -> XPathMacSpider()
            SpiderType.XPATH_MAC_FILTER -> XPathMacFilterSpider()
            SpiderType.APP_YS -> AppYsSpider()
            SpiderType.JAVASCRIPT -> JavaScriptSpider()
            SpiderType.CUSTOM -> CustomSpider()
            else -> DefaultSpider()
        }

        spider.init(site, httpManager)
        return spider
    }

    private fun detectSpiderType(site: VodSite): SpiderType {
        return when {
            site.api.endsWith(".js") -> SpiderType.JAVASCRIPT
            site.type == 3 -> when (site.api) {
                "csp_XPath" -> SpiderType.XPATH
                "csp_XPathMac" -> SpiderType.XPATH_MAC
                "csp_XPathMacFilter" -> SpiderType.XPATH_MAC_FILTER
                "csp_AppYs" -> SpiderType.APP_YS
                else -> SpiderType.CUSTOM
            }
            site.type == 1 -> SpiderType.APP_YS
            else -> SpiderType.DEFAULT
        }
    }
}

enum class SpiderType {
    XPATH, XPATH_MAC, XPATH_MAC_FILTER, APP_YS, JAVASCRIPT, CUSTOM, DEFAULT
}
```

#### 9.5.2 Spider管理器
```kotlin
// movie/src/main/java/top/cywin/onetv/movie/spider/SpiderManager.kt
class SpiderManager(
    private val httpManager: OkHttpManager,
    private val cacheManager: MovieCacheManager
) {
    private val TAG = "ONETV_MOVIE_SPIDER_MANAGER"
    private val spiderCache = mutableMapOf<String, Spider>()

    suspend fun executeSpiderOperation(
        site: VodSite,
        operation: SpiderOperation,
        params: Map<String, Any> = emptyMap()
    ): Result<String> {
        return try {
            val spider = getOrCreateSpider(site)
            val result = when (operation) {
                SpiderOperation.HOME_CONTENT -> {
                    val filter = params["filter"] as? Boolean ?: false
                    spider.homeContent(filter)
                }
                SpiderOperation.CATEGORY_CONTENT -> {
                    val tid = params["tid"] as? String ?: ""
                    val pg = params["pg"] as? String ?: "1"
                    val filter = params["filter"] as? Boolean ?: false
                    val extend = params["extend"] as? Map<String, String> ?: emptyMap()
                    spider.categoryContent(tid, pg, filter, extend)
                }
                SpiderOperation.DETAIL_CONTENT -> {
                    val ids = params["ids"] as? List<String> ?: emptyList()
                    spider.detailContent(ids)
                }
                SpiderOperation.SEARCH_CONTENT -> {
                    val key = params["key"] as? String ?: ""
                    val quick = params["quick"] as? Boolean ?: false
                    spider.searchContent(key, quick)
                }
                SpiderOperation.PLAYER_CONTENT -> {
                    val flag = params["flag"] as? String ?: ""
                    val id = params["id"] as? String ?: ""
                    val vipFlags = params["vipFlags"] as? List<String> ?: emptyList()
                    spider.playerContent(flag, id, vipFlags)
                }
            }

            Result.success(result)
        } catch (e: Exception) {
            Log.e(TAG, "Spider operation failed: $operation", e)
            Result.failure(e)
        }
    }

    private fun getOrCreateSpider(site: VodSite): Spider {
        return spiderCache.getOrPut(site.key) {
            SpiderFactory.createSpider(site, httpManager)
        }
    }

    fun clearCache() {
        spiderCache.clear()
    }

    fun clearSiteCache(siteKey: String) {
        spiderCache.remove(siteKey)
    }
}

enum class SpiderOperation {
    HOME_CONTENT, CATEGORY_CONTENT, DETAIL_CONTENT, SEARCH_CONTENT, PLAYER_CONTENT
}
```

### 9.6 配置解析增强

#### 9.6.1 配置解析器
```kotlin
// movie/src/main/java/top/cywin/onetv/movie/parser/ConfigParser.kt
class ConfigParser(
    private val httpManager: OkHttpManager,
    private val cacheManager: MovieCacheManager
) {
    private val TAG = "ONETV_MOVIE_CONFIG_PARSER"

    suspend fun parseConfig(configUrl: String): Result<VodConfigResponse> {
        return try {
            Log.d(TAG, "开始解析配置: $configUrl")

            // 检查缓存
            val cacheKey = "config_${configUrl.hashCode()}"
            val cached = cacheManager.getCache(cacheKey, VodConfigResponse::class.java)
            if (cached != null) {
                Log.d(TAG, "使用缓存配置")
                return Result.success(cached)
            }

            val configContent = httpManager.getString(configUrl)
            val config = when {
                configContent.trim().startsWith("{") -> parseJsonConfig(configContent)
                configContent.trim().startsWith("http") -> parseUrlListConfig(configContent)
                configContent.contains("sites") -> parseJsonConfig(configContent)
                else -> parseTextConfig(configContent)
            }

            // 缓存配置
            cacheManager.saveCache(cacheKey, config, 24 * 60 * 60 * 1000L) // 24小时

            Log.d(TAG, "配置解析成功: 站点=${config.sites.size}个, 解析器=${config.parses.size}个")
            Result.success(config)

        } catch (e: Exception) {
            Log.e(TAG, "配置解析失败: $configUrl", e)
            Result.failure(e)
        }
    }

    private fun parseJsonConfig(content: String): VodConfigResponse {
        return try {
            Json.decodeFromString<VodConfigResponse>(content)
        } catch (e: Exception) {
            // 尝试修复常见的JSON格式问题
            val fixedContent = fixJsonFormat(content)
            Json.decodeFromString<VodConfigResponse>(fixedContent)
        }
    }

    private suspend fun parseUrlListConfig(content: String): VodConfigResponse {
        val urls = content.lines()
            .filter { it.isNotBlank() && it.startsWith("http") }
            .take(10) // 限制最多10个配置文件

        val configs = urls.mapNotNull { url ->
            try {
                parseConfig(url).getOrNull()
            } catch (e: Exception) {
                Log.w(TAG, "解析配置失败: $url", e)
                null
            }
        }

        return mergeConfigs(configs)
    }

    private fun parseTextConfig(content: String): VodConfigResponse {
        // 处理简单的文本格式配置
        val lines = content.lines().filter { it.isNotBlank() }
        val sites = mutableListOf<VodSite>()

        lines.forEach { line ->
            if (line.contains(",")) {
                val parts = line.split(",")
                if (parts.size >= 3) {
                    sites.add(
                        VodSite(
                            key = parts[0].trim(),
                            name = parts[1].trim(),
                            type = parts[2].trim().toIntOrNull() ?: 0,
                            api = parts.getOrNull(3)?.trim() ?: ""
                        )
                    )
                }
            }
        }

        return VodConfigResponse(sites = sites)
    }

    private fun mergeConfigs(configs: List<VodConfigResponse>): VodConfigResponse {
        val allSites = configs.flatMap { it.sites }
        val allParses = configs.flatMap { it.parses }.distinctBy { it.name }
        val allFlags = configs.flatMap { it.flags }.distinct()
        val allAds = configs.flatMap { it.ads }.distinct()

        return VodConfigResponse(
            spider = configs.firstOrNull()?.spider ?: "",
            wallpaper = configs.firstOrNull()?.wallpaper ?: "",
            sites = allSites,
            parses = allParses,
            flags = allFlags,
            ads = allAds
        )
    }

    private fun fixJsonFormat(content: String): String {
        return content
            .replace("//.*".toRegex(), "") // 移除注释
            .replace(",\\s*}".toRegex(), "}") // 修复尾随逗号
            .replace(",\\s*]".toRegex(), "]") // 修复尾随逗号
    }
}
```

### 9.7 集成到现有系统

#### 9.7.1 增强的VodRepository
```kotlin
// movie/src/main/java/top/cywin/onetv/movie/data/repository/EnhancedVodRepository.kt
class EnhancedVodRepository(
    private val context: Context,
    private val appConfigManager: AppConfigManager,
    private val cacheManager: MovieCacheManager,
    private val vodCacheManager: VodCacheManager,
    private val configManager: VodConfigManager,
    private val spiderManager: SpiderManager,
    private val configParser: ConfigParser,
    private val apiService: VodApiService,
    private val siteApiService: VodApiService
) {
    private val TAG = "ONETV_MOVIE_ENHANCED_REPOSITORY"

    // 保持现有接口兼容性
    suspend fun getContentList(
        typeId: String,
        page: Int = 1,
        siteKey: String = "",
        filters: Map<String, String> = emptyMap()
    ): Result<VodResponse> = withContext(Dispatchers.IO) {
        try {
            val site = configManager.getSite(siteKey)
                ?: return@withContext Result.failure(Exception("站点不存在: $siteKey"))

            Log.d(TAG, "获取内容列表: 站点=$siteKey, 分类=$typeId, 页码=$page")

            // 使用Spider管理器执行操作
            val result = spiderManager.executeSpiderOperation(
                site = site,
                operation = SpiderOperation.CATEGORY_CONTENT,
                params = mapOf(
                    "tid" to typeId,
                    "pg" to page.toString(),
                    "filter" to true,
                    "extend" to filters
                )
            )

            if (result.isFailure) {
                return@withContext Result.failure(result.exceptionOrNull() ?: Exception("解析失败"))
            }

            val jsonResponse = result.getOrThrow()
            val vodResponse = parseVodResponse(jsonResponse)

            Log.d(TAG, "内容列表获取成功: ${vodResponse.list.size}个项目")
            Result.success(vodResponse)

        } catch (e: Exception) {
            Log.e(TAG, "获取内容列表失败", e)
            Result.failure(e)
        }
    }

    suspend fun getVideoDetail(vodId: String, siteKey: String = ""): Result<VodItem> = withContext(Dispatchers.IO) {
        try {
            val site = configManager.getSite(siteKey)
                ?: return@withContext Result.failure(Exception("站点不存在: $siteKey"))

            Log.d(TAG, "获取视频详情: vodId=$vodId, 站点=$siteKey")

            val result = spiderManager.executeSpiderOperation(
                site = site,
                operation = SpiderOperation.DETAIL_CONTENT,
                params = mapOf("ids" to listOf(vodId))
            )

            if (result.isFailure) {
                return@withContext Result.failure(result.exceptionOrNull() ?: Exception("解析失败"))
            }

            val jsonResponse = result.getOrThrow()
            val detailResponse = parseDetailResponse(jsonResponse)

            if (detailResponse.list.isEmpty()) {
                return@withContext Result.failure(Exception("未找到视频详情"))
            }

            val vodItem = detailResponse.list.first()
            Log.d(TAG, "视频详情获取成功: ${vodItem.vodName}")
            Result.success(vodItem)

        } catch (e: Exception) {
            Log.e(TAG, "获取视频详情失败", e)
            Result.failure(e)
        }
    }

    suspend fun searchContent(keyword: String, siteKey: String = ""): Result<VodResponse> = withContext(Dispatchers.IO) {
        try {
            if (siteKey.isNotEmpty()) {
                // 单站点搜索
                return@withContext searchSingleSite(keyword, siteKey)
            } else {
                // 多站点并发搜索
                return@withContext searchMultipleSites(keyword)
            }
        } catch (e: Exception) {
            Log.e(TAG, "搜索失败", e)
            Result.failure(e)
        }
    }

    private suspend fun searchSingleSite(keyword: String, siteKey: String): Result<VodResponse> {
        val site = configManager.getSite(siteKey)
            ?: return Result.failure(Exception("站点不存在: $siteKey"))

        if (site.searchable != 1) {
            return Result.failure(Exception("站点不支持搜索"))
        }

        val result = spiderManager.executeSpiderOperation(
            site = site,
            operation = SpiderOperation.SEARCH_CONTENT,
            params = mapOf(
                "key" to keyword,
                "quick" to false
            )
        )

        if (result.isFailure) {
            return Result.failure(result.exceptionOrNull() ?: Exception("搜索失败"))
        }

        val jsonResponse = result.getOrThrow()
        val vodResponse = parseVodResponse(jsonResponse)

        return Result.success(vodResponse)
    }

    private suspend fun searchMultipleSites(keyword: String): Result<VodResponse> {
        val sites = configManager.getSites().filter { it.searchable == 1 }

        val searchResults = sites.map { site ->
            async {
                try {
                    searchSingleSite(keyword, site.key).getOrNull()
                } catch (e: Exception) {
                    Log.w(TAG, "站点搜索失败: ${site.name}", e)
                    null
                }
            }
        }.awaitAll().filterNotNull()

        // 合并搜索结果
        val allItems = searchResults.flatMap { it.list }
        val mergedResponse = VodResponse(
            list = allItems,
            page = 1,
            pagecount = 1,
            limit = allItems.size,
            total = allItems.size
        )

        return Result.success(mergedResponse)
    }

    private fun parseVodResponse(jsonResponse: String): VodResponse {
        return try {
            Json.decodeFromString<VodResponse>(jsonResponse)
        } catch (e: Exception) {
            Log.e(TAG, "解析响应失败", e)
            VodResponse()
        }
    }

    private fun parseDetailResponse(jsonResponse: String): VodDetailResponse {
        return try {
            Json.decodeFromString<VodDetailResponse>(jsonResponse)
        } catch (e: Exception) {
            Log.e(TAG, "解析详情响应失败", e)
            VodDetailResponse()
        }
    }
}
```

## 10. 实施指南

### 10.1 开发环境准备

#### 10.1.1 依赖添加
```kotlin
// movie/build.gradle.kts
dependencies {
    // QuickJS引擎
    implementation("com.github.seven332:quickjs-onetv:0.9.0")

    // HTML解析
    implementation("org.jsoup:jsoup:1.15.3")

    // 网络请求
    implementation("com.squareup.okhttp3:okhttp:4.10.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.10.0")

    // JSON序列化
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.5.0")

    // 协程
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-onetv:1.6.4")

    // 现有依赖保持不变
    // ...
}
```

#### 10.1.2 NDK配置
```kotlin
// movie/build.gradle.kts
android {
    ndkVersion = "25.1.8937393"

    defaultConfig {
        ndk {
            abiFilters += listOf("arm64-v8a", "armeabi-v7a", "x86", "x86_64")
        }
    }

    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }
}
```

#### 10.1.3 CMakeLists.txt
```cmake
# movie/src/main/cpp/CMakeLists.txt
cmake_minimum_required(VERSION 3.22.1)
project("quickjs-onetv")

# QuickJS源码
add_subdirectory(quickjs)

# 创建共享库
add_library(quickjs-android SHARED
    quickjs-onetv.cpp
    jsoup-bridge.cpp
    http-bridge.cpp
)

# 链接库
target_link_libraries(quickjs-android
    quickjs
    android
    log
)
```

### 10.2 分阶段实施

#### 10.2.1 第一阶段：基础架构（第1-2天）
```bash
# 创建目录结构
mkdir -p movie/src/main/java/top/cywin/onetv/movie/spider
mkdir -p movie/src/main/java/top/cywin/onetv/movie/engine
mkdir -p movie/src/main/java/top/cywin/onetv/movie/network
mkdir -p movie/src/main/java/top/cywin/onetv/movie/utils
mkdir -p movie/src/main/cpp

# 实施任务清单
- [ ] 创建Spider基础类
- [ ] 实现QuickJS引擎
- [ ] 创建网络管理器
- [ ] 实现工具类
- [ ] 编写单元测试
```

#### 10.2.2 第二阶段：解析器实现（第3-5天）
```bash
# 实施任务清单
- [ ] XPathMacFilterSpider实现
- [ ] AppYsSpider实现
- [ ] JavaScriptSpider实现
- [ ] SpiderFactory实现
- [ ] SpiderManager实现
- [ ] 解析器测试
```

#### 10.2.3 第三阶段：系统集成（第6-7天）
```bash
# 实施任务清单
- [ ] 增强VodRepository
- [ ] 更新MovieApp依赖注入
- [ ] 配置解析器增强
- [ ] 缓存系统优化
- [ ] 集成测试
```

#### 10.2.4 第四阶段：测试验证（第8-10天）
```bash
# 实施任务清单
- [ ] 单元测试完善
- [ ] 集成测试
- [ ] 性能测试
- [ ] 兼容性测试
- [ ] 用户验收测试
```

### 10.3 质量保证

#### 10.3.1 代码审查清单
```markdown
## Spider实现审查
- [ ] 是否正确实现所有抽象方法
- [ ] 错误处理是否完善
- [ ] 日志记录是否充分
- [ ] 性能是否优化

## 网络层审查
- [ ] 超时设置是否合理
- [ ] 重试机制是否有效
- [ ] 代理支持是否完整
- [ ] 请求头处理是否正确

## 缓存机制审查
- [ ] 缓存策略是否合理
- [ ] 缓存失效机制是否正确
- [ ] 内存使用是否优化
- [ ] 并发安全是否保证
```

#### 10.3.2 测试覆盖率要求
```kotlin
// 测试覆盖率目标
- Spider类: 90%以上
- 网络层: 85%以上
- 工具类: 95%以上
- 管理器类: 80%以上
```

### 10.4 部署策略

#### 10.4.1 特性开关
```kotlin
// movie/src/main/java/top/cywin/onetv/movie/config/FeatureFlags.kt
object FeatureFlags {
    // 是否启用新解析系统
    const val ENABLE_ENHANCED_PARSER = true

    // 是否启用JavaScript引擎
    const val ENABLE_JAVASCRIPT_ENGINE = true

    // 是否启用多引擎回退
    const val ENABLE_MULTI_ENGINE_FALLBACK = true

    // 是否启用并发搜索
    const val ENABLE_CONCURRENT_SEARCH = true
}
```

#### 10.4.2 降级机制
```kotlin
// 解析失败时的降级策略
class FallbackStrategy {
    suspend fun executeWithFallback(operation: suspend () -> String): String {
        return try {
            if (FeatureFlags.ENABLE_ENHANCED_PARSER) {
                // 使用新解析系统
                operation()
            } else {
                // 使用原有解析系统
                legacyParseManager.parse()
            }
        } catch (e: Exception) {
            Log.w("FallbackStrategy", "Enhanced parser failed, using legacy", e)
            legacyParseManager.parse()
        }
    }
}
```

### 10.5 监控和维护

#### 10.5.1 性能监控
```kotlin
// 性能指标收集
class PerformanceMonitor {
    private val metrics = mutableMapOf<String, Long>()

    fun recordParseTime(siteKey: String, operation: String, timeMs: Long) {
        val key = "${siteKey}_${operation}_time"
        metrics[key] = timeMs

        if (timeMs > 5000) { // 超过5秒记录警告
            Log.w("PerformanceMonitor", "Slow parsing detected: $key = ${timeMs}ms")
        }
    }

    fun recordSuccessRate(siteKey: String, operation: String, success: Boolean) {
        val key = "${siteKey}_${operation}_success"
        // 记录成功率统计
    }
}
```

#### 10.5.2 错误监控
```kotlin
// 错误统计和报告
class ErrorMonitor {
    private val errorCounts = mutableMapOf<String, Int>()

    fun recordError(siteKey: String, operation: String, error: Throwable) {
        val key = "${siteKey}_${operation}_error"
        errorCounts[key] = errorCounts.getOrDefault(key, 0) + 1

        Log.e("ErrorMonitor", "Parse error: $key", error)

        // 错误率过高时发送警报
        if (errorCounts[key]!! > 10) {
            sendAlert("High error rate detected: $key")
        }
    }
}
```

## 11. 风险评估与应对

### 11.1 技术风险

| 风险 | 概率 | 影响 | 应对措施 |
|------|------|------|----------|
| QuickJS集成失败 | 中 | 高 | 提供WebView备选方案 |
| 解析器兼容性问题 | 高 | 中 | 逐步迁移，保留原解析器 |
| 性能下降 | 中 | 中 | 性能测试，优化关键路径 |
| 内存泄漏 | 低 | 高 | 严格测试，及时释放资源 |

### 11.2 业务风险

| 风险 | 概率 | 影响 | 应对措施 |
|------|------|------|----------|
| 直播功能受影响 | 低 | 高 | 严格模块隔离，独立测试 |
| 用户体验下降 | 中 | 高 | 渐进式发布，快速回滚 |
| 配置不兼容 | 中 | 中 | 配置迁移工具，向后兼容 |

### 11.3 应急预案

#### 11.3.1 快速回滚
```kotlin
// 紧急回滚开关
object EmergencySwitch {
    var useEnhancedParser: Boolean = true
        private set

    fun disableEnhancedParser() {
        useEnhancedParser = false
        Log.w("EmergencySwitch", "Enhanced parser disabled")
    }

    fun enableEnhancedParser() {
        useEnhancedParser = true
        Log.i("EmergencySwitch", "Enhanced parser enabled")
    }
}
```

#### 11.3.2 数据恢复
```kotlin
// 配置备份和恢复
class ConfigBackup {
    fun backupCurrentConfig() {
        val currentConfig = configManager.getCurrentConfig()
        cacheManager.saveCache("config_backup", currentConfig)
    }

    fun restoreConfig() {
        val backupConfig = cacheManager.getCache("config_backup", VodConfigResponse::class.java)
        if (backupConfig != null) {
            configManager.loadConfig(backupConfig)
        }
    }
}
```

## 12. 总结

### 12.1 预期收益

1. **解析能力提升**: 支持完整的TVBOX生态系统
2. **兼容性增强**: 100%兼容标准TVBOX配置
3. **性能优化**: 多引擎并行，智能回退机制
4. **维护性改善**: 模块化设计，易于扩展和维护
5. **用户体验**: 更稳定的解析，更丰富的内容源

### 12.2 关键成功因素

1. **严格的模块隔离**: 确保直播系统不受影响
2. **渐进式迁移**: 分阶段实施，降低风险
3. **完善的测试**: 全面的单元测试和集成测试
4. **性能监控**: 实时监控系统性能和稳定性
5. **应急预案**: 快速回滚和问题修复机制

### 12.3 后续优化方向

1. **AI智能解析**: 集成机器学习提升解析成功率
2. **分布式解析**: 支持多节点并行解析
3. **自适应优化**: 根据网络状况自动调整解析策略
4. **用户个性化**: 基于用户偏好优化解析器选择

---
**方案版本**: v1.0
**制定日期**: 2025-07-12
**预计完成**: 2025-07-22
**负责模块**: OneTV点播系统
**文档状态**: 待审核实施
