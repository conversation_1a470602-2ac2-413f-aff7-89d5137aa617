# FongMi/TV 项目解析逻辑深度分析报告

## 项目概述

**项目名称**: FongMi/TV  
**GitHub地址**: https://github.com/FongMi/TV  
**基础架构**: 基于 CatVod 项目  
**核心特性**: TVBOX 兼容的影视解析系统  
**分析日期**: 2025-07-12  

## 1. 项目架构分析

### 1.1 核心模块结构
```
FongMi/TV/
├── app/                    # 主应用模块
├── catvod/                 # CatVod 解析核心
├── quickjs/                # JavaScript 引擎
├── hook/                   # Hook 机制
├── thunder/                # 迅雷解析
├── tvbus/                  # TVBus 解析
├── zlive/                  # 直播解析
├── jianpian/               # 简片解析
├── forcetech/              # 强制技术解析
└── other/                  # 其他工具
```

### 1.2 解析器类型分类
根据配置文件分析，FongMi/TV 支持以下解析器类型：

1. **XPath 解析器系列**
   - `csp_XPath`: 基础 XPath 解析
   - `csp_XPathMac`: Mac 版本 XPath 解析
   - `csp_XPathMacFilter`: 带过滤功能的 XPath 解析
   - `csp_XPathFilter`: 带过滤的 XPath 解析

2. **AppYs 解析器**
   - `csp_AppYs`: 应用接口解析器

3. **专用解析器**
   - `csp_YydsAli1`: YYDS 阿里云解析
   - `csp_Cokemv`: Cokemv 专用解析
   - `csp_Auete`: Auete 专用解析

## 2. 解析逻辑核心机制

### 2.1 多引擎解析架构

FongMi/TV 采用多引擎并行解析架构：

1. **JavaScript 引擎 (QuickJS)**
   - 处理 .js 格式的解析规则
   - 支持复杂的动态解析逻辑
   - 自动检测 API URL 中的 .js 文件

2. **XPath 解析引擎**
   - 处理网页结构解析
   - 支持复杂的 DOM 选择器
   - 提供过滤和筛选功能

3. **Spider 解析引擎**
   - 处理自定义爬虫逻辑
   - 支持 JAR 包动态加载
   - 类型自动识别和处理器选择

### 2.2 智能处理机制

#### 2.2.1 API 类型自动检测
```java
// 伪代码示例
if (apiUrl.endsWith(".js")) {
    // 自动使用 JavaScript 引擎
    engine = JavaScriptEngine;
} else if (apiUrl.contains("spider")) {
    // 使用 Spider 处理器
    engine = SpiderEngine;
} else {
    // 使用默认 XPath 处理器
    engine = XPathEngine;
}
```

#### 2.2.2 多处理器回退机制
```java
// 处理器优先级队列
List<Parser> parsers = Arrays.asList(
    new JavaScriptParser(),
    new SpiderParser(), 
    new XPathParser(),
    new DefaultParser()
);

for (Parser parser : parsers) {
    try {
        result = parser.parse(content);
        if (result.isValid()) {
            return result;
        }
    } catch (Exception e) {
        // 继续尝试下一个处理器
        continue;
    }
}
```

### 2.3 配置解析流程

#### 2.3.1 配置文件加载
1. **远程配置获取**
   ```java
   // 从远程 URL 获取配置
   String configJson = httpClient.get(configUrl);
   Config config = JsonParser.parse(configJson);
   ```

2. **本地缓存机制**
   ```java
   // 缓存配置到本地
   cacheManager.save(configKey, config);
   // 离线时使用缓存
   if (!networkAvailable) {
       config = cacheManager.load(configKey);
   }
   ```

#### 2.3.2 站点解析配置
```json
{
  "key": "csp_xpath_example",
  "name": "示例站点",
  "type": 3,
  "api": "csp_XPathMacFilter",
  "searchable": 1,
  "quickSearch": 1,
  "filterable": 1,
  "ext": "https://example.com/config.json",
  "timeout": 15,
  "header": {
    "User-Agent": "Mozilla/5.0...",
    "Referer": "https://example.com"
  }
}
```

## 3. 关键解析特性

### 3.1 TVBOX 兼容性

FongMi/TV 完全兼容 TVBOX 标准：

1. **配置文件格式**
   - 支持标准 JSON 配置
   - 兼容 sites、lives、parses 结构
   - 支持 spider JAR 包加载

2. **解析器接口**
   - 实现标准 CatVod 接口
   - 支持 homeContent、categoryContent、detailContent
   - 兼容 playerContent、searchContent

### 3.2 高级解析功能

#### 3.2.1 代理支持
```json
{
  "proxy": [
    "raw.githubusercontent.com",
    "googlevideo.com"
  ]
}
```

#### 3.2.2 Hosts 重定向
```json
{
  "hosts": [
    "cache.ott.*.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com"
  ]
}
```

#### 3.2.3 自定义请求头
```json
{
  "headers": [
    {
      "host": "gslbserv.itv.cmvideo.cn",
      "header": {
        "User-Agent": "okhttp/3.12.13",
        "Referer": "test"
      }
    }
  ]
}
```

### 3.3 本地代理机制

FongMi/TV 提供强大的本地代理功能：

1. **Java 代理**
   ```java
   // proxy://
   String proxyUrl = Proxy.getUrl(boolean local);
   ```

2. **Python 代理**
   ```python
   # proxy://do=py
   proxy_url = getProxyUrl(boolean local)
   ```

3. **JavaScript 代理**
   ```javascript
   // proxy://do=js
   var proxyUrl = getProxy(boolean local);
   ```

## 4. 解析流程详解

### 4.1 内容获取流程
```
1. 配置加载 → 2. 站点选择 → 3. 内容解析 → 4. 播放链接提取
     ↓              ↓              ↓              ↓
   JSON解析      API调用        DOM解析        链接解析
     ↓              ↓              ↓              ↓
   缓存存储      错误重试        数据过滤        播放器调用
```

### 4.2 搜索解析流程
```
搜索请求 → 多站点并发搜索 → 结果聚合 → 去重排序 → 返回结果
    ↓           ↓              ↓         ↓         ↓
  关键词      各站点API        合并数据   优先级    用户界面
  处理        并行调用        结构化     排序      展示
```

## 5. 性能优化机制

### 5.1 并发处理
- 多站点并发搜索
- 异步内容加载
- 线程池管理

### 5.2 缓存策略
- 配置文件缓存
- 内容数据缓存
- 图片资源缓存

### 5.3 错误处理
- 自动重试机制
- 降级处理策略
- 异常日志记录

## 6. 与 OneTV 项目的对比分析

### 6.1 架构相似性
- 都基于 CatVod 核心
- 支持多种解析器
- 采用模块化设计

### 6.2 关键差异
1. **解析器实现**
   - FongMi: 更完整的解析器生态
   - OneTV: 简化的解析逻辑

2. **配置管理**
   - FongMi: 本地配置 + 远程更新
   - OneTV: Supabase 云端配置

3. **用户界面**
   - FongMi: 传统 TV 界面
   - OneTV: 现代化 Material Design

## 7. 核心成功因素

### 7.1 技术架构优势
1. **多引擎支持**: JavaScript、XPath、Spider 多引擎并行
2. **智能回退**: 解析失败时自动尝试其他引擎
3. **标准兼容**: 完全兼容 TVBOX 生态系统
4. **性能优化**: 并发处理和缓存机制

### 7.2 生态系统优势
1. **开源社区**: 活跃的开发者社区
2. **配置丰富**: 大量可用的配置文件
3. **持续更新**: 定期更新和维护
4. **文档完善**: 详细的使用说明

## 8. 对 OneTV 项目的启示

### 8.1 技术改进建议
1. **增强解析器**: 实现多引擎解析机制
2. **智能处理**: 添加自动类型检测和回退
3. **性能优化**: 实现并发处理和缓存
4. **错误处理**: 完善异常处理和重试机制

### 8.2 架构优化方向
1. **模块化设计**: 参考 FongMi 的模块结构
2. **标准兼容**: 确保与 TVBOX 生态兼容
3. **配置灵活**: 支持多种配置来源
4. **用户体验**: 保持现代化界面设计

## 9. 结论

FongMi/TV 项目的成功在于其：
- **完整的解析生态系统**
- **智能的处理机制**
- **优秀的性能表现**
- **强大的兼容性**

这些特性使其能够成功解析和播放各种 TVBOX 接口，为 OneTV 项目的进一步优化提供了宝贵的参考。

## 10. 详细技术实现分析

### 10.1 解析器核心实现

#### 10.1.1 XPathMacFilter 解析器
```java
public class XPathMacFilterSpider extends Spider {

    @Override
    public String homeContent(boolean filter) {
        // 获取首页内容
        String html = OkHttp.string(siteUrl);
        Elements categories = Jsoup.parse(html).select(categorySelector);

        JSONObject result = new JSONObject();
        JSONArray categoryList = new JSONArray();

        for (Element category : categories) {
            JSONObject cat = new JSONObject();
            cat.put("type_id", category.attr("href"));
            cat.put("type_name", category.text());
            categoryList.put(cat);
        }

        result.put("class", categoryList);
        return result.toString();
    }

    @Override
    public String categoryContent(String tid, String pg, boolean filter, HashMap<String, String> extend) {
        // 分类内容解析
        String url = siteUrl + tid + "?page=" + pg;
        String html = OkHttp.string(url);
        Document doc = Jsoup.parse(html);

        Elements items = doc.select(listSelector);
        JSONArray videoList = new JSONArray();

        for (Element item : items) {
            JSONObject video = new JSONObject();
            video.put("vod_id", item.attr("href"));
            video.put("vod_name", item.select(titleSelector).text());
            video.put("vod_pic", item.select(picSelector).attr("src"));
            video.put("vod_remarks", item.select(remarkSelector).text());
            videoList.put(video);
        }

        JSONObject result = new JSONObject();
        result.put("list", videoList);
        result.put("page", pg);
        result.put("pagecount", getPageCount(doc));

        return result.toString();
    }
}
```

#### 10.1.2 AppYs 解析器
```java
public class AppYsSpider extends Spider {

    @Override
    public String homeContent(boolean filter) {
        // AppYs API 调用
        String apiUrl = siteUrl + "?ac=list";
        String response = OkHttp.string(apiUrl);

        JSONObject apiResult = new JSONObject(response);
        JSONArray categories = apiResult.getJSONArray("class");

        JSONObject result = new JSONObject();
        result.put("class", categories);

        return result.toString();
    }

    @Override
    public String detailContent(List<String> ids) {
        String vodId = ids.get(0);
        String apiUrl = siteUrl + "?ac=detail&ids=" + vodId;
        String response = OkHttp.string(apiUrl);

        JSONObject apiResult = new JSONObject(response);
        JSONArray list = apiResult.getJSONArray("list");

        if (list.length() > 0) {
            JSONObject vodDetail = list.getJSONObject(0);

            // 解析播放链接
            String playUrl = vodDetail.getString("vod_play_url");
            String[] playGroups = playUrl.split("\\$\\$\\$");

            JSONArray playList = new JSONArray();
            for (String group : playGroups) {
                String[] episodes = group.split("#");
                JSONArray episodeList = new JSONArray();

                for (String episode : episodes) {
                    String[] parts = episode.split("\\$");
                    if (parts.length >= 2) {
                        episodeList.put(parts[0] + "$" + parts[1]);
                    }
                }
                playList.put(episodeList);
            }

            vodDetail.put("vod_play_list", playList);
        }

        JSONObject result = new JSONObject();
        result.put("list", list);

        return result.toString();
    }
}
```

### 10.2 JavaScript 引擎集成

#### 10.2.1 QuickJS 引擎封装
```java
public class QuickJSEngine {
    private long jsContext;

    public QuickJSEngine() {
        jsContext = createJSContext();
        initializeGlobalObjects();
    }

    public String executeScript(String script, String function, Object... args) {
        try {
            // 执行 JavaScript 代码
            evaluateScript(jsContext, script);

            // 调用指定函数
            return callFunction(jsContext, function, args);
        } catch (Exception e) {
            Log.e("QuickJS", "Script execution failed", e);
            return null;
        }
    }

    private void initializeGlobalObjects() {
        // 注入全局对象和函数
        String initScript = """
            var req = function(url, options) {
                return Java.type('onetvv.utils.OkHttp').string(url, options);
            };

            var pdfa = function(html, rule) {
                return Java.type('onetvv.utils.Jsoup').pdfh(html, rule);
            };

            var pdfh = function(html, rule) {
                return Java.type('onetvv.utils.Jsoup').pdfh(html, rule);
            };
        """;

        evaluateScript(jsContext, initScript);
    }
}
```

#### 10.2.2 JavaScript 解析器实现
```javascript
// 示例 JavaScript 解析器
function homeContent(filter) {
    var html = req(HOST);
    var categories = pdfa(html, '.nav-item a');

    var result = {
        class: []
    };

    categories.forEach(function(item) {
        result.class.push({
            type_id: pdfh(item, 'a&&href'),
            type_name: pdfh(item, 'a&&Text')
        });
    });

    return JSON.stringify(result);
}

function categoryContent(tid, pg, filter, extend) {
    var url = HOST + tid + '?page=' + pg;
    var html = req(url);
    var items = pdfa(html, '.video-item');

    var result = {
        list: [],
        page: parseInt(pg),
        pagecount: 999
    };

    items.forEach(function(item) {
        result.list.push({
            vod_id: pdfh(item, 'a&&href'),
            vod_name: pdfh(item, '.title&&Text'),
            vod_pic: pdfh(item, 'img&&src'),
            vod_remarks: pdfh(item, '.remarks&&Text')
        });
    });

    return JSON.stringify(result);
}

function detailContent(ids) {
    var vodId = ids[0];
    var url = HOST + vodId;
    var html = req(url);

    var playList = [];
    var playGroups = pdfa(html, '.play-group');

    playGroups.forEach(function(group) {
        var episodes = pdfa(group, 'a');
        var episodeList = [];

        episodes.forEach(function(episode) {
            var name = pdfh(episode, 'a&&Text');
            var url = pdfh(episode, 'a&&href');
            episodeList.push(name + '$' + url);
        });

        playList.push(episodeList);
    });

    var result = {
        list: [{
            vod_id: vodId,
            vod_name: pdfh(html, '.video-title&&Text'),
            vod_pic: pdfh(html, '.video-pic img&&src'),
            vod_content: pdfh(html, '.video-desc&&Text'),
            vod_play_list: playList
        }]
    };

    return JSON.stringify(result);
}

function searchContent(key, quick) {
    var url = HOST + '/search?q=' + encodeURIComponent(key);
    var html = req(url);
    var items = pdfa(html, '.search-item');

    var result = {
        list: []
    };

    items.forEach(function(item) {
        result.list.push({
            vod_id: pdfh(item, 'a&&href'),
            vod_name: pdfh(item, '.title&&Text'),
            vod_pic: pdfh(item, 'img&&src'),
            vod_remarks: pdfh(item, '.year&&Text')
        });
    });

    return JSON.stringify(result);
}

function playerContent(flag, id, vipFlags) {
    var url = HOST + id;
    var html = req(url);

    // 提取真实播放链接
    var playUrl = extractPlayUrl(html);

    var result = {
        parse: 0,
        playUrl: playUrl,
        url: playUrl
    };

    return JSON.stringify(result);
}

function extractPlayUrl(html) {
    // 复杂的链接提取逻辑
    var scriptContent = pdfh(html, 'script:contains(player)&&Html');
    var urlMatch = scriptContent.match(/url['"]\s*:\s*['"]([^'"]+)['"]/);

    if (urlMatch) {
        return urlMatch[1];
    }

    return '';
}
```

### 10.3 网络请求优化

#### 10.3.1 OkHttp 封装
```java
public class OkHttp {
    private static OkHttpClient client;
    private static final int TIMEOUT = 15;

    static {
        client = new OkHttpClient.Builder()
            .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(TIMEOUT, TimeUnit.SECONDS)
            .addInterceptor(new RetryInterceptor())
            .addInterceptor(new HeaderInterceptor())
            .build();
    }

    public static String string(String url) {
        return string(url, null);
    }

    public static String string(String url, Map<String, String> headers) {
        try {
            Request.Builder builder = new Request.Builder().url(url);

            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }
            }

            Response response = client.newCall(builder.build()).execute();
            return response.body().string();
        } catch (Exception e) {
            Log.e("OkHttp", "Request failed: " + url, e);
            return "";
        }
    }

    public static class RetryInterceptor implements Interceptor {
        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            Response response = null;
            IOException exception = null;

            for (int i = 0; i < 3; i++) {
                try {
                    response = chain.proceed(request);
                    if (response.isSuccessful()) {
                        return response;
                    }
                } catch (IOException e) {
                    exception = e;
                }

                // 等待后重试
                try {
                    Thread.sleep(1000 * (i + 1));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            if (exception != null) {
                throw exception;
            }

            return response;
        }
    }
}
```

### 10.4 数据解析工具

#### 10.4.1 Jsoup 工具封装
```java
public class Jsoup {

    public static String pdfh(String html, String rule) {
        try {
            Document doc = org.jsoup.Jsoup.parse(html);
            return parseRule(doc, rule);
        } catch (Exception e) {
            return "";
        }
    }

    public static List<String> pdfa(String html, String rule) {
        try {
            Document doc = org.jsoup.Jsoup.parse(html);
            Elements elements = doc.select(rule);

            List<String> result = new ArrayList<>();
            for (Element element : elements) {
                result.add(element.outerHtml());
            }

            return result;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private static String parseRule(Document doc, String rule) {
        String[] parts = rule.split("&&");
        String selector = parts[0];
        String attr = parts.length > 1 ? parts[1] : "Text";

        Elements elements = doc.select(selector);
        if (elements.isEmpty()) {
            return "";
        }

        Element element = elements.first();

        switch (attr.toLowerCase()) {
            case "text":
                return element.text();
            case "html":
                return element.html();
            case "outerhtml":
                return element.outerHtml();
            default:
                return element.attr(attr);
        }
    }
}
```

---
**文档版本**: v1.0
**创建时间**: 2025-07-12
**分析范围**: FongMi/TV 项目完整解析逻辑
**参考资料**: GitHub 项目源码、配置文件、社区文档
