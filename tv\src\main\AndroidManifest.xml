<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />

    <queries>
        <package android:name="com.google.android.webview" />
    </queries>


    <!-- 在现有权限声明下方添加 -->

    <!-- 在AndroidManifest.xml添加 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />

    <application


        android:name=".MyTVApplication"
        android:allowBackup="true"
        android:banner="@drawable/tv_banner"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:screenOrientation="sensorLandscape"
        android:supportsRtl="true"
        android:theme="@style/Theme.MyTV"
        android:usesCleartextTraffic="true"
        tools:ignore="DiscouragedApi"
        tools:targetApi="n"
        tools:replace="android:banner">

        <activity
            android:name=".LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
        <!-- 新增注册和忘记密码 Activity -->
        <activity
            android:name=".RegisterActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
        <activity
            android:name=".ForgotPasswordActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />

        <activity
            android:name=".MainActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|screenLayout|keyboardHidden"
            android:exported="true"
            android:resizeableActivity="true"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>

        <receiver
            android:name=".BootReceiver"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.RECEIVE_BOOT_COMPLETED">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.FileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Supabase认证活动 -->
        <activity
            android:name=".supabase.SupabaseLoginActivity"
            android:exported="true"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:theme="@style/Theme.MyTV">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".supabase.SupabaseRegisterActivity"
            android:exported="false"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:theme="@style/Theme.MyTV" />
        <activity
            android:name=".supabase.SupabaseForgotPasswordActivity"
            android:exported="false"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:theme="@style/Theme.MyTV" />
        <!-- 新增用户个人中心页面 -->
        <activity
            android:name=".supabase.SupabaseUserProfileActivity"
            android:exported="false"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:theme="@style/Theme.Transparent" />
        <!-- 暂时注释掉DefaultRedirectActivity
        <activity
            onetv:name="io.github.jan.supabase.auth.redirects.DefaultRedirectActivity"
            onetv:exported="true">
            <intent-filter>
                <action onetv:name="onetv.intent.action.VIEW" />
                <category onetv:name="onetv.intent.category.DEFAULT" />
                <category onetv:name="onetv.intent.category.BROWSABLE" />
                <data onetv:scheme="io.onetv.app" onetv:host="auth-callback" />
            </intent-filter>
        </activity>
        -->

        <!-- movie模块的组件通过库依赖自动合并 -->

        <!-- QuickJS Provider - 用于初始化QuickJS库 -->
        <provider
            android:name="top.cywin.onetv.movie.quickjs.Provider"
            android:authorities="${applicationId}.quickjs.provider"
            android:exported="false"
            android:enabled="true" />

    </application>

</manifest>