package top.cywin.onetv.movie.api.loader;

import top.cywin.onetv.movie.App;
import com.github.catvod.crawler.Spider;
import com.github.catvod.crawler.SpiderNull;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class JsLoader {

    private final ConcurrentHashMap<String, Spider> spiders;
    private String recent;

    public JsLoader() {
        spiders = new ConcurrentHashMap<>();
    }

    public void clear() {
        for (Spider spider : spiders.values())
            App.execute(spider::destroy);
        spiders.clear();
    }

    public void setRecent(String recent) {
        this.recent = recent;
    }

    public Spider getSpider(String key, String api, String ext, String jar) {
        android.util.Log.d("ONETV_JS_LOADER", "📜 JsLoader处理JavaScript Spider: key=" + key + ", api=" + api);
        try {
            if (spiders.containsKey(key)) {
                android.util.Log.d("ONETV_JS_LOADER", "✅ 从缓存获取JavaScript Spider");
                return spiders.get(key);
            }

            android.util.Log.d("ONETV_JS_LOADER", "🔄 创建新的JavaScript Spider实例");
            android.util.Log.d("ONETV_JS_LOADER", "🔄 调用BaseLoader.get().dex(jar): " + jar);
            dalvik.system.DexClassLoader dexLoader = BaseLoader.get().dex(jar);
            android.util.Log.d("ONETV_JS_LOADER", "📊 dex返回结果: " + (dexLoader != null ? "成功" : "null"));

            if (dexLoader == null) {
                android.util.Log.e("ONETV_JS_LOADER", "❌ DexClassLoader为null，无法创建JavaScript Spider");
                throw new RuntimeException("DexClassLoader为null");
            }

            // 🔧 增强JavaScript内容检查
            try {
                String jsContent = top.cywin.onetv.movie.quickjs.utils.Module.get().fetch(api);
                android.util.Log.d("ONETV_JS_LOADER", "📊 JavaScript内容长度: " + jsContent.length());
                android.util.Log.d("ONETV_JS_LOADER",
                        "📊 JavaScript内容预览: " + jsContent.substring(0, Math.min(200, jsContent.length())));
            } catch (Exception contentCheckError) {
                android.util.Log.w("ONETV_JS_LOADER", "⚠️ JavaScript内容检查失败: " + contentCheckError.getMessage());
            }

            android.util.Log.d("ONETV_JS_LOADER", "🔄 创建QuickJS Spider实例");
            Spider spider = new top.cywin.onetv.movie.quickjs.crawler.Spider(key, api, dexLoader);
            android.util.Log.d("ONETV_JS_LOADER", "✅ JavaScript Spider实例创建成功");

            spider.init(App.get(), ext);
            android.util.Log.d("ONETV_JS_LOADER", "✅ JavaScript Spider初始化完成");

            spiders.put(key, spider);
            android.util.Log.d("ONETV_JS_LOADER", "✅ JavaScript Spider缓存成功，返回实例: " + spider.getClass().getSimpleName());
            return spider;
        } catch (Throwable e) {
            android.util.Log.e("ONETV_JS_LOADER", "❌ JavaScript Spider创建失败: " + e.getMessage(), e);
            android.util.Log.e("ONETV_JS_LOADER", "❌ 异常类型: " + e.getClass().getSimpleName());
            android.util.Log.e("ONETV_JS_LOADER", "❌ API URL: " + api);
            android.util.Log.e("ONETV_JS_LOADER", "❌ JAR路径: " + jar);

            if (e.getCause() != null) {
                android.util.Log.e("ONETV_JS_LOADER", "❌ 根本原因: " + e.getCause().getMessage(), e.getCause());
            }

            android.util.Log.d("ONETV_JS_LOADER", "🔄 返回SpiderNull作为默认实现");
            return new SpiderNull();
        }
    }

    public Object[] proxyInvoke(Map<String, String> params) {
        try {
            if (!params.containsKey("siteKey"))
                return spiders.get(recent).proxyLocal(params);
            return BaseLoader.get().getSpider(params).proxyLocal(params);
        } catch (Throwable e) {
            e.printStackTrace();
            return null;
        }
    }
}
