package top.cywin.onetv.core.designsystem.theme

import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import top.cywin.onetv.core.designsystem.R

val HarmonyOSSans = FontFamily(
    Font(R.font.harmonyos_sans_black, FontWeight.Black),
    <PERSON><PERSON>(R.font.harmonyos_sans_bold, FontWeight.Bold),
    <PERSON><PERSON>(R.font.harmonyos_sans_light, FontWeight.Light),
    <PERSON>ont(R.font.harmonyos_sans_medium, FontWeight.Medium),
    Font(R.font.harmonyos_sans_regular, FontWeight.Normal),
    Font(R.font.harmonyos_sans_thin, FontWeight.Thin)
)