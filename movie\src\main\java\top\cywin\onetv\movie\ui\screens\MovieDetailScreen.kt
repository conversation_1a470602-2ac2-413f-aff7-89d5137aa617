package top.cywin.onetv.movie.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Sort
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import top.cywin.onetv.movie.viewmodel.MovieDetailViewModel
import top.cywin.onetv.movie.viewmodel.DetailUiState
import top.cywin.onetv.movie.bean.Vod
import top.cywin.onetv.movie.bean.Flag
import top.cywin.onetv.movie.ui.model.PlayFlag
import top.cywin.onetv.movie.ui.model.MovieItem
import top.cywin.onetv.movie.ui.model.Episode
import top.cywin.onetv.movie.MovieApp
import android.util.Log
import coil.compose.AsyncImage

/**
 * OneTV Movie详情页面 - 按照FongMi_TV整合指南重构
 */
@Composable
fun MovieDetailScreen(
    vodId: String,
    siteKey: String = "",
    navController: NavController,
    viewModel: MovieDetailViewModel = viewModel {
        MovieDetailViewModel()
    }
) {
    Log.d("ONETV_DETAIL_SCREEN", "🎬 [电影ID跟踪] 详情页面启动: vodId=$vodId, siteKey=$siteKey")
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(vodId, siteKey) {
        Log.d("ONETV_DETAIL_SCREEN", "🎬 [FongMi_TV兼容] 开始加载电影详情: vodId=$vodId, siteKey=$siteKey")
        Log.d("ONETV_DETAIL_SCREEN", "🎯 [电影ID跟踪] 详情页面初始化，调用loadMovieDetail")
        viewModel.loadMovieDetail(vodId, siteKey)
    }

    // ✅ UI内容渲染
    DetailContent(
        uiState = uiState,
        onBack = { navController.popBackStack() },
        onPlay = { episode, episodeIndex ->
            navController.navigate(top.cywin.onetv.movie.navigation.MovieRoutes.player(vodId, episodeIndex, siteKey))
        },
        onToggleFavorite = { viewModel.toggleFavorite() },
        onFlagSelect = { flag -> viewModel.selectFlag(flag) },
        onEpisodeSelect = { episode -> viewModel.selectEpisode(episode) },
        onShowFlagSelector = { viewModel.showFlagSelector() },
        onHideFlagSelector = { viewModel.hideFlagSelector() },
        onShowEpisodeSelector = { viewModel.showEpisodeSelector() },
        onHideEpisodeSelector = { viewModel.hideEpisodeSelector() }
    )
}

@Composable
private fun DetailContent(
    uiState: DetailUiState,
    onBack: () -> Unit,
    onPlay: (Episode, Int) -> Unit,
    onToggleFavorite: () -> Unit,
    onFlagSelect: (PlayFlag) -> Unit,
    onEpisodeSelect: (Episode) -> Unit,
    onShowFlagSelector: () -> Unit,
    onHideFlagSelector: () -> Unit,
    onShowEpisodeSelector: () -> Unit,
    onHideEpisodeSelector: () -> Unit
) {
    when {
        uiState.isLoading -> {
            LoadingScreen(message = "正在加载详情...")
        }
        uiState.error != null -> {
            val errorMessage = uiState.error ?: "未知错误"
            ErrorScreen(
                error = errorMessage,
                onRetry = { /* 重试逻辑 */ },
                onBack = onBack
            )
        }
        else -> {
            // 🎬 新的电影详情页面布局架构
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                // 🎬 注释掉顶部导航栏 - 按用户要求释放空间
                /*
                TopAppBar(
                    title = { Text(uiState.movie?.vodName ?: "详情") },
                    navigationIcon = {
                        IconButton(onClick = onBack) {
                            Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
                        }
                    }
                )
                */

                // 🎬 主要内容区域 - 固定高度，不滚动
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // A区域（上左）+ B区域（上右）：电影信息 + 播放器
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(0.5f), // 占用50%的高度 - 按用户要求调整
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // A区域：电影详细信息（左侧）
                        uiState.movie?.let { movie ->
                            MovieInfoSection(
                                movie = movie,
                                modifier = Modifier.weight(0.55f), // 占用55%的宽度 - 调整以适应播放器
                                onToggleFavorite = onToggleFavorite,
                                isFavorite = uiState.isFavorite
                            )
                        }

                        // B区域：内嵌播放器（右侧）- 按用户要求占据45%区域
                        if (uiState.episodes.isNotEmpty() && uiState.currentEpisode != null) {
                            EmbeddedPlayerSection(
                                episode = uiState.currentEpisode!!,
                                isParsingPlayUrl = uiState.isParsingPlayUrl,
                                modifier = Modifier
                                    .weight(0.45f) // 占用45%的宽度 - 按用户要求调整
                                    .height(200.dp), // 🔥 修复：固定播放器高度，与功能按钮对齐
                                onPlay = { episode ->
                                    // 🔥 修复：点击播放器进入全屏播放
                                    val episodeIndex = uiState.episodes.indexOf(episode)
                                    onPlay(episode, episodeIndex)
                                }
                            )
                        }
                    }

                    // C区域：播放线路选择（中间）
                    if (uiState.playFlags.isNotEmpty()) {
                        PlayFlagSection(
                            flags = uiState.playFlags,
                            selectedFlag = uiState.currentFlag,
                            onFlagSelect = onFlagSelect,
                            modifier = Modifier.height(60.dp) // 固定高度
                        )
                    }

                    // D区域：选集播放（下方）
                    if (uiState.episodes.isNotEmpty()) {
                        EpisodeSection(
                            episodes = uiState.episodes,
                            selectedEpisode = uiState.currentEpisode,
                            onEpisodeSelect = onEpisodeSelect,
                            onPlay = onPlay,
                            modifier = Modifier.weight(0.35f) // 占用剩余35%的高度
                        )
                    }
                }
            }
        }
    }
}

// ✅ 按照指南添加必要的辅助Composable函数

@Composable
private fun LoadingScreen(message: String) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text(text = message)
        }
    }
}

@Composable
private fun ErrorScreen(
    error: String,
    onRetry: () -> Unit,
    onBack: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(onClick = onRetry) {
                    Text("重试")
                }
                OutlinedButton(onClick = onBack) {
                    Text("返回")
                }
            }
        }
    }
}

@Composable
private fun MovieInfoSection(
    movie: MovieItem,
    modifier: Modifier = Modifier,
    onToggleFavorite: () -> Unit,
    isFavorite: Boolean
) {
    // 🎬 A区域：电影详细信息（参考架构图左侧区域）
    Column(
        modifier = modifier.height(200.dp), // 🔥 修复：固定高度与播放器对齐
        verticalArrangement = Arrangement.SpaceBetween // 🔥 修复：上下分布，功能按钮固定在底部
    ) {
        // 🎬 电影基本信息区域 - 按用户要求调整字体大小和显示内容
        Column(
            modifier = Modifier.weight(1f), // 占用剩余空间
            verticalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            // 剧名 - 比下方信息字体大一号
            Text(
                text = movie.vodName,
                style = MaterialTheme.typography.headlineSmall.copy(fontSize = 18.sp),
                fontWeight = FontWeight.Bold,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            // 来源 - 显示完整站点名称
            Text(
                text = "来源: ${getSiteDisplayName(movie.siteKey)}",
                style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                color = MaterialTheme.colorScheme.primary
            )

            // 演员 - 清理特殊字符，只显示一行
            if (movie.vodActor.isNotEmpty()) {
                Text(
                    text = "演员: ${cleanActorDirectorName(movie.vodActor)}",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 导演 - 清理特殊字符
            if (movie.vodDirector.isNotEmpty()) {
                Text(
                    text = "导演: ${cleanActorDirectorName(movie.vodDirector)}",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 🎬 删除播放地址行 - 按用户要求留给简介更多空间

            // 内容简介 - 限制高度，为功能按钮留出空间
            if (movie.vodContent.isNotEmpty()) {
                Text(
                    text = "简介: ${movie.vodContent}",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                    maxLines = 3, // 减少行数，为按钮留空间
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 🔥 修复：功能按钮区域固定在底部，与播放器下边框齐平
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(40.dp), // 固定按钮区域高度
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
                // 快速搜索 - 使用FilterChip样式
                FilterChip(
                    onClick = { /* TODO: 实现快速搜索 */ },
                    label = {
                        Text(
                            text = "快速搜索",
                            fontSize = 12.sp
                        )
                    },
                    selected = false,
                    modifier = Modifier.weight(1f),
                    leadingIcon = {
                        Icon(
                            Icons.Default.Search,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )

                // 集数正/倒序 - 使用FilterChip样式
                FilterChip(
                    onClick = { /* TODO: 实现集数排序 */ },
                    label = {
                        Text(
                            text = "倒序",
                            fontSize = 12.sp
                        )
                    },
                    selected = false,
                    modifier = Modifier.weight(1f),
                    leadingIcon = {
                        Icon(
                            Icons.Default.Sort,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )

                // 加入收藏 - 使用FilterChip样式
                FilterChip(
                    onClick = onToggleFavorite,
                    label = {
                        Text(
                            text = if (isFavorite) "已收藏" else "收藏",
                            fontSize = 12.sp
                        )
                    },
                    selected = isFavorite,
                    modifier = Modifier.weight(1f),
                    leadingIcon = {
                        Icon(
                            if (isFavorite) Icons.Default.Favorite else Icons.Default.Add,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )

                // 内容简介 - 使用FilterChip样式
                FilterChip(
                    onClick = { /* TODO: 显示完整简介 */ },
                    label = {
                        Text(
                            text = "简介",
                            fontSize = 12.sp
                        )
                    },
                    selected = false,
                    modifier = Modifier.weight(1f),
                    leadingIcon = {
                        Icon(
                            Icons.Default.Info,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )
            }
    }
}

// 🎬 B区域：内嵌播放器（参考架构图右侧区域）
@Composable
private fun EmbeddedPlayerSection(
    episode: Episode,
    isParsingPlayUrl: Boolean = false,
    modifier: Modifier = Modifier,
    onPlay: ((Episode) -> Unit)? = null
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
                .clickable {
                    // 🔥 修复：点击播放器进入全屏播放
                    Log.d("ONETV_EMBEDDED_PLAYER", "🎬 用户点击播放器进入全屏: ${episode.name}")
                    onPlay?.invoke(episode)
                },
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                if (isParsingPlayUrl) {
                    // 🔥 显示播放地址解析状态
                    CircularProgressIndicator(
                        color = Color.White,
                        modifier = Modifier.size(48.dp)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "正在解析播放地址",
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = episode.name,
                        color = Color.White.copy(alpha = 0.7f),
                        style = MaterialTheme.typography.bodySmall,
                        textAlign = TextAlign.Center
                    )
                } else if (episode.playUrl.isNotEmpty()) {
                    // 🔥 播放地址解析成功，显示播放按钮
                    Icon(
                        Icons.Default.PlayArrow,
                        contentDescription = "播放",
                        tint = Color.White,
                        modifier = Modifier.size(64.dp)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = episode.name,
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "点击进入全屏播放",
                        color = Color.White.copy(alpha = 0.7f),
                        style = MaterialTheme.typography.bodySmall,
                        textAlign = TextAlign.Center
                    )
                } else {
                    // 🔥 默认状态：等待播放地址解析
                    Icon(
                        Icons.Default.PlayArrow,
                        contentDescription = "播放",
                        tint = Color.White.copy(alpha = 0.5f),
                        modifier = Modifier.size(64.dp)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = episode.name,
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "等待播放地址解析",
                        color = Color.White.copy(alpha = 0.7f),
                        style = MaterialTheme.typography.bodySmall,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

@Composable
private fun InfoRow(label: String, value: String?) {
    value?.let {
        if (it.isNotEmpty()) {
            Row {
                Text(
                    text = "$label: ",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}

// 🎬 C区域：播放线路选择（参考架构图中间区域）
@Composable
private fun PlayFlagSection(
    flags: List<PlayFlag>,
    selectedFlag: PlayFlag?,
    onFlagSelect: (PlayFlag) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 🎬 注释掉"在线播放"标题 - 按用户要求释放空间
        /*
        Text(
            text = "在线播放",
            style = MaterialTheme.typography.titleMedium.copy(fontSize = 14.sp),
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp),
            color = MaterialTheme.colorScheme.primary
        )
        */

        // 🎬 线路标签可左右滑动
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            items(flags) { flag ->
                FilterChip(
                    onClick = { onFlagSelect(flag) },
                    label = {
                        Text(
                            text = flag.flag ?: "未知线路",
                            fontSize = 12.sp
                        )
                    },
                    selected = selectedFlag == flag,
                    modifier = Modifier.height(36.dp)
                )
            }
        }
    }
}

// 🎬 D区域：选集播放（参考架构图下方区域）
@Composable
private fun EpisodeSection(
    episodes: List<Episode>,
    selectedEpisode: Episode?,
    onEpisodeSelect: (Episode) -> Unit,
    onPlay: (Episode, Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 🎬 注释掉"选集播放"标题 - 按用户要求释放空间
        /*
        Text(
            text = "选集播放",
            style = MaterialTheme.typography.titleMedium.copy(fontSize = 14.sp),
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp),
            color = MaterialTheme.colorScheme.primary
        )
        */

        // 🎬 剧集网格布局 - 固定每行8个标签，平均分布
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 🎬 固定每行显示8个集数标签 - 按用户要求
            val itemsPerRow = 8
            val chunkedEpisodes = episodes.chunked(itemsPerRow)

            items(chunkedEpisodes) { rowEpisodes ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    rowEpisodes.forEachIndexed { localIndex, episode ->
                        val globalIndex = episodes.indexOf(episode)
                        EpisodeChip(
                            episode = episode,
                            isSelected = selectedEpisode == episode,
                            onClick = {
                                onEpisodeSelect(episode)
                                onPlay(episode, globalIndex)
                            },
                            modifier = Modifier.weight(1f) // 平均分布，铺满一行
                        )
                    }

                    // 如果这一行不足8个，用空白填充
                    repeat(itemsPerRow - rowEpisodes.size) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
        }
    }
}

@Composable
private fun EpisodeChip(
    episode: Episode,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    FilterChip(
        onClick = onClick,
        label = {
            Text(
                text = episode.name,
                style = MaterialTheme.typography.bodySmall.copy(fontSize = 11.sp),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center, // 🎬 文字左右居中
                modifier = Modifier.fillMaxWidth() // 确保文字在整个标签宽度内居中
            )
        },
        selected = isSelected,
        modifier = modifier
            .height(32.dp), // 调整高度以适应新布局
        leadingIcon = if (isSelected) {
            {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = null,
                    modifier = Modifier.size(14.dp)
                )
            }
        } else null
    )
}

// 🎬 工具函数：清理演员和导演名字中的特殊字符
private fun cleanActorDirectorName(name: String): String {
    if (name.isEmpty()) return name

    // 移除方括号及其内容，例如：[a=cr:{"id":"宿宇杰/{pg}
    val withoutBrackets = name.replace(Regex("\\[.*?\\]"), "")

    // 移除花括号及其内容
    val withoutBraces = withoutBrackets.replace(Regex("\\{.*?\\}"), "")

    // 移除其他特殊字符，保留中文、英文、数字和常见标点
    val cleaned = withoutBraces.replace(Regex("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s,，、·]"), "")

    // 清理多余的空格和逗号
    return cleaned.replace(Regex("\\s+"), " ")
                  .replace(Regex("[,，]+"), "，")
                  .trim()
}

// 🎬 工具函数：获取站点显示名称
private fun getSiteDisplayName(siteKey: String): String {
    return try {
        val site = top.cywin.onetv.movie.api.config.VodConfig.get().getSite(siteKey)
        site?.name ?: siteKey.replace("onetv_", "").replace("_", " ")
    } catch (e: Exception) {
        Log.w("MovieDetailScreen", "获取站点名称失败: $siteKey", e)
        siteKey.replace("onetv_", "").replace("_", " ")
    }
}
