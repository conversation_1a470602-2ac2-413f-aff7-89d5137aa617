# ONETV 用户协议与免责声明

**最后更新：2025年06月18日 | 版本：2.0.0**

> **重要提示**：本文档为用户注册时必读内容。继续使用本软件视为您已完整阅读并接受本协议的全部条款。

## 一、软件简介

壹来电视(OneTV)是一款基于Android TV的流媒体应用，使用Su云作为后端服务。本应用采用Kotlin Multiplatform技术，支持Android TV和手机设备。

### 核心特性

- 全新透明UI界面设计
- Su后端集成
- 多平台支持
- 个性化用户体验
- 高效缓存机制

## 二、软件性质与基本声明

1. **使用限制**：本软件仅供技术研究与学习交流使用，严禁用于任何商业场景或非法用途。用户不得对本软件进行二次贩卖、捆绑销售或用于盈利性服务。
2. **内容免责**：

   - 本软件自身不制作、不存储、不传播任何音视频内容
   - 所有直播流均来源于用户自定义添加或第三方网络公开资源
   - 开发者对内容的合法性、准确性及稳定性不做任何担保，亦不承担相关责任
   - 播放内容完全取决于用户自行配置或网络公开资源
3. **开发性质**：

   - 本软件为个人开发者开源学习项目，无商业团队运营、无公司主体
   - 软件内涉及的代码、UI设计及文档资源均基于开发者社区公开贡献构建
   - 开发者仅进行技术整合与界面优化

## 三、用户责任与承诺

1. **合规使用**：

   - 您应遵守所在地区法律法规，合理使用网络资源
   - 严禁利用本软件从事违法活动或接入非法内容源
   - 强烈建议用户自行添加版权合规内容源
   - 发现异常内容应立即断开播放并自行检查节目源头
   - 应用仅供个人学习和测试使用，请在24小时内删除
2. **风险承担**：

   - 用户需自行确保所播放内容符合所在地法律法规
   - 因用户行为导致的版权纠纷、数据滥用等后果需自行承担，与本软件及开发者无关
   - 您承诺作为具备完全民事行为能力的自然人，在使用本软件前已充分理解并自愿承担所有风险
3. **使用风险**：您知悉并同意使用本软件可能存在的包括但不限于以下风险：

   - 因网络传输导致的内容延迟、卡顿或解析失败
   - 第三方直播源的内容合法性风险
   - 软件运行导致的设备耗电量增加
   - 其他不可预见的兼容性问题
   - 使用公共网络播放可能导致数据被第三方截获
   - 不保证与所有设备/系统版本兼容，Root设备或自定义ROM导致的故障不予支持

## 四、隐私与数据处理

1. **数据收集清单**：

   | 数据类型       | 用途                 | 存储方   | 留存周期             |
   | -------------- | -------------------- | -------- | -------------------- |
   | 设备基础信息   | 崩溃修复/版本兼容性  | 本地     | 卸载即删除           |
   | 匿名播放日志   | 解码器性能优化       | Google云 | 30天自动清除         |
   | 第三方账号信息 | 依第三方公司服务政策 | Su云     | 依第三方公司服务政策 |
2. **用户数据保护**：

   - 本软件内置Google Analytics仅用于采集匿名化功能使用数据（如频道切换频次、功能使用时长）
   - 用于优化软件性能，绝不收集设备信息、用户身份等敏感数据
   - 服务器仅为管理个人账号信息，不会接触到您个人设备上的密码、手机号等敏感信息
3. **用户控制权**：

   - 数据追踪开关：应用启动时将告知相关数据收集情况
   - 本地数据清除：通过"设置-恢复出厂"可彻底删除本设备所有记录（不含第三方服务器数据）
   - 账号管理：如需注销账号，请通过软件内联系方式联系开发者修改/删除权限

## 五、开发者免责声明

1. **内容免责**：开发者不对以下事项承担任何责任：

   - 因使用本软件导致的直接或间接损失
   - 第三方内容的知识产权纠纷
   - 软件使用引发的法律风险
   - 因软件漏洞导致的数据安全问题
   - 因不可抗力导致的服务中断或数据丢失
2. **技术免责**：

   - 不保证与所有设备/系统版本兼容
   - 禁止通过技术手段劫持软件通信协议，因此造成的账号封禁后果自负
   - 本服务可能因不可预知原因导致功能暂时不可用，开发者不承担连带责任
   - 升级至2.0.0版本与旧版本不完全兼容，升级后可能需要重新登录
3. **知识产权保护**：

   - 开发者始终尊重并致力于保护第三方合法权益
   - 若权利人认为存在侵权内容，请通过软件内联系方式提交完整通知：
     - 权利归属证明文件扫描件
     - 涉嫌侵权内容在本软件中的具体定位信息
     - 具有法律效力的权利主张声明书
   - 开发者在收到符合要求的通知后，将启动内部合规审查流程
   - 经复核确认存在争议的内容，开发者将依据技术可行性采取适当措施

## 六、版本更新与维护

1. **版本更新**：

   - 当前版本：2.0.0（发布于2025年06月18日）
   - 重大更新：全平台架构重构、Supabase集成升级、全新UI设计、性能优化
   - 版本控制遵循语义化版本规范：主版本号.次版本号.修订号
2. **更新策略**：

   - 开发者将不定期发布更新，修复已知问题并改进用户体验
   - 更新内容将在应用内公示
   - 用户可选择是否升级，但部分功能可能需要最新版本支持
3. **维护范围**：

   - 仅针对最新版本提供基本维护
   - 不承诺解决所有已知问题或满足所有功能需求
   - 开发者保留随时终止项目维护的权利

## 七、其他条款

1. **协议变更**：

   - 更新内容将公示于本条款，不另行通知
   - 继续使用视为接受新条款
2. **极端情况**：

   - 开发者保留随时终止项目运营且不提前通知的权利
   - 如遇不可抗力导致项目终止，用户应自行备份配置数据
3. **联系方式**：

   - 唯一联系方式见软件内（仅处理侵权投诉，不提供技术支持）
   - 反馈问题请通过GitHub Issues提交

**声明性质**：本声明不具备法律合同效力，仅为风险提示。开发者已通过本协议充分履行告知义务，用户滚动查看即视为已阅读，继续使用将默认您接受全部条款。
