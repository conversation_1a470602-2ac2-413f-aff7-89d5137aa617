/**
 * 第三方库模块: Thunder Download Engine (迅雷下载引擎)
 * 原始模块名: thunder
 * 库包名: com.xunlei.downloadlib.android
 * 功能说明: 迅雷Android平台工具类，提供平台相关的工具方法
 *
 * 注意: 文件夹名为 xunlei 是为了符合 Java 包名规范
 * 实际对应的是 thunder 第三方库模块
 */
package com.xunlei.downloadlib.android;

import android.util.Base64;

import java.security.SecureRandom;
import java.util.UUID;

public class XLUtil {

    public static String getMAC() {
        return random("ABCDEF0123456", 12).toUpperCase();
    }

    public static String getIMEI() {
        return random("0123456", 15);
    }

    public static String getPeerId() {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        uuid = uuid.substring(0, 12).toUpperCase() + "004V";
        return uuid;
    }

    private static String random(String base, int length) {
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++)
            sb.append(base.charAt(random.nextInt(base.length())));
        return sb.toString();
    }

    public static String getGuid() {
        return getIMEI() + "_" + getMAC();
    }

    public static String generateAppKey(String str, short s, byte b) {
        int length = str.length();
        int i = length + 1;
        byte[] bArr = new byte[(i + 2 + 1)];
        byte[] bytes = str.getBytes();
        System.arraycopy(bytes, 0, bArr, 0, bytes.length);
        bArr[length] = 0;
        bArr[i] = (byte) (s & 255);
        bArr[length + 2] = (byte) ((s >> 8) & 255);
        bArr[length + 3] = b;
        return new String(Base64.encode(bArr, 0)).trim();
    }
}
