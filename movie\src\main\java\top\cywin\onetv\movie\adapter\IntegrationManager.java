package top.cywin.onetv.movie.adapter;

import android.content.Context;
import android.util.Log;

/**
 * 集成管理器 - 纯粹的配置和初始化管理器
 * 只负责FongMi_TV系统的初始化和配置管理
 */
public class IntegrationManager {

    private static final String TAG = "IntegrationManager";
    private static IntegrationManager instance;

    private Context context;
    private boolean isInitialized = false;

    public static IntegrationManager getInstance() {
        if (instance == null) {
            synchronized (IntegrationManager.class) {
                if (instance == null) {
                    instance = new IntegrationManager();
                }
            }
        }
        return instance;
    }

    private IntegrationManager() {
        Log.d(TAG, "🏗️ IntegrationManager 创建");
    }

    /**
     * 初始化FongMi_TV系统 - 只做系统初始化
     */
    public void initialize(Context context) {
        if (isInitialized) {
            Log.d(TAG, "⚠️ IntegrationManager 已初始化，跳过");
            return;
        }

        this.context = context.getApplicationContext();
        Log.d(TAG, "🔄 初始化FongMi_TV系统");

        try {
            // ✅ 1. 初始化FongMi_TV核心组件
            initializeFongMiTVCore();

            // ✅ 2. 初始化数据库
            initializeDatabase();

            // ✅ 3. 初始化配置系统
            initializeConfigSystem();

            // ✅ 4. 初始化EventBus
            initializeEventBus();

            // ✅ 5. 初始化Spider引擎管理器
            initializeSpiderEngines();

            isInitialized = true;
            Log.d(TAG, "✅ FongMi_TV系统初始化完成");

        } catch (Exception e) {
            Log.e(TAG, "💥 FongMi_TV系统初始化失败", e);
            throw new RuntimeException("FongMi_TV系统初始化失败", e);
        }
    }

    /**
     * 初始化FongMi_TV核心组件
     */
    private void initializeFongMiTVCore() {
        Log.d(TAG, "🔄 初始化FongMi_TV核心组件");

        try {
            // ✅ 初始化FongMi_TV的App实例（基于Movie模块中的App.java）
            if (context != null) {
                Log.d(TAG, "🔧 初始化FongMi_TV App实例");

                // ✅ 首先设置Init上下文（这是FongMi_TV的关键初始化步骤）
                Log.d(TAG, "🔧 设置Init上下文");
                com.github.catvod.Init.set(context);
                Log.d(TAG, "✅ Init上下文设置完成");

                // 检查App实例是否已存在
                top.cywin.onetv.movie.App fongMiApp = top.cywin.onetv.movie.App.get();
                if (fongMiApp == null) {
                    Log.d(TAG, "📦 创建FongMi_TV App实例");
                    // ✅ 修复：不直接调用protected方法，而是通过反射或其他方式
                    // 由于attachBaseContext是protected方法，我们跳过这个调用
                    // FongMi_TV的App构造函数已经设置了instance，直接调用onCreate即可
                    fongMiApp = new top.cywin.onetv.movie.App();
                    fongMiApp.onCreate();
                    Log.d(TAG, "✅ FongMi_TV App实例创建完成");
                } else {
                    Log.d(TAG, "✅ FongMi_TV App实例已存在");
                    // 即使App实例存在，也要确保Init上下文正确设置
                    Log.d(TAG, "🔧 验证Init上下文");
                    android.content.Context initContext = com.github.catvod.Init.context();
                    if (initContext == null) {
                        Log.d(TAG, "🔧 重新设置Init上下文");
                        com.github.catvod.Init.set(context);
                    }
                }
            }

            // ✅ 初始化VodConfig
            Log.d(TAG, "🔧 初始化VodConfig");
            top.cywin.onetv.movie.api.config.VodConfig.get().init();
            Log.d(TAG, "✅ VodConfig初始化完成");

            // ✅ 初始化网络配置
            Log.d(TAG, "🔧 初始化网络配置");
            initializeNetworkConfig();
            Log.d(TAG, "✅ 网络配置初始化完成");

            Log.d(TAG, "✅ FongMi_TV核心组件初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "❌ FongMi_TV核心组件初始化失败", e);
            throw e;
        }
    }

    /**
     * 初始化数据库
     */
    private void initializeDatabase() {
        Log.d(TAG, "🔄 初始化数据库");

        try {
            // ✅ 初始化FongMi_TV数据库（基于Movie模块中的AppDatabase.java）
            Log.d(TAG, "🔧 获取AppDatabase实例");
            top.cywin.onetv.movie.database.AppDatabase database = top.cywin.onetv.movie.database.AppDatabase.get();

            if (database != null) {
                Log.d(TAG, "✅ AppDatabase实例获取成功");

                // 验证数据库连接
                Log.d(TAG, "🔧 验证数据库连接");
                database.getConfigDao(); // 测试DAO访问
                Log.d(TAG, "✅ 数据库连接验证成功");

                // 检查数据库版本
                Log.d(TAG, "📊 数据库版本: " + top.cywin.onetv.movie.database.AppDatabase.VERSION);
                Log.d(TAG, "📊 数据库名称: " + top.cywin.onetv.movie.database.AppDatabase.NAME);
            } else {
                Log.e(TAG, "❌ AppDatabase实例获取失败");
                throw new RuntimeException("AppDatabase实例获取失败");
            }

            Log.d(TAG, "✅ 数据库初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "❌ 数据库初始化失败", e);
            throw e;
        }
    }

    /**
     * 初始化配置系统
     */
    private void initializeConfigSystem() {
        Log.d(TAG, "🔄 初始化配置系统");

        try {
            // ✅ 获取VodConfig实例
            Log.d(TAG, "🔧 获取VodConfig实例");
            top.cywin.onetv.movie.api.config.VodConfig vodConfig = top.cywin.onetv.movie.api.config.VodConfig.get();

            if (vodConfig != null) {
                Log.d(TAG, "✅ VodConfig实例获取成功");

                // ✅ 检查配置状态
                Log.d(TAG, "📊 当前站点数量: " + vodConfig.getSites().size());
                Log.d(TAG, "📊 当前解析器数量: " + vodConfig.getParses().size());

                // ✅ 初始化默认配置（如果需要）
                if (vodConfig.getSites().isEmpty()) {
                    Log.d(TAG, "📥 站点列表为空，准备加载默认配置");

                    // 检查是否有保存的配置
                    top.cywin.onetv.movie.bean.Config savedConfig = top.cywin.onetv.movie.bean.Config.vod();
                    if (savedConfig != null && !savedConfig.getUrl().isEmpty()) {
                        Log.d(TAG, "🔧 发现保存的配置: " + savedConfig.getUrl());
                        // 这里可以触发配置加载，但不在初始化阶段强制加载
                        // 让RepositoryAdapter在需要时加载
                    } else {
                        Log.d(TAG, "📝 未发现保存的配置，等待用户配置");
                    }
                } else {
                    Log.d(TAG, "✅ 已有配置数据，跳过默认配置加载");
                }

                // ✅ 验证配置系统功能
                Log.d(TAG, "🔧 验证配置系统功能");
                boolean hasWall = vodConfig.getWall() != null && !vodConfig.getWall().isEmpty();
                boolean hasHome = vodConfig.getHome() != null && !vodConfig.getHome().isEmpty();
                Log.d(TAG, "📊 Wall配置: " + (hasWall ? "存在" : "无"));
                Log.d(TAG, "📊 Home配置: " + (hasHome ? "存在" : "无"));

            } else {
                Log.e(TAG, "❌ VodConfig实例获取失败");
                throw new RuntimeException("VodConfig实例获取失败");
            }

            Log.d(TAG, "✅ 配置系统初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "❌ 配置系统初始化失败", e);
            throw e;
        }
    }

    /**
     * 初始化EventBus
     */
    private void initializeEventBus() {
        Log.d(TAG, "🔄 初始化EventBus");

        try {
            // ✅ 检查EventBus是否已经初始化（基于FongMi_TV的EventBus配置）
            Log.d(TAG, "🔧 检查EventBus状态");

            // EventBus在FongMi_TV的App.onCreate()中已经初始化
            // EventBus.builder().addIndex(new EventIndex()).installDefaultEventBus();
            // 这里只需要验证EventBus是否可用

            org.greenrobot.eventbus.EventBus eventBus = org.greenrobot.eventbus.EventBus.getDefault();
            if (eventBus != null) {
                Log.d(TAG, "✅ EventBus实例获取成功");

                // 验证EventBus功能
                Log.d(TAG, "🔧 验证EventBus功能");
                boolean hasSubscribers = eventBus.hasSubscriberForEvent(Object.class);
                Log.d(TAG, "📊 EventBus订阅者状态: " + (hasSubscribers ? "有订阅者" : "无订阅者"));

                // 检查是否有索引
                Log.d(TAG, "📊 EventBus配置验证完成");
            } else {
                Log.e(TAG, "❌ EventBus实例获取失败");
                throw new RuntimeException("EventBus实例获取失败");
            }

            // ✅ 注册IntegrationManager为EventBus订阅者
            if (!eventBus.isRegistered(this)) {
                eventBus.register(this);
                Log.d(TAG, "✅ IntegrationManager已注册为EventBus订阅者");
            }

            Log.d(TAG, "✅ EventBus初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "❌ EventBus初始化失败", e);
            throw e;
        }
    }

    /**
     * 🔥 初始化Spider引擎管理器
     */
    private void initializeSpiderEngines() {
        Log.d(TAG, "🔄 初始化Spider引擎管理器");
        try {
            // 1. 初始化SpiderEngineManager
            top.cywin.onetv.movie.spider.engine.SpiderEngineManager.getInstance().init(context);
            Log.d(TAG, "✅ SpiderEngineManager初始化完成");

            // 2. 初始化AppSpiderManager
            top.cywin.onetv.movie.spider.AppSpiderManager.getInstance().init(context);
            Log.d(TAG, "✅ AppSpiderManager初始化完成");

            // 3. 初始化SpiderManager
            top.cywin.onetv.movie.spider.SpiderManager spiderManager = top.cywin.onetv.movie.spider.SpiderManager
                    .getInstance();
            // SpiderManager是单例，获取实例即完成初始化
            Log.d(TAG, "✅ SpiderManager初始化完成: " + spiderManager.getClass().getSimpleName());

            Log.d(TAG, "✅ 所有Spider引擎管理器初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "💥 Spider引擎管理器初始化失败", e);
            // 不抛出异常，允许系统继续运行
            Log.w(TAG, "⚠️ Spider引擎管理器初始化失败，但系统将继续运行");
        }
    }

    /**
     * 初始化网络配置 - 基于FongMi_TV的网络配置
     */
    private void initializeNetworkConfig() {
        Log.d(TAG, "🔄 初始化网络配置");

        try {
            // ✅ 获取网络配置（基于FongMi_TV的Setting类）
            Log.d(TAG, "🔧 获取代理配置");
            String proxy = top.cywin.onetv.movie.Setting.getProxy();
            Log.d(TAG, "📊 代理配置: " + (proxy.isEmpty() ? "无" : proxy));

            Log.d(TAG, "🔧 获取DNS配置");
            String doh = top.cywin.onetv.movie.Setting.getDoh();
            Log.d(TAG, "📊 DNS配置: " + (doh.isEmpty() ? "系统默认" : doh));

            // ✅ 应用网络配置到OkHttp
            Log.d(TAG, "🔧 应用网络配置到OkHttp");
            com.github.catvod.net.OkHttp okHttp = com.github.catvod.net.OkHttp.get();
            if (okHttp != null) {
                okHttp.setProxy(proxy);
                // ✅ 修复：使用正确的Doh类包名
                okHttp.setDoh(com.github.catvod.bean.Doh.objectFrom(doh));
                Log.d(TAG, "✅ 网络配置应用成功");
            } else {
                Log.e(TAG, "❌ OkHttp实例获取失败");
            }

            Log.d(TAG, "✅ 网络配置初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "❌ 网络配置初始化失败", e);
            // 网络配置失败不应该阻止整个初始化过程
        }
    }

    /**
     * 获取初始化状态
     */
    public boolean isInitialized() {
        return isInitialized;
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        Log.d(TAG, "🧹 清理IntegrationManager资源");

        try {
            // ✅ 清理FongMi_TV资源
            // 根据实际需要进行清理

            // ✅ 取消EventBus注册
            org.greenrobot.eventbus.EventBus eventBus = org.greenrobot.eventbus.EventBus.getDefault();
            if (eventBus.isRegistered(this)) {
                eventBus.unregister(this);
                Log.d(TAG, "✅ IntegrationManager已取消EventBus注册");
            }

            isInitialized = false;
            Log.d(TAG, "✅ IntegrationManager资源清理完成");
        } catch (Exception e) {
            Log.e(TAG, "❌ IntegrationManager资源清理失败", e);
        }
    }

    /**
     * 监听配置更新事件
     */
    @org.greenrobot.eventbus.Subscribe(threadMode = org.greenrobot.eventbus.ThreadMode.MAIN)
    public void onConfigUpdate(top.cywin.onetv.movie.event.ConfigUpdateEvent event) {
        Log.d(TAG, "📡 收到配置更新事件: success=" + event.isSuccess());

        if (event.isSuccess() && event.getConfig() != null) {
            Log.d(TAG, "✅ 配置更新成功，重新检查配置状态");
            Log.d(TAG, "📊 更新后站点数量: " + event.getConfig().getSites().size());
            Log.d(TAG, "📊 更新后解析器数量: " + event.getConfig().getParses().size());
        } else {
            Log.w(TAG, "⚠️ 配置更新失败或配置为空");
        }
    }
}
