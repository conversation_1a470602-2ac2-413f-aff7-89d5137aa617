package top.cywin.onetv.movie.network.config;

import java.util.HashMap;
import java.util.Map;

/**
 * Hosts配置类
 * 基于FongMi_TV架构设计，支持域名重定向
 */
public class HostsConfig {
    private boolean enabled;
    private Map<String, String> hostsMap;

    public HostsConfig() {
        this.enabled = false;
        this.hostsMap = new HashMap<>();
    }

    public HostsConfig(boolean enabled) {
        this();
        this.enabled = enabled;
    }

    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Map<String, String> getHostsMap() {
        return hostsMap;
    }

    public void setHostsMap(Map<String, String> hostsMap) {
        this.hostsMap = hostsMap != null ? hostsMap : new HashMap<>();
    }

    /**
     * 添加Hosts映射
     * @param originalHost 原始域名
     * @param redirectHost 重定向域名
     */
    public void addHost(String originalHost, String redirectHost) {
        if (originalHost != null && redirectHost != null) {
            hostsMap.put(originalHost.trim(), redirectHost.trim());
        }
    }

    /**
     * 移除Hosts映射
     * @param originalHost 原始域名
     */
    public void removeHost(String originalHost) {
        if (originalHost != null) {
            hostsMap.remove(originalHost.trim());
        }
    }

    /**
     * 获取重定向域名
     * @param originalHost 原始域名
     * @return 重定向域名，如果没有映射则返回null
     */
    public String getRedirectHost(String originalHost) {
        if (!enabled || originalHost == null) {
            return null;
        }
        return hostsMap.get(originalHost.trim());
    }

    /**
     * 检查是否有指定域名的映射
     * @param originalHost 原始域名
     * @return 是否存在映射
     */
    public boolean hasMapping(String originalHost) {
        return enabled && originalHost != null && hostsMap.containsKey(originalHost.trim());
    }

    /**
     * 清空所有Hosts映射
     */
    public void clearHosts() {
        hostsMap.clear();
    }

    /**
     * 获取映射数量
     */
    public int getHostsCount() {
        return hostsMap.size();
    }

    /**
     * 检查配置是否有效
     */
    public boolean isValid() {
        return enabled && !hostsMap.isEmpty();
    }

    @Override
    public String toString() {
        return "HostsConfig{" +
                "enabled=" + enabled +
                ", hostsCount=" + hostsMap.size() +
                '}';
    }

    /**
     * 从Hosts文件内容解析配置
     * 格式：每行一个映射，格式为 "IP/域名 域名"
     */
    public static HostsConfig fromHostsContent(String hostsContent) {
        HostsConfig config = new HostsConfig();
        
        if (hostsContent == null || hostsContent.trim().isEmpty()) {
            return config;
        }

        try {
            String[] lines = hostsContent.split("\n");
            for (String line : lines) {
                line = line.trim();
                
                // 跳过注释和空行
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }

                String[] parts = line.split("\\s+");
                if (parts.length >= 2) {
                    String redirectHost = parts[0];
                    String originalHost = parts[1];
                    config.addHost(originalHost, redirectHost);
                }
            }

            if (config.getHostsCount() > 0) {
                config.setEnabled(true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return config;
    }

    /**
     * 转换为Hosts文件格式
     */
    public String toHostsContent() {
        if (!isValid()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("# Generated by OneTV Movie\n");
        
        for (Map.Entry<String, String> entry : hostsMap.entrySet()) {
            sb.append(entry.getValue()).append(" ").append(entry.getKey()).append("\n");
        }

        return sb.toString();
    }
}
