<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.6.0" type="baseline" client="gradle" dependencies="false" name="AGP (8.6.0)" variant="all" version="8.6.0">

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            spiderName.toLowerCase(),"
        errorLine2="                       ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/spider/AppSpiderManager.java"
            line="105"
            column="24"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        gb >= 1 -> String.format(&quot;%.1f GB&quot;, gb)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/screens/CloudDriveScreen.kt"
            line="381"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        mb >= 1 -> String.format(&quot;%.1f MB&quot;, mb)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/screens/CloudDriveScreen.kt"
            line="382"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        kb >= 1 -> String.format(&quot;%.1f KB&quot;, kb)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/screens/CloudDriveScreen.kt"
            line="383"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            String lowerName = name.toLowerCase();"
        errorLine2="                                    ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/cloudrive/bean/CloudFile.java"
            line="118"
            column="37"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        String decode = new String(Util.hex2byte(data)).toLowerCase();"
        errorLine2="                                                        ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/api/Decoder.java"
            line="60"
            column="57"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        return getName().toLowerCase().contains(name.toLowerCase());"
        errorLine2="                         ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/bean/Episode.java"
            line="105"
            column="26"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        return getName().toLowerCase().contains(name.toLowerCase());"
        errorLine2="                                                     ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/bean/Episode.java"
            line="105"
            column="54"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        return name.toLowerCase().contains(getName().toLowerCase());"
        errorLine2="                    ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/bean/Episode.java"
            line="109"
            column="21"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        return name.toLowerCase().contains(getName().toLowerCase());"
        errorLine2="                                                     ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/bean/Episode.java"
            line="109"
            column="54"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            return String.format(&quot;PoolStatus{total=%d, active=%d, available=%d, max=%d, core=%d}&quot;,"
        errorLine2="                   ^">
        <location
            file="src/main/java/top/cywin/onetv/movie/spider/engine/JsEnginePool.java"
            line="250"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            if (fn.toLowerCase().endsWith(&quot;.zip&quot;)) FileUtil.zipDecompress(temp, Path.root(path));"
        errorLine2="                   ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/server/process/Local.java"
            line="60"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            return o1.getName().toLowerCase().compareTo(o2.getName().toLowerCase());"
        errorLine2="                                ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/catvod/utils/Path.java"
            line="207"
            column="33"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            return o1.getName().toLowerCase().compareTo(o2.getName().toLowerCase());"
        errorLine2="                                                                     ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/catvod/utils/Path.java"
            line="207"
            column="70"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        String.format(&quot;%d:%02d:%02d&quot;, hours, minutes, seconds)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/components/PlayerControls.kt"
            line="310"
            column="9"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        String.format(&quot;%02d:%02d&quot;, minutes, seconds)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/components/PlayerControls.kt"
            line="312"
            column="9"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            return String.format(&quot;SpiderStats{total=%d, jar=%d, js=%d, py=%d, xpath=%d, json=%d}&quot;,"
        errorLine2="                   ^">
        <location
            file="src/main/java/top/cywin/onetv/movie/spider/engine/SpiderEngineManager.java"
            line="348"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        String path = jarPaths.get(type.toLowerCase());"
        errorLine2="                                        ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/spider/SpiderJarManager.java"
            line="65"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            jarPaths.put(type.toLowerCase(), path);"
        errorLine2="                              ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/spider/SpiderJarManager.java"
            line="179"
            column="31"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            jarPaths.remove(type.toLowerCase());"
        errorLine2="                                 ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/spider/SpiderJarManager.java"
            line="188"
            column="34"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        return getFileName().contains(&quot;.&quot;) ? getFileName().substring(getFileName().lastIndexOf(&quot;.&quot;) + 1).toLowerCase() : &quot;&quot;;"
        errorLine2="                                                                                                         ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/thunder/downloadlib/parameter/TorrentFileInfo.java"
            line="52"
            column="106"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        return scheme == null ? &quot;&quot; : scheme.toLowerCase().trim();"
        errorLine2="                                            ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/utils/UrlUtil.java"
            line="21"
            column="45"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        return host == null ? &quot;&quot; : host.toLowerCase().trim();"
        errorLine2="                                        ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/utils/UrlUtil.java"
            line="30"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        return scheme.toLowerCase();"
        errorLine2="                      ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/forcetech/Util.java"
            line="21"
            column="23"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        return name.toLowerCase();"
        errorLine2="                    ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/forcetech/Util.java"
            line="29"
            column="21"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            return sb.toString().toLowerCase();"
        errorLine2="                                 ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/catvod/utils/Util.java"
            line="83"
            column="34"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        return random(&quot;ABCDEF0123456&quot;, 12).toUpperCase();"
        errorLine2="                                           ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/thunder/downloadlib/android/XLUtil.java"
            line="11"
            column="44"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        uuid = uuid.substring(0, 12).toUpperCase() + &quot;004V&quot;;"
        errorLine2="                                     ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/thunder/downloadlib/android/XLUtil.java"
            line="20"
            column="38"/>
    </issue>

    <issue
        id="InlinedApi"
        message="Field requires API level 29 (current min is 26): `android.provider.MediaStore#VOLUME_EXTERNAL`"
        errorLine1="                return getDataColumn(context, ContentUris.withAppendedId(MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL), Long.parseLong(split[1])));"
        errorLine2="                                                                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/utils/FileChooser.java"
            line="144"
            column="105"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.8.7"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="220"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.8.7"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-livedata-ktx:2.7.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="221"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0"
        errorLine1="    implementation(&quot;com.google.code.gson:gson:2.10.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="247"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of org.jsoup:jsoup than 1.17.2 is available: 1.19.1"
        errorLine1="    implementation(&quot;org.jsoup:jsoup:1.17.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="248"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of wang.harlon.quickjs:wrapper-java than 3.2.0 is available: 3.2.3"
        errorLine1="    implementation(&quot;wang.harlon.quickjs:wrapper-java:3.2.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="295"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of wang.harlon.quickjs:wrapper-android than 3.2.0 is available: 3.2.3"
        errorLine1="    implementation(&quot;wang.harlon.quickjs:wrapper-android:3.2.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="296"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of org.fourthline.cling:cling-core than 2.1.1 is available: 2.1.2"
        errorLine1="    implementation(&quot;org.fourthline.cling:cling-core:2.1.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="364"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of org.fourthline.cling:cling-support than 2.1.1 is available: 2.1.2"
        errorLine1="    implementation(&quot;org.fourthline.cling:cling-support:2.1.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="365"
            column="20"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    private SimpleCache cache;"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/CacheManager.java"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    public Cache getCache() {"
        errorLine2="           ~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/CacheManager.java"
            line="23"
            column="12"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        cache = new SimpleCache(Path.exo(), new NoOpCacheEvictor(), new StandaloneDatabaseProvider(App.get()));"
        errorLine2="        ~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/CacheManager.java"
            line="29"
            column="9"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        cache = new SimpleCache(Path.exo(), new NoOpCacheEvictor(), new StandaloneDatabaseProvider(App.get()));"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/CacheManager.java"
            line="29"
            column="17"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        cache = new SimpleCache(Path.exo(), new NoOpCacheEvictor(), new StandaloneDatabaseProvider(App.get()));"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/CacheManager.java"
            line="29"
            column="45"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        cache = new SimpleCache(Path.exo(), new NoOpCacheEvictor(), new StandaloneDatabaseProvider(App.get()));"
        errorLine2="                                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/CacheManager.java"
            line="29"
            column="69"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return Util.getUserAgent(App.get(), BuildConfig.APPLICATION_ID);"
        errorLine2="                    ~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="42"
            column="21"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    public static LoadControl buildLoadControl() {"
        errorLine2="                  ~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="45"
            column="19"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new DefaultLoadControl.Builder().setBufferDurationsMs(DefaultLoadControl.DEFAULT_MIN_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_MAX_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_MS, DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS).build();"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="46"
            column="16"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new DefaultLoadControl.Builder().setBufferDurationsMs(DefaultLoadControl.DEFAULT_MIN_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_MAX_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_MS, DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS).build();"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="46"
            column="49"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new DefaultLoadControl.Builder().setBufferDurationsMs(DefaultLoadControl.DEFAULT_MIN_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_MAX_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_MS, DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS).build();"
        errorLine2="                                                                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="46"
            column="89"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new DefaultLoadControl.Builder().setBufferDurationsMs(DefaultLoadControl.DEFAULT_MIN_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_MAX_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_MS, DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS).build();"
        errorLine2="                                                                                                                                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="46"
            column="153"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new DefaultLoadControl.Builder().setBufferDurationsMs(DefaultLoadControl.DEFAULT_MIN_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_MAX_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_MS, DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS).build();"
        errorLine2="                                                                                                                                                                                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="46"
            column="217"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new DefaultLoadControl.Builder().setBufferDurationsMs(DefaultLoadControl.DEFAULT_MIN_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_MAX_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_MS, DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS).build();"
        errorLine2="                                                                                                                                                                                                                                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="46"
            column="268"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new DefaultLoadControl.Builder().setBufferDurationsMs(DefaultLoadControl.DEFAULT_MIN_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_MAX_BUFFER_MS * Setting.getBuffer(), DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_MS, DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS).build();"
        errorLine2="                                                                                                                                                                                                                                                                                                                          ~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="46"
            column="315"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    public static TrackSelector buildTrackSelector() {"
        errorLine2="                  ~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="49"
            column="19"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        DefaultTrackSelector trackSelector = new DefaultTrackSelector(App.get());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="50"
            column="9"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        DefaultTrackSelector trackSelector = new DefaultTrackSelector(App.get());"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="50"
            column="46"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        DefaultTrackSelector.Parameters.Builder builder = trackSelector.buildUponParameters();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="51"
            column="9"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        DefaultTrackSelector.Parameters.Builder builder = trackSelector.buildUponParameters();"
        errorLine2="                                                                        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="51"
            column="73"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        if (Setting.isPreferAAC()) builder.setPreferredAudioMimeType(MimeTypes.AUDIO_AAC);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="52"
            column="44"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        builder.setPreferredTextLanguage(Locale.getDefault().getISO3Language());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="53"
            column="17"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        builder.setTunnelingEnabled(Setting.isTunnel());"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="54"
            column="17"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        builder.setForceHighestSupportedBitrate(true);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="55"
            column="17"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        trackSelector.setParameters(builder.build());"
        errorLine2="                      ~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="56"
            column="23"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        trackSelector.setParameters(builder.build());"
        errorLine2="                                            ~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="56"
            column="45"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    public static RenderersFactory buildRenderersFactory(int renderMode) {"
        errorLine2="                  ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="60"
            column="19"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new NextRenderersFactory(App.get()).setEnableDecoderFallback(true).setExtensionRendererMode(renderMode);"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="61"
            column="16"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new NextRenderersFactory(App.get()).setEnableDecoderFallback(true).setExtensionRendererMode(renderMode);"
        errorLine2="                                                   ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="61"
            column="52"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new NextRenderersFactory(App.get()).setEnableDecoderFallback(true).setExtensionRendererMode(renderMode);"
        errorLine2="                                                                                  ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="61"
            column="83"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    public static CaptionStyleCompat getCaptionStyle() {"
        errorLine2="                  ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="68"
            column="19"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return Setting.isCaption() ? CaptionStyleCompat.createFromCaptionStyle(((CaptioningManager) App.get().getSystemService(Context.CAPTIONING_SERVICE)).getUserStyle()) : new CaptionStyleCompat(Color.WHITE, Color.TRANSPARENT, Color.TRANSPARENT, CaptionStyleCompat.EDGE_TYPE_OUTLINE, Color.BLACK, null);"
        errorLine2="                                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="69"
            column="57"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return Setting.isCaption() ? CaptionStyleCompat.createFromCaptionStyle(((CaptioningManager) App.get().getSystemService(Context.CAPTIONING_SERVICE)).getUserStyle()) : new CaptionStyleCompat(Color.WHITE, Color.TRANSPARENT, Color.TRANSPARENT, CaptionStyleCompat.EDGE_TYPE_OUTLINE, Color.BLACK, null);"
        errorLine2="                                                                                                                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="69"
            column="175"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return Setting.isCaption() ? CaptionStyleCompat.createFromCaptionStyle(((CaptioningManager) App.get().getSystemService(Context.CAPTIONING_SERVICE)).getUserStyle()) : new CaptionStyleCompat(Color.WHITE, Color.TRANSPARENT, Color.TRANSPARENT, CaptionStyleCompat.EDGE_TYPE_OUTLINE, Color.BLACK, null);"
        errorLine2="                                                                                                                                                                                                                                                                           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="69"
            column="268"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        exo.getSubtitleView().setStyle(getCaptionStyle());"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="95"
            column="13"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        exo.getSubtitleView().setStyle(getCaptionStyle());"
        errorLine2="                              ~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="95"
            column="31"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        exo.getSubtitleView().setApplyEmbeddedFontSizes(false);"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="96"
            column="13"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        exo.getSubtitleView().setApplyEmbeddedFontSizes(false);"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="96"
            column="31"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        exo.getSubtitleView().setApplyEmbeddedStyles(!Setting.isCaption());"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="97"
            column="13"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        exo.getSubtitleView().setApplyEmbeddedStyles(!Setting.isCaption());"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="97"
            column="31"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        if (Setting.getSubtitleTextSize() != 0) exo.getSubtitleView().setFractionalTextSize(Setting.getSubtitleTextSize());"
        errorLine2="                                                    ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="98"
            column="53"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        if (Setting.getSubtitleTextSize() != 0) exo.getSubtitleView().setFractionalTextSize(Setting.getSubtitleTextSize());"
        errorLine2="                                                                      ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="98"
            column="71"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        builder.setImageDurationMs(15000);"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/ExoUtil.java"
            line="121"
            column="17"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    private ExtractorsFactory extractorsFactory;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        defaultMediaSourceFactory = new DefaultMediaSourceFactory(getDataSourceFactory(), getExtractorsFactory());"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="39"
            column="9"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        defaultMediaSourceFactory = new DefaultMediaSourceFactory(getDataSourceFactory(), getExtractorsFactory());"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="39"
            column="37"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    public MediaSource.Factory setDrmSessionManagerProvider(@NonNull DrmSessionManagerProvider drmSessionManagerProvider) {"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="44"
            column="32"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    public MediaSource.Factory setDrmSessionManagerProvider(@NonNull DrmSessionManagerProvider drmSessionManagerProvider) {"
        errorLine2="                                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="44"
            column="70"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return defaultMediaSourceFactory.setDrmSessionManagerProvider(drmSessionManagerProvider);"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="45"
            column="42"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    public MediaSource.Factory setLoadErrorHandlingPolicy(@NonNull LoadErrorHandlingPolicy loadErrorHandlingPolicy) {"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="50"
            column="32"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    public MediaSource.Factory setLoadErrorHandlingPolicy(@NonNull LoadErrorHandlingPolicy loadErrorHandlingPolicy) {"
        errorLine2="                                                                   ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="50"
            column="68"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return defaultMediaSourceFactory.setLoadErrorHandlingPolicy(loadErrorHandlingPolicy);"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="51"
            column="42"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    public @C.ContentType int[] getSupportedTypes() {"
        errorLine2="                                ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="56"
            column="33"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return defaultMediaSourceFactory.getSupportedTypes();"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="57"
            column="42"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    public MediaSource createMediaSource(@NonNull MediaItem mediaItem) {"
        errorLine2="                       ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="62"
            column="24"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            return defaultMediaSourceFactory.createMediaSource(setHeader(mediaItem));"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="66"
            column="46"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        getHttpDataSourceFactory().setDefaultRequestProperties(headers);"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="73"
            column="36"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        ConcatenatingMediaSource2.Builder builder = new ConcatenatingMediaSource2.Builder();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="78"
            column="9"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        ConcatenatingMediaSource2.Builder builder = new ConcatenatingMediaSource2.Builder();"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="78"
            column="53"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            if (info.length >= 2) builder.add(defaultMediaSourceFactory.createMediaSource(mediaItem.buildUpon().setUri(Uri.parse(info[0])).build()), Long.parseLong(info[1]));"
        errorLine2="                                          ~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="81"
            column="43"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            if (info.length >= 2) builder.add(defaultMediaSourceFactory.createMediaSource(mediaItem.buildUpon().setUri(Uri.parse(info[0])).build()), Long.parseLong(info[1]));"
        errorLine2="                                                                        ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="81"
            column="73"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return builder.build();"
        errorLine2="                       ~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="83"
            column="24"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    private ExtractorsFactory getExtractorsFactory() {"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="86"
            column="13"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        if (extractorsFactory == null) extractorsFactory = new DefaultExtractorsFactory().setTsExtractorFlags(FLAG_ENABLE_HDMV_DTS_AUDIO_STREAMS).setTsExtractorTimestampSearchBytes(TsExtractor.DEFAULT_TIMESTAMP_SEARCH_BYTES * 10);"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="87"
            column="40"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        if (extractorsFactory == null) extractorsFactory = new DefaultExtractorsFactory().setTsExtractorFlags(FLAG_ENABLE_HDMV_DTS_AUDIO_STREAMS).setTsExtractorTimestampSearchBytes(TsExtractor.DEFAULT_TIMESTAMP_SEARCH_BYTES * 10);"
        errorLine2="                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="87"
            column="60"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        if (extractorsFactory == null) extractorsFactory = new DefaultExtractorsFactory().setTsExtractorFlags(FLAG_ENABLE_HDMV_DTS_AUDIO_STREAMS).setTsExtractorTimestampSearchBytes(TsExtractor.DEFAULT_TIMESTAMP_SEARCH_BYTES * 10);"
        errorLine2="                                                                                          ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="87"
            column="91"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        if (extractorsFactory == null) extractorsFactory = new DefaultExtractorsFactory().setTsExtractorFlags(FLAG_ENABLE_HDMV_DTS_AUDIO_STREAMS).setTsExtractorTimestampSearchBytes(TsExtractor.DEFAULT_TIMESTAMP_SEARCH_BYTES * 10);"
        errorLine2="                                                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="87"
            column="111"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        if (extractorsFactory == null) extractorsFactory = new DefaultExtractorsFactory().setTsExtractorFlags(FLAG_ENABLE_HDMV_DTS_AUDIO_STREAMS).setTsExtractorTimestampSearchBytes(TsExtractor.DEFAULT_TIMESTAMP_SEARCH_BYTES * 10);"
        errorLine2="                                                                                                                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="87"
            column="147"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        if (extractorsFactory == null) extractorsFactory = new DefaultExtractorsFactory().setTsExtractorFlags(FLAG_ENABLE_HDMV_DTS_AUDIO_STREAMS).setTsExtractorTimestampSearchBytes(TsExtractor.DEFAULT_TIMESTAMP_SEARCH_BYTES * 10);"
        errorLine2="                                                                                                                                                                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="87"
            column="194"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="    private CacheDataSource.Factory buildReadOnlyCacheDataSource(DataSource.Factory upstreamFactory) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="96"
            column="13"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new CacheDataSource.Factory().setCache(CacheManager.get().getCache()).setUpstreamDataSourceFactory(upstreamFactory).setCacheWriteDataSinkFactory(null).setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR);"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="97"
            column="16"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new CacheDataSource.Factory().setCache(CacheManager.get().getCache()).setUpstreamDataSourceFactory(upstreamFactory).setCacheWriteDataSinkFactory(null).setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR);"
        errorLine2="                                             ~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="97"
            column="46"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new CacheDataSource.Factory().setCache(CacheManager.get().getCache()).setUpstreamDataSourceFactory(upstreamFactory).setCacheWriteDataSinkFactory(null).setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR);"
        errorLine2="                                                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="97"
            column="86"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new CacheDataSource.Factory().setCache(CacheManager.get().getCache()).setUpstreamDataSourceFactory(upstreamFactory).setCacheWriteDataSinkFactory(null).setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR);"
        errorLine2="                                                                                                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="97"
            column="132"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new CacheDataSource.Factory().setCache(CacheManager.get().getCache()).setUpstreamDataSourceFactory(upstreamFactory).setCacheWriteDataSinkFactory(null).setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR);"
        errorLine2="                                                                                                                                                                      ~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="97"
            column="167"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        return new CacheDataSource.Factory().setCache(CacheManager.get().getCache()).setUpstreamDataSourceFactory(upstreamFactory).setCacheWriteDataSinkFactory(null).setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR);"
        errorLine2="                                                                                                                                                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/MediaSourceFactory.java"
            line="97"
            column="192"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        exoPlayer = new ExoPlayer.Builder(App.get()).setLoadControl(ExoUtil.buildLoadControl()).setTrackSelector(ExoUtil.buildTrackSelector()).setRenderersFactory(ExoUtil.buildRenderersFactory(isHard() ? EXTENSION_RENDERER_MODE_ON : EXTENSION_RENDERER_MODE_PREFER)).setMediaSourceFactory(ExoUtil.buildMediaSourceFactory()).build();"
        errorLine2="                                                     ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/Players.java"
            line="129"
            column="54"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        exoPlayer = new ExoPlayer.Builder(App.get()).setLoadControl(ExoUtil.buildLoadControl()).setTrackSelector(ExoUtil.buildTrackSelector()).setRenderersFactory(ExoUtil.buildRenderersFactory(isHard() ? EXTENSION_RENDERER_MODE_ON : EXTENSION_RENDERER_MODE_PREFER)).setMediaSourceFactory(ExoUtil.buildMediaSourceFactory()).build();"
        errorLine2="                                                                                                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/Players.java"
            line="129"
            column="97"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        exoPlayer = new ExoPlayer.Builder(App.get()).setLoadControl(ExoUtil.buildLoadControl()).setTrackSelector(ExoUtil.buildTrackSelector()).setRenderersFactory(ExoUtil.buildRenderersFactory(isHard() ? EXTENSION_RENDERER_MODE_ON : EXTENSION_RENDERER_MODE_PREFER)).setMediaSourceFactory(ExoUtil.buildMediaSourceFactory()).build();"
        errorLine2="                                                                                                                                               ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/Players.java"
            line="129"
            column="144"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        exoPlayer = new ExoPlayer.Builder(App.get()).setLoadControl(ExoUtil.buildLoadControl()).setTrackSelector(ExoUtil.buildTrackSelector()).setRenderersFactory(ExoUtil.buildRenderersFactory(isHard() ? EXTENSION_RENDERER_MODE_ON : EXTENSION_RENDERER_MODE_PREFER)).setMediaSourceFactory(ExoUtil.buildMediaSourceFactory()).build();"
        errorLine2="                                                                                                                                                                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/Players.java"
            line="129"
            column="205"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        exoPlayer = new ExoPlayer.Builder(App.get()).setLoadControl(ExoUtil.buildLoadControl()).setTrackSelector(ExoUtil.buildTrackSelector()).setRenderersFactory(ExoUtil.buildRenderersFactory(isHard() ? EXTENSION_RENDERER_MODE_ON : EXTENSION_RENDERER_MODE_PREFER)).setMediaSourceFactory(ExoUtil.buildMediaSourceFactory()).build();"
        errorLine2="                                                                                                                                                                                                                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/Players.java"
            line="129"
            column="234"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        int bitrate = format.bitrate;"
        errorLine2="                             ~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="45"
            column="30"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        Locale languageLocale = Util.SDK_INT >= 21 ? Locale.forLanguageTag(language) : new Locale(language);"
        errorLine2="                                     ~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="86"
            column="38"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        Locale displayLocale = Util.getDefaultDisplayLocale();"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="87"
            column="37"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        int trackType = MimeTypes.getTrackType(format.sampleMimeType);"
        errorLine2="                                  ~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="122"
            column="35"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        if (MimeTypes.getVideoMediaMimeType(format.codecs) != null) return C.TRACK_TYPE_VIDEO;"
        errorLine2="                      ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="124"
            column="23"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        if (MimeTypes.getAudioMediaMimeType(format.codecs) != null) return C.TRACK_TYPE_AUDIO;"
        errorLine2="                      ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="125"
            column="23"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        else if (mimeType.contains(MimeTypes.VIDEO_FLV)) return &quot;FLV&quot;;"
        errorLine2="                                             ~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="158"
            column="46"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        else if (mimeType.contains(MimeTypes.VIDEO_VP8)) return &quot;VP8&quot;;"
        errorLine2="                                             ~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="167"
            column="46"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        else if (mimeType.contains(MimeTypes.VIDEO_VP9)) return &quot;VP9&quot;;"
        errorLine2="                                             ~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="168"
            column="46"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="        else if (mimeType.contains(MimeTypes.APPLICATION_MEDIA3_CUES)) return &quot;CUES&quot;;"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="178"
            column="46"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            return androidx.media3.common.util.Util.getStringForTime(builder, formatter, timeMs);"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/utils/Util.java"
            line="159"
            column="53"/>
    </issue>

    <issue
        id="ModifierParameter"
        message="Modifier parameter should be the first optional parameter"
        errorLine1="    modifier: Modifier = Modifier,"
        errorLine2="    ~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/components/DynamicMovieLayout.kt"
            line="32"
            column="5"/>
    </issue>

    <issue
        id="ModifierParameter"
        message="Modifier parameter should be the first optional parameter"
        errorLine1="    modifier: Modifier = Modifier"
        errorLine2="    ~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/components/MovieCards.kt"
            line="31"
            column="5"/>
    </issue>

    <issue
        id="ModifierParameter"
        message="Modifier parameter should be the first optional parameter"
        errorLine1="    modifier: Modifier = Modifier"
        errorLine2="    ~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/components/MovieCards.kt"
            line="101"
            column="5"/>
    </issue>

    <issue
        id="ModifierParameter"
        message="Modifier parameter should be the first optional parameter"
        errorLine1="    modifier: Modifier = Modifier"
        errorLine2="    ~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/components/MovieCards.kt"
            line="151"
            column="5"/>
    </issue>

    <issue
        id="ModifierParameter"
        message="Modifier parameter should be the first optional parameter"
        errorLine1="    modifier: Modifier = Modifier"
        errorLine2="    ~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/components/MovieCards.kt"
            line="221"
            column="5"/>
    </issue>

    <issue
        id="ModifierParameter"
        message="Modifier parameter should be the first optional parameter"
        errorLine1="    modifier: Modifier = Modifier,"
        errorLine2="    ~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/components/SearchBar.kt"
            line="29"
            column="5"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_controls_pause_description` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="        if (nonNull() &amp;&amp; player.isPlaying()) return buildNotificationAction(R.drawable.vod_ic_live_block, androidx.media3.ui.R.string.exo_controls_pause_description, ActionEvent.PAUSE);"
        errorLine2="                                                                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/service/PlaybackService.java"
            line="70"
            column="107"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_controls_play_description` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="        return buildNotificationAction(R.drawable.vod_ic_live_boot, androidx.media3.ui.R.string.exo_controls_play_description, ActionEvent.PLAY);"
        errorLine2="                                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/service/PlaybackService.java"
            line="71"
            column="69"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_controls_previous_description` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="        builder.addAction(buildNotificationAction(R.drawable.vod_ic_live_block, androidx.media3.ui.R.string.exo_controls_previous_description, ActionEvent.PREV));"
        errorLine2="                                                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/service/PlaybackService.java"
            line="100"
            column="81"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_controls_next_description` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="        builder.addAction(buildNotificationAction(R.drawable.vod_ic_live_pass, androidx.media3.ui.R.string.exo_controls_next_description, ActionEvent.NEXT));"
        errorLine2="                                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/service/PlaybackService.java"
            line="102"
            column="80"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_track_unknown` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="        return TextUtils.isEmpty(trackName) ? resources.getString(R.string.exo_track_unknown) : joinWithSeparator(trackName, buildMimeString(trackType, format));"
        errorLine2="                                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="35"
            column="67"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_track_resolution` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="        return width == Format.NO_VALUE || height == Format.NO_VALUE ? &quot;&quot; : resources.getString(R.string.exo_track_resolution, width, height);"
        errorLine2="                                                                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="41"
            column="97"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_track_bitrate` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="        return bitrate == Format.NO_VALUE ? &quot;&quot; : resources.getString(R.string.exo_track_bitrate, bitrate / 1000000f);"
        errorLine2="                                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="46"
            column="70"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_track_mono` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="                return resources.getString(R.string.exo_track_mono);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="59"
            column="44"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_track_stereo` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="                return resources.getString(R.string.exo_track_stereo);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="61"
            column="44"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_track_surround_5_point_1` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="                return resources.getString(R.string.exo_track_surround_5_point_1);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="64"
            column="44"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_track_surround_7_point_1` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="                return resources.getString(R.string.exo_track_surround_7_point_1);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="66"
            column="44"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_track_surround` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="                return resources.getString(R.string.exo_track_surround);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="68"
            column="44"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_track_role_alternate` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="        if ((format.roleFlags &amp; C.ROLE_FLAG_ALTERNATE) != 0) roles = resources.getString(R.string.exo_track_role_alternate);"
        errorLine2="                                                                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="100"
            column="90"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_track_role_supplementary` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="        if ((format.roleFlags &amp; C.ROLE_FLAG_SUPPLEMENTARY) != 0) roles = joinWithSeparator(roles, resources.getString(R.string.exo_track_role_supplementary));"
        errorLine2="                                                                                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="101"
            column="119"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_track_role_commentary` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="        if ((format.roleFlags &amp; C.ROLE_FLAG_COMMENTARY) != 0) roles = joinWithSeparator(roles, resources.getString(R.string.exo_track_role_commentary));"
        errorLine2="                                                                                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="102"
            column="116"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_track_role_closed_captions` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="        if ((format.roleFlags &amp; (C.ROLE_FLAG_CAPTION | C.ROLE_FLAG_DESCRIBES_MUSIC_AND_SOUND)) != 0) roles = joinWithSeparator(roles, resources.getString(R.string.exo_track_role_closed_captions));"
        errorLine2="                                                                                                                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="103"
            column="155"/>
    </issue>

    <issue
        id="PrivateResource"
        message="The resource `@string/exo_item_list` is marked as private in androidx.media3:media3-ui:1.6.1"
        errorLine1="                    itemList = resources.getString(R.string.exo_item_list, itemList, item);"
        errorLine2="                                                   ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/player/exo/TrackNameProvider.java"
            line="114"
            column="52"/>
    </issue>

    <issue
        id="StateFlowValueCalledInComposition"
        message="StateFlow.value should not be called within composition"
        errorLine1="    val currentState = viewModelInstance.uiState.value // 直接获取最新状态"
        errorLine2="                                                 ~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/screens/MovieHomeScreen.kt"
            line="160"
            column="50"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`)."
        errorLine1="        return App.get().getResources().getIdentifier(resId, &quot;drawable&quot;, App.get().getPackageName());"
        errorLine2="                                        ~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/utils/ResUtil.java"
            line="88"
            column="41"/>
    </issue>

    <issue
        id="ChromeOsAbiSupport"
        message="Missing x86_64 ABI support for ChromeOS"
        errorLine1="            abiFilters += listOf(&quot;arm64-v8a&quot;)"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="56"
            column="27"/>
    </issue>

    <issue
        id="HardwareIds"
        message="Using `getString` to get device identifiers is not recommended"
        errorLine1="            String id = Settings.Secure.getString(App.get().getContentResolver(), Settings.Secure.ANDROID_ID);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/utils/Util.java"
            line="103"
            column="25"/>
    </issue>

    <issue
        id="SetJavaScriptEnabled"
        message="Using `setJavaScriptEnabled` can introduce XSS vulnerabilities into your application, review carefully"
        errorLine1="                        javaScriptEnabled = true"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/components/ParseWebView.kt"
            line="153"
            column="25"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        message="`checkClientTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers"
        errorLine1="    public void checkClientTrusted(X509Certificate[] chain, String authType) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/network/interceptor/TrustAllManager.java"
            line="15"
            column="17"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        message="`checkServerTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers"
        errorLine1="    public void checkServerTrusted(X509Certificate[] chain, String authType) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/network/interceptor/TrustAllManager.java"
            line="20"
            column="17"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        message="`checkClientTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-util/8.1.21.v20160908/fa2cd60223c8355059cd80328e27bfce3e268038/jetty-util-8.1.21.v20160908.jar"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        message="`checkServerTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-util/8.1.21.v20160908/fa2cd60223c8355059cd80328e27bfce3e268038/jetty-util-8.1.21.v20160908.jar"/>
    </issue>

    <issue
        id="CustomX509TrustManager"
        message="Implementing a custom `X509TrustManager` is error-prone and likely to be insecure. It is likely to disable certificate validation altogether, and is non-trivial to implement correctly without calling Android&apos;s default implementation."
        errorLine1="public class TrustAllManager implements X509TrustManager {"
        errorLine2="             ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/network/interceptor/TrustAllManager.java"
            line="11"
            column="14"/>
    </issue>

    <issue
        id="UnsafeDynamicallyLoadedCode"
        message="Dynamically loading code using `load` is risky, please use `loadLibrary` instead when possible"
        errorLine1="        System.load(path);"
        errorLine2="        ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/tvbus/engine/TVCore.java"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is never &lt; 26"
        errorLine1="        return Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.N ? Uri.fromFile(file) : FileProvider.getUriForFile(App.get(), App.get().getPackageName() + &quot;.provider&quot;, file);"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/utils/FileUtil.java"
            line="116"
            column="16"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="    @TargetApi(26)"
        errorLine2="    ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/hook/Hook.java"
            line="53"
            column="5"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/network/NetworkUtils.java"
            line="33"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/network/NetworkUtils.java"
            line="78"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/network/NetworkUtils.java"
            line="119"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/network/NetworkUtils.java"
            line="160"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O &amp;&amp;"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/network/NetworkUtils.java"
            line="178"
            column="28"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/utils/Util.java"
            line="173"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `App` which has field `activity` pointing to `Activity`); this is a memory leak"
        errorLine1="    private static App instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/App.java"
            line="44"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `AppSpiderManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static AppSpiderManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/spider/AppSpiderManager.java"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `IntegrationManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static IntegrationManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/adapter/IntegrationManager.java"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `JsEnginePool` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static JsEnginePool instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/spider/engine/JsEnginePool.java"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `JsSpiderManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static JsSpiderManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/spider/engine/JsSpiderManager.java"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `SpiderEngineManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static SpiderEngineManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/spider/engine/SpiderEngineManager.java"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `SpiderJarManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static SpiderJarManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/spider/SpiderJarManager.java"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `XPathSpiderManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static XPathSpiderManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/spider/engine/XPathSpiderManager.java"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="KaptUsageInsteadOfKsp"
        message="This library supports using KSP instead of kapt, which greatly improves performance. Learn more: https://developer.android.com/studio/build/migrate-to-ksp"
        errorLine1="    kapt(&quot;androidx.room:room-compiler:2.7.1&quot;)"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="369"
            column="5"/>
    </issue>

    <issue
        id="KaptUsageInsteadOfKsp"
        message="This library supports using KSP instead of kapt, which greatly improves performance. Learn more: https://developer.android.com/studio/build/migrate-to-ksp"
        errorLine1="    kapt(&quot;com.github.bumptech.glide:compiler:4.16.0&quot;)"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="405"
            column="5"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`"
        errorLine1="    var recompositionTrigger by remember { mutableStateOf(0) }"
        errorLine2="                                           ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/screens/MovieHomeScreen.kt"
            line="85"
            column="44"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        message="Prefer `mutableLongStateOf` instead of `mutableStateOf`"
        errorLine1="    val recompositionTrigger by remember { mutableStateOf(System.currentTimeMillis()) }"
        errorLine2="                                           ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/ui/screens/MovieHomeScreen.kt"
            line="635"
            column="44"/>
    </issue>

    <issue
        id="TypographyDashes"
        message="Replace &quot;-&quot; with an &quot;en dash&quot; character (–, &amp;#8211;) ?"
        errorLine1="        &lt;item>ISO-8859-1&lt;/item>"
        errorLine2="              ~~~~~~~~~~">
        <location
            file="src/main/res/values/vod_arrays.xml"
            line="57"
            column="15"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/vod_img_empty.png` in densityless folder">
        <location
            file="src/main/res/drawable/vod_img_empty.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/vod_img_error.png` in densityless folder">
        <location
            file="src/main/res/drawable/vod_img_error.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/vod_img_loading.png` in densityless folder">
        <location
            file="src/main/res/drawable/vod_img_loading.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        message="The following unrelated icon files have identical contents: vod_img_empty.png, vod_img_error.png, vod_img_loading.png">
        <location
            file="src/main/res/drawable/vod_img_loading.png"/>
        <location
            file="src/main/res/drawable/vod_img_error.png"/>
        <location
            file="src/main/res/drawable/vod_img_empty.png"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="220"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-livedata-ktx:2.7.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="221"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.fragment:fragment-ktx:1.6.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="224"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.squareup.okhttp3:logging-interceptor:4.12.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="246"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.code.gson:gson:2.10.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="247"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.jsoup:jsoup:1.17.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="248"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.greenrobot:eventbus:3.3.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="251"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.orhanobut:logger:2.2.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="254"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-session:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="266"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-common:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="267"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-datasource:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="268"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.work:work-runtime-ktx:2.9.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="271"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.localbroadcastmanager:localbroadcastmanager:1.1.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="272"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;wang.harlon.quickjs:wrapper-java:3.2.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="295"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;wang.harlon.quickjs:wrapper-android:3.2.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="296"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;net.sourceforge.streamsupport:android-retrofuture:1.7.4&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="297"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-container:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="300"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-database:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="301"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-datasource-okhttp:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="302"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-datasource-rtmp:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="303"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-decoder:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="304"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-effect:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="305"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-exoplayer-dash:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="306"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-exoplayer-rtsp:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="307"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-exoplayer-smoothstreaming:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="308"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media3:media3-extractor:1.6.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="309"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.github.thegrizzlylabs:sardine-android:0.9&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="312"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.github.teamnewpipe:NewPipeExtractor:v0.24.6&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="313"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.hierynomus:smbj:0.14.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="314"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;io.antmedia:rtmp-client:3.2.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="315"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.nanohttpd:nanohttpd:2.3.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="318"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.eclipse.jetty:jetty-client:8.1.21.v20160908&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="319"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;javax.servlet:javax.servlet-api:3.1.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="326"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.github.bumptech.glide:glide:4.16.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="329"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.github.bumptech.glide:annotations:4.16.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="330"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.github.bumptech.glide:okhttp3-integration:4.16.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="334"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.aomedia.avif.android:avif:1.1.1.14d8e3c4&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="335"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.zxing:core:3.5.3&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="338"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.guolindev.permissionx:permissionx:1.8.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="339"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;cat.ereza:customactivityoncrash:2.4.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="344"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.media:media:1.7.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="355"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.github.bassaer:materialdesigncolors:1.0.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="359"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.github.jahirfiquitiva:TextDrawable:1.0.3&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="360"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.android.material:material:1.12.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="361"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.fourthline.cling:cling-core:2.1.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="364"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.fourthline.cling:cling-support:2.1.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="365"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use the existing version catalog reference (`libs.androidx.room.runtime`) instead"
        errorLine1="    implementation(&quot;androidx.room:room-runtime:2.7.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="368"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use the existing version catalog reference (`libs.androidx.room.compiler`) instead"
        errorLine1="    kapt(&quot;androidx.room:room-compiler:2.7.1&quot;)"
        errorLine2="         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="369"
            column="10"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.leanback:leanback:1.2.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="372"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.github.JessYanCoding:AndroidAutoSize:1.2.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="373"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.biometric:biometric:1.1.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="374"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.swiperefreshlayout:swiperefreshlayout:1.1.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="375"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.android.flexbox:flexbox:3.0.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="376"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.squareup.okhttp3:okhttp-dnsoverhttps:4.12.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="385"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.preference:preference-ktx:1.2.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="388"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    kapt(&quot;com.github.bumptech.glide:compiler:4.16.0&quot;)"
        errorLine2="         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="405"
            column="10"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    kapt(&quot;org.greenrobot:eventbus-annotation-processor:3.3.1&quot;)"
        errorLine2="         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="406"
            column="10"/>
    </issue>

    <issue
        id="ConstantLocale"
        message="Assigning `Locale.getDefault()` to a final static field is suspicious; this code will not work correctly if the user changes locale while the app is running"
        errorLine1="    private static final SimpleDateFormat formatTime = new SimpleDateFormat(&quot;HH:mm&quot;, Locale.getDefault());"
        errorLine2="                                                                                     ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/api/EpgParser.java"
            line="31"
            column="86"/>
    </issue>

    <issue
        id="ConstantLocale"
        message="Assigning `Locale.getDefault()` to a final static field is suspicious; this code will not work correctly if the user changes locale while the app is running"
        errorLine1="    private static final SimpleDateFormat formatDate = new SimpleDateFormat(&quot;yyyy-MM-dd&quot;, Locale.getDefault());"
        errorLine2="                                                                                          ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/api/EpgParser.java"
            line="32"
            column="91"/>
    </issue>

    <issue
        id="ConstantLocale"
        message="Assigning `Locale.getDefault()` to a final static field is suspicious; this code will not work correctly if the user changes locale while the app is running"
        errorLine1="    private static final SimpleDateFormat formatFull = new SimpleDateFormat(&quot;yyyyMMddHHmmss Z&quot;, Locale.getDefault());"
        errorLine2="                                                                                                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/top/cywin/onetv/movie/api/EpgParser.java"
            line="33"
            column="97"/>
    </issue>

</issues>
