package com.github.catvod;

import com.github.catvod.utils.Util;
import java.util.Map;

public class Proxy {

    private static int port = -1;

    public static void set(int port) {
        Proxy.port = port;
    }

    public static int getPort() {
        return port;
    }

    public static String getUrl(boolean local) {
        return "http://" + (local ? "127.0.0.1" : Util.getIp()) + ":" + getPort() + "/proxy";
    }

    // 🔧 修复：添加FongMi_TV期望的proxy方法
    public static Object[] proxy(Map<String, String> params) {
        // 返回空数组，表示没有代理处理
        return new Object[0];
    }
}
