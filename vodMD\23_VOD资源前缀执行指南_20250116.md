# VOD资源前缀执行指南

## 快速开始

### 一键执行（推荐）
```bash
# 在项目根目录执行
cd d:\apk\OneTV_Movie_Supabase
python scripts/vod_resource_setup.py
```

这个脚本会自动完成所有步骤：
1. ✅ 环境检查
2. ✅ 创建备份
3. ✅ 更新Gradle配置
4. ✅ 资源重命名
5. ✅ 测试编译
6. ✅ 运行验证
7. ✅ 提交更改

## 分步执行

### 1. 预览模式
```bash
# 查看将要进行的更改（不实际修改文件）
python scripts/rename_vod_resources.py --dry-run
```

### 2. 处理特定模块
```bash
# 只处理主模块
python scripts/rename_vod_resources.py --module main

# 只处理Leanback模块
python scripts/rename_vod_resources.py --module leanback

# 只处理Mobile模块
python scripts/rename_vod_resources.py --module mobile

# 只处理CatVOD模块
python scripts/rename_vod_resources.py --module catvod

# 处理所有模块
python scripts/rename_vod_resources.py --module all
```

### 3. 验证测试
```bash
# 运行所有测试
python scripts/test_vod_resources.py

# 运行特定测试
python scripts/test_vod_resources.py --test prefix      # 前缀验证
python scripts/test_vod_resources.py --test conflicts  # 冲突检测
python scripts/test_vod_resources.py --test references # 引用完整性
python scripts/test_vod_resources.py --test compile    # 编译测试
python scripts/test_vod_resources.py --test gradle     # Gradle配置
```

## 执行前准备

### 1. 环境要求
- Python 3.6+
- Git (用于版本控制)
- Android SDK (用于编译测试)
- 在OneTV项目根目录执行

### 2. 状态检查
```bash
# 检查当前Git状态
git status

# 如果有未提交的更改，建议先提交
git add .
git commit -m "保存当前状态"
```

### 3. 创建工作分支（可选）
```bash
git checkout -b feature/vod-resource-prefix
```

## 执行过程

### 自动化流程
脚本会按以下顺序执行：

1. **环境检查**
   - 验证项目结构
   - 检查Python版本
   - 确认Git状态

2. **创建备份**
   - 自动备份整个vod目录
   - 备份名称包含时间戳

3. **更新Gradle配置**
   - 在build.gradle.kts中添加resourcePrefix
   - 添加验证任务

4. **资源重命名**
   - 扫描所有资源文件
   - 重命名文件添加vod_前缀
   - 更新所有引用

5. **验证测试**
   - 编译测试
   - 前缀验证
   - 冲突检测
   - 引用完整性检查

6. **提交更改**
   - 自动提交到Git
   - 生成详细的提交信息

### 交互确认
脚本会在关键步骤暂停，等待用户确认：
- 预览更改后确认执行
- 每个模块处理完成后确认
- 提交更改前确认

## 预期结果

### 文件变更
```
vod/src/main/res/drawable/
├── vod_ic_file.xml          (原: ic_file.xml)
├── vod_ic_folder.xml        (原: ic_folder.xml)
└── vod_shape_cursor.xml     (原: shape_cursor.xml)

vod/src/leanback/res/layout/
├── vod_activity_home.xml    (原: activity_home.xml)
├── vod_activity_vod.xml     (原: activity_vod.xml)
└── ...
```

### 引用更新
```xml
<!-- XML文件中 -->
<ImageView android:src="@drawable/vod_ic_file" />

<!-- 代码文件中 -->
R.drawable.vod_ic_file
```

### Gradle配置
```kotlin
android {
    resourcePrefix = "vod_"
    // ...
}
```

## 验证步骤

### 1. 编译验证
```bash
# 清理并重新编译
./gradlew clean
./gradlew :vod:assembleDebug
./gradlew :tv:assembleDebug
```

### 2. 功能验证
1. 启动TV应用
2. 进入"影视点播"模块
3. 验证界面显示正常
4. 测试基本功能

### 3. 资源验证
```bash
# 检查所有资源文件都有前缀
find vod/src -name "*.xml" -o -name "*.png" | grep -v "vod_" | grep -v "values"

# 检查是否有资源冲突
python scripts/test_vod_resources.py --test conflicts
```

## 故障排除

### 常见问题

#### 1. 编译错误
**症状**: `error: resource drawable/xxx not found`
**解决**: 
```bash
# 检查引用更新
python scripts/test_vod_resources.py --test references

# 手动检查特定文件
grep -r "drawable/[^v]" vod/src/
```

#### 2. 脚本执行失败
**症状**: Python脚本报错
**解决**:
```bash
# 检查Python版本
python --version

# 检查文件权限
ls -la scripts/

# 使用详细模式运行
python -v scripts/vod_resource_setup.py
```

#### 3. Git提交失败
**症状**: 无法提交更改
**解决**:
```bash
# 检查Git状态
git status

# 手动添加文件
git add vod/
git commit -m "feat: 为VOD模块资源添加vod_前缀"
```

### 回滚操作

#### 使用备份恢复
```bash
# 查看备份
ls -la vod_backup_*

# 恢复备份
rm -rf vod
mv vod_backup_20250116_143022 vod
```

#### 使用Git恢复
```bash
# 查看提交历史
git log --oneline

# 回滚到指定提交
git reset --hard HEAD~1

# 或者撤销特定文件
git checkout HEAD~1 -- vod/
```

## 后续维护

### 新资源添加规范
1. **文件命名**: 所有新资源文件必须以`vod_`开头
2. **验证检查**: 添加后运行 `./gradlew :vod:validateResourcePrefix`
3. **提交前测试**: 运行 `python scripts/test_vod_resources.py`

### 定期检查
```bash
# 每月运行一次完整检查
python scripts/test_vod_resources.py

# 检查是否有新的无前缀资源
./gradlew :vod:validateResourcePrefix
```

### CI/CD集成
在`.github/workflows/`中添加自动检查：
```yaml
- name: Validate VOD Resources
  run: python scripts/test_vod_resources.py
```

## 技术支持

### 日志文件
- 执行报告: `vod_resource_rename_report_*.txt`
- 错误日志: 控制台输出
- Git历史: `git log --oneline`

### 联系方式
如遇到问题，请：
1. 查看执行日志
2. 运行测试脚本诊断
3. 检查Git状态
4. 提供详细错误信息

---

**执行前请确保**:
- ✅ 已备份重要数据
- ✅ 在正确的项目目录
- ✅ Git状态干净
- ✅ Python环境正常

**执行后请验证**:
- ✅ 编译成功
- ✅ 功能正常
- ✅ 无资源冲突
- ✅ Git提交完成
