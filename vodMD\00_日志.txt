> Task :vod:compileDebugJavaWithJavac
注: [1] Wrote GeneratedAppGlideModule with: [com.bumptech.glide.integration.okhttp3.OkHttpLibraryGlideModule, com.bumptech.glide.integration.avif.AvifGlideModule]
D:\apk\OneTV_Movie_Supabase\vod\src\main\java\top\cywin\onetv\vod\utils\Util.java:148: 错误: 找不到符号
        return "leanback".equals(BuildConfig.FLAVOR_mode);
                                            ^
  符号:   变量 FLAVOR_mode
  位置: 类 BuildConfig
D:\apk\OneTV_Movie_Supabase\vod\src\main\java\top\cywin\onetv\vod\utils\Util.java:152: 错误: 找不到符号
        return "mobile".equals(BuildConfig.FLAVOR_mode);
                                          ^
  符号:   变量 FLAVOR_mode
  位置: 类 BuildConfig

> Task :vod:compileDebugJavaWithJavac
D:\apk\OneTV_Movie_Supabase\vod\src\leanback\java\top\cywin\onetv\vod\Updater.java:36: 错误: 找不到符号
        return Github.getJson(dev, BuildConfig.FLAVOR_mode);
                                              ^
  符号:   变量 FLAVOR_mode
  位置: 类 BuildConfig
D:\apk\OneTV_Movie_Supabase\vod\src\leanback\java\top\cywin\onetv\vod\Updater.java:40: 错误: 找不到符号
        return Github.getApk(dev, BuildConfig.FLAVOR_mode + "-" + BuildConfig.FLAVOR_abi);
                                             ^
  符号:   变量 FLAVOR_mode
  位置: 类 BuildConfig
D:\apk\OneTV_Movie_Supabase\vod\src\leanback\java\top\cywin\onetv\vod\Updater.java:40: 错误: 找不到符号
        return Github.getApk(dev, BuildConfig.FLAVOR_mode + "-" + BuildConfig.FLAVOR_abi);
                                                                             ^
  符号:   变量 FLAVOR_abi
  位置: 类 BuildConfig
注: 某些输入文件使用或覆盖了已过时的 API。
注: 有关详细信息, 请使用 -Xlint:deprecation 重新编译。
注: 某些输入文件使用了未经检查或不安全的操作。
注: 有关详细信息, 请使用 -Xlint:unchecked 重新编译。
5 个错误

> Task :vod:compileDebugJavaWithJavac FAILED
