# onevod模块完整文件结构分析

> 生成时间: 2025-07-14
> 模块版本: 2.1.1
> 基于: FongMi_TV 4.6.8 完整移植

## 概述

onevod模块是基于FongMi_TV项目完整移植的影视点播系统，包含了完整的TVBOX兼容功能，支持多种解析引擎和播放器。

## 根目录结构

```
onevod/
├── build.gradle.kts                    # 主模块构建配置
├── consumer-rules.pro                  # 消费者混淆规则
├── proguard-rules.pro                  # 混淆规则配置
├── LICENSE.md                          # 许可证文件
├── README.md                           # 模块说明文档
├── build/                              # 构建输出目录
├── libs/                               # 本地AAR库文件
├── schemas/                            # Room数据库架构文件
├── src/                                # 源码目录
├── catvod/                             # 爬虫核心引擎子模块
├── chaquo/                             # Python引擎子模块
├── quickjs/                            # JavaScript引擎子模块
├── hook/                               # Hook机制子模块
├── forcetech/                          # 解析器模块
├── jianpian/                           # 解析器模块
├── thunder/                            # 迅雷解析器子模块
├── tvbus/                              # 直播解析器子模块
└── other/                              # 其他资源文件
```

## 核心配置文件

### 1. build.gradle.kts
- **功能**: 主模块构建配置
- **版本**: 2.1.1 (VERSION_CODE: 211)
- **特性**: 支持多flavor构建(leanback/mobile, java/python, arm64/arm32)

### 2. consumer-rules.pro / proguard-rules.pro
- **功能**: 代码混淆和优化规则
- **用途**: 保护代码安全，减小APK体积

## 源码目录结构 (src/)

### 主要源码集

```
src/
├── main/                               # 主源码集
│   ├── AndroidManifest.xml            # 主清单文件
│   ├── java/                          # Java源码
│   ├── res/                           # 资源文件
│   ├── assets/                        # 资产文件
│   └── python/                        # Python脚本
├── leanback/                          # TV版本源码集
│   ├── AndroidManifest.xml            # TV版清单文件
│   ├── java/                          # TV版Java源码
│   └── res/                           # TV版资源文件
└── mobile/                            # 手机版源码集
    ├── AndroidManifest.xml            # 手机版清单文件
    ├── java/                          # 手机版Java源码
    └── res/                           # 手机版资源文件
```

## 主要Java包结构

### 核心包 (onetv.tv)

```
onetv.tv/
├── App.java                           # 应用程序主类
├── Product.java                       # 产品配置类
├── Setting.java                       # 设置管理类
├── Updater.java                       # 更新管理类
├── api/                               # API接口层
│   ├── config/                        # 配置管理
│   │   ├── VodConfig.java             # 点播配置管理
│   │   ├── LiveConfig.java            # 直播配置管理
│   │   └── WallConfig.java            # 壁纸配置管理
│   └── loader/                        # 加载器
├── bean/                              # 数据模型
│   ├── Vod.java                       # 点播视频模型
│   ├── Live.java                      # 直播频道模型
│   ├── Site.java                      # 站点模型
│   ├── Parse.java                     # 解析器模型
│   └── Config.java                    # 配置模型
├── db/                                # 数据库层
│   ├── AppDatabase.java               # 数据库主类
│   └── dao/                           # 数据访问对象
├── event/                             # 事件总线
├── model/                             # ViewModel层
├── net/                               # 网络层
├── player/                            # 播放器层
├── server/                            # 本地服务器
├── service/                           # 系统服务
├── ui/                                # 用户界面层
└── utils/                             # 工具类
```

## 用户界面层 (ui/)

### Activity层

```
ui/activity/
├── HomeActivity.java                  # 主界面Activity
├── VodActivity.java                   # 点播分类Activity
├── VideoActivity.java                 # 视频播放Activity
├── LiveActivity.java                  # 直播Activity
├── SearchActivity.java                # 搜索Activity
├── SettingActivity.java               # 设置Activity
├── CollectActivity.java               # 收藏Activity
├── KeepActivity.java                  # 追剧Activity
├── PushActivity.java                  # 推送Activity
├── CastActivity.java                  # 投屏Activity
└── FileActivity.java                  # 文件管理Activity
```

### Fragment层

```
ui/fragment/
├── VodFragment.java                   # 点播内容Fragment
├── SettingFragment.java               # 设置Fragment
└── SettingPlayerFragment.java         # 播放器设置Fragment
```

### 适配器层

```
ui/adapter/
├── VodAdapter.java                    # 点播视频适配器
├── EpisodeAdapter.java                # 剧集适配器
├── ParseAdapter.java                  # 解析器适配器
├── QualityAdapter.java                # 清晰度适配器
└── ...                                # 其他适配器
```

## 资源文件结构 (res/)

### 布局文件

```
res/layout/
├── activity_home.xml                  # 主界面布局
├── activity_vod.xml                   # 点播界面布局
├── activity_video.xml                 # 播放界面布局
├── fragment_vod.xml                   # 点播Fragment布局
├── adapter_vod.xml                    # 点播项布局
└── ...                                # 其他布局文件
```

### 资源文件

```
res/
├── drawable/                          # 图标和图片资源
├── values/                            # 字符串、颜色、样式
├── values-zh-rCN/                     # 简体中文资源
├── values-zh-rTW/                     # 繁体中文资源
└── anim/                              # 动画资源
```

## 子模块结构

### 1. catvod/ - 爬虫核心引擎
- **功能**: 提供爬虫接口和基础实现
- **包名**: com.github.catvod.crawler

### 2. chaquo/ - Python引擎
- **功能**: Python脚本执行环境
- **包名**: com.fongmi.chaquo
- **特性**: 支持Python解析器

### 3. quickjs/ - JavaScript引擎
- **功能**: JavaScript脚本执行环境
- **包名**: com.fongmi.quickjs
- **特性**: 高性能JS引擎

### 4. hook/ - Hook机制
- **功能**: 提供Hook拦截功能
- **包名**: com.github.tvbox.osc.hook

### 5. forcetech/ - 解析器模块
- **功能**: 特定解析器实现
- **包名**: com.forcetech.android

### 6. jianpian/ - 解析器模块
- **功能**: 特定解析器实现
- **包名**: com.jianpian.android

### 7. thunder/ - 迅雷解析器
- **功能**: 迅雷下载解析
- **包名**: com.thunder.android

### 8. tvbus/ - 直播解析器
- **功能**: 直播流解析
- **包名**: com.tvbus.android

## 本地库文件 (libs/)

```
libs/
├── dfm-release.aar                    # DFM库
├── dlna-core-release.aar              # DLNA核心库
├── dlna-dmc-release.aar               # DLNA DMC库
├── dlna-dmr-release.aar               # DLNA DMR库
├── forcetech-release.aar              # Forcetech解析库
├── hook-release.aar                   # Hook库
├── jianpian-release.aar               # Jianpian解析库
├── thunder-release.aar                # 迅雷库
└── tvbus-release.aar                  # TVBus库
```

## 资产文件 (assets/)

```
assets/
├── js/                                # JavaScript脚本
│   ├── script.js                      # 主脚本文件
│   └── lib/                           # JS库文件
└── python/                            # Python脚本
```

## 数据库架构 (schemas/)

```
schemas/
└── onetv.tv.db.AppDatabase/
    ├── 1.json                         # 数据库版本1架构
    ├── 2.json                         # 数据库版本2架构
    └── ...                            # 其他版本架构
```

## 关键特性

### 1. 多平台支持
- **TV版本**: 适配Android TV和机顶盒
- **手机版本**: 适配手机和平板设备

### 2. 多引擎支持
- **Java引擎**: 原生Java解析器
- **Python引擎**: Python脚本解析器
- **JavaScript引擎**: JS脚本解析器

### 3. 多架构支持
- **ARM64**: arm64-v8a架构
- **ARM32**: armeabi-v7a架构

### 4. 完整功能
- 点播视频播放
- 直播频道播放
- 搜索功能
- 收藏追剧
- 投屏功能
- 本地文件播放
- 推送播放

## 集成说明

onevod模块已完全集成到OneTV项目中，通过以下方式调用：

1. **主应用依赖**: tv模块已添加onevod依赖
2. **Activity声明**: 主应用AndroidManifest.xml已声明onevod的Activity
3. **导航集成**: 直播系统的"影视点播"按钮已配置为启动onevod模块

## 版本信息

- **模块版本**: 2.1.1
- **版本代码**: 211
- **基于版本**: FongMi_TV 4.6.8
- **目标SDK**: 34
- **最小SDK**: 21
- **编译SDK**: 35

## 详细文件说明

### 核心Java类详细说明

#### 应用程序核心类

**App.java**
- 应用程序主类，继承自Application
- 初始化全局配置和服务
- 管理应用程序生命周期

**Product.java**
- 产品配置和常量定义
- 设备信息和版本管理
- 构建配置相关功能

**Setting.java**
- 应用设置管理类
- SharedPreferences封装
- 用户偏好设置存储

**Updater.java**
- 应用更新检查和下载
- 版本比较和升级逻辑
- APK安装管理

#### API配置层

**VodConfig.java**
- 点播配置管理核心类
- 站点配置加载和解析
- 解析器配置管理
- 支持多配置源切换

**LiveConfig.java**
- 直播配置管理
- M3U播放列表解析
- 频道分组和管理

**WallConfig.java**
- 壁纸配置管理
- 背景图片设置
- 主题相关配置

#### 数据模型层

**Vod.java**
- 点播视频数据模型
- 包含视频ID、名称、图片、播放地址等
- 支持多集、多线路播放

**Live.java**
- 直播频道数据模型
- 频道信息和播放地址
- EPG节目单支持

**Site.java**
- 站点配置模型
- 支持多种站点类型(API、爬虫等)
- 站点状态和配置管理

**Parse.java**
- 解析器配置模型
- 支持多种解析器类型
- 解析器优先级管理

#### 数据库层

**AppDatabase.java**
- Room数据库主类
- 数据库版本管理
- 实体类和DAO注册

**DAO接口**
- ConfigDao: 配置数据访问
- HistoryDao: 观看历史访问
- KeepDao: 收藏数据访问

#### 网络层

**OkHttp.java**
- HTTP客户端封装
- 请求拦截和处理
- 超时和重试配置

**Decoder.java**
- 数据解码和解密
- 支持多种编码格式
- 配置文件解析

#### 播放器层

**Players.java**
- 播放器管理类
- 支持多种播放器内核
- 播放器切换和配置

**ExoUtil.java**
- ExoPlayer工具类
- 媒体源创建和管理
- 播放器事件处理

#### 服务器层

**Nano.java**
- 内置HTTP服务器
- 本地文件服务
- API接口提供

**Local.java**
- 本地文件处理
- 文件浏览和管理
- 上传下载功能

### UI层详细说明

#### 主要Activity

**HomeActivity.java**
- 应用主界面
- 功能导航和内容展示
- 配置加载和初始化

**VodActivity.java**
- 点播分类浏览界面
- 视频列表展示
- 分类筛选功能

**VideoActivity.java**
- 视频播放界面
- 播放控制和设置
- 弹幕和字幕支持

**LiveActivity.java**
- 直播播放界面
- 频道切换和EPG
- 直播流处理

**SearchActivity.java**
- 搜索功能界面
- 多站点搜索
- 搜索历史管理

#### Fragment组件

**VodFragment.java**
- 点播内容展示Fragment
- 视频网格/列表布局
- 分页加载支持

**SettingFragment.java**
- 设置界面Fragment
- 配置项管理
- 用户偏好设置

#### 适配器类

**VodAdapter.java**
- 点播视频列表适配器
- 支持多种布局样式
- 图片加载和缓存

**EpisodeAdapter.java**
- 剧集选择适配器
- 播放进度显示
- 剧集状态管理

### 资源文件详细说明

#### 布局文件

**activity_home.xml**
- 主界面布局文件
- TV版本使用Leanback组件
- 手机版本使用标准布局

**activity_video.xml**
- 视频播放界面布局
- 播放器容器和控制栏
- 全屏和窗口模式支持

#### 字符串资源

**strings.xml**
- 应用文本资源
- 支持多语言国际化
- 错误信息和提示文本

#### 样式资源

**styles.xml**
- 应用主题和样式定义
- TV和手机版本差异化样式
- 颜色和尺寸配置

### 子模块详细说明

#### catvod模块
- **Spider.java**: 爬虫接口定义
- **JsonParallel.java**: 并行JSON解析
- **功能**: 提供统一的爬虫接口

#### quickjs模块
- **QuickJS.java**: JS引擎封装
- **JSEngine.java**: JS执行环境
- **功能**: 高性能JavaScript执行

#### chaquo模块
- **Python.java**: Python引擎接口
- **requirements.txt**: Python依赖配置
- **功能**: Python脚本执行支持

#### hook模块
- **HookLoader.java**: Hook加载器
- **功能**: 提供动态Hook能力

### 配置文件说明

#### build.gradle.kts
```kotlin
// 主要配置项
namespace = "onetvv"
compileSdk = 35
minSdk = 21
targetSdk = 34
versionName = "2.1.1"
versionCode = 211

// 构建变体
productFlavors {
    leanback/mobile  // 平台变体
    java/python      // API变体
    arm64/arm32      // 架构变体
}
```

#### AndroidManifest.xml
- Activity声明和配置
- 权限申请
- 服务注册
- 特性声明

#### proguard-rules.pro
- 代码混淆规则
- 保留关键类和方法
- 优化配置

## 完整文件清单

### 根目录文件
```
onevod/
├── build.gradle.kts                    # 主模块构建配置
├── consumer-rules.pro                  # 消费者混淆规则
├── proguard-rules.pro                  # 混淆规则配置
├── LICENSE.md                          # MIT许可证
└── README.md                           # 模块说明文档
```

### 本地库文件 (libs/)
```
libs/
├── dfm-release.aar                     # DFM动态功能模块库
├── dlna-core-release.aar               # DLNA核心功能库
├── dlna-dmc-release.aar                # DLNA数字媒体控制器
├── dlna-dmr-release.aar                # DLNA数字媒体渲染器
├── forcetech-release.aar               # Forcetech解析引擎
├── hook-release.aar                    # Hook拦截库
├── jianpian-release.aar                # 简片解析引擎
├── thunder-release.aar                 # 迅雷下载引擎
└── tvbus-release.aar                   # TVBus直播解析库
```

### 数据库架构文件 (schemas/)
```
schemas/onetv.tv.db.AppDatabase/
├── 1.json                              # 数据库版本1架构定义
├── 2.json                              # 数据库版本2架构定义
├── 3.json                              # 数据库版本3架构定义
└── ...                                 # 其他版本架构文件
```

### 主源码集 (src/main/)

#### Java源码 (src/main/java/)
```
onetv.tv/
├── App.java                            # 应用程序主类
├── Product.java                        # 产品配置类
├── Setting.java                        # 设置管理类
├── Updater.java                        # 更新管理类
├── api/                                # API接口层
│   ├── config/
│   │   ├── VodConfig.java              # 点播配置管理
│   │   ├── LiveConfig.java             # 直播配置管理
│   │   └── WallConfig.java             # 壁纸配置管理
│   └── loader/
│       ├── BaseLoader.java             # 基础加载器
│       └── JarLoader.java              # JAR动态加载器
├── bean/                               # 数据模型层
│   ├── Vod.java                        # 点播视频模型
│   ├── Live.java                       # 直播频道模型
│   ├── Site.java                       # 站点配置模型
│   ├── Parse.java                      # 解析器模型
│   ├── Config.java                     # 配置模型
│   ├── Episode.java                    # 剧集模型
│   ├── Flag.java                       # 播放标识模型
│   ├── History.java                    # 观看历史模型
│   ├── Keep.java                       # 收藏模型
│   └── Result.java                     # 结果模型
├── db/                                 # 数据库层
│   ├── AppDatabase.java                # Room数据库主类
│   └── dao/                            # 数据访问对象
│       ├── ConfigDao.java              # 配置数据访问
│       ├── HistoryDao.java             # 历史数据访问
│       └── KeepDao.java                # 收藏数据访问
├── event/                              # 事件总线
│   ├── ActionEvent.java                # 动作事件
│   ├── ErrorEvent.java                 # 错误事件
│   ├── PlayerEvent.java                # 播放器事件
│   └── RefreshEvent.java               # 刷新事件
├── model/                              # ViewModel层
│   ├── SiteViewModel.java              # 站点视图模型
│   └── LiveViewModel.java              # 直播视图模型
├── net/                                # 网络层
│   ├── OkHttp.java                     # HTTP客户端
│   ├── Decoder.java                    # 数据解码器
│   └── UrlUtil.java                    # URL工具类
├── player/                             # 播放器层
│   ├── Players.java                    # 播放器管理
│   └── exo/                            # ExoPlayer相关
│       ├── ExoUtil.java                # ExoPlayer工具
│       └── TrackSelector.java          # 轨道选择器
├── server/                             # 本地服务器
│   ├── Nano.java                       # HTTP服务器
│   └── process/                        # 请求处理
│       ├── Local.java                  # 本地文件处理
│       └── RequestProcess.java         # 请求处理器
├── service/                            # 系统服务
│   └── PlaybackService.java           # 播放服务
├── ui/                                 # 用户界面层
│   ├── activity/                       # Activity层
│   ├── adapter/                        # 适配器层
│   ├── base/                           # 基础类
│   ├── custom/                         # 自定义组件
│   ├── dialog/                         # 对话框
│   ├── fragment/                       # Fragment层
│   ├── holder/                         # ViewHolder
│   └── presenter/                      # Presenter层
└── utils/                              # 工具类
    ├── FileUtil.java                   # 文件工具
    ├── ResUtil.java                    # 资源工具
    └── Utils.java                      # 通用工具
```

#### 资源文件 (src/main/res/)
```
res/
├── drawable/                           # 图标和图片
├── layout/                             # 布局文件
├── values/                             # 默认资源值
│   ├── strings.xml                     # 字符串资源
│   ├── colors.xml                      # 颜色资源
│   ├── styles.xml                      # 样式资源
│   └── arrays.xml                      # 数组资源
├── values-zh-rCN/                      # 简体中文资源
├── values-zh-rTW/                      # 繁体中文资源
└── xml/                                # XML配置文件
```

#### 资产文件 (src/main/assets/)
```
assets/
├── js/                                 # JavaScript脚本
│   ├── script.js                       # 主脚本文件
│   └── lib/                            # JS库文件
│       ├── crypto-js.js                # 加密库
│       ├── cheerio.min.js              # HTML解析库
│       └── utils.js                    # 工具函数
└── python/                             # Python脚本
    ├── spider.py                       # 爬虫脚本
    └── utils.py                        # Python工具
```

### TV版本源码集 (src/leanback/)

#### TV版Activity (src/leanback/java/.../ui/activity/)
```
activity/
├── HomeActivity.java                   # TV版主界面
├── VodActivity.java                    # TV版点播界面
├── VideoActivity.java                  # TV版播放界面
├── LiveActivity.java                   # TV版直播界面
├── SearchActivity.java                 # TV版搜索界面
├── SettingActivity.java                # TV版设置界面
├── CollectActivity.java                # TV版收藏界面
├── KeepActivity.java                   # TV版追剧界面
├── PushActivity.java                   # TV版推送界面
├── CastActivity.java                   # TV版投屏界面
└── FileActivity.java                   # TV版文件管理
```

#### TV版布局 (src/leanback/res/layout/)
```
layout/
├── activity_home.xml                   # TV版主界面布局
├── activity_vod.xml                    # TV版点播布局
├── activity_video.xml                  # TV版播放布局
├── fragment_vod.xml                    # TV版点播Fragment
├── adapter_vod.xml                     # TV版视频项布局
├── adapter_episode.xml                 # TV版剧集项布局
└── ...                                 # 其他TV版布局
```

### 手机版源码集 (src/mobile/)

#### 手机版Activity (src/mobile/java/.../ui/activity/)
```
activity/
├── HomeActivity.java                   # 手机版主界面
├── VideoActivity.java                  # 手机版播放界面
├── LiveActivity.java                   # 手机版直播界面
├── CollectActivity.java                # 手机版收藏界面
├── KeepActivity.java                   # 手机版追剧界面
├── HistoryActivity.java                # 手机版历史界面
├── FileActivity.java                   # 手机版文件管理
├── FolderActivity.java                 # 手机版文件夹界面
└── ScanActivity.java                   # 手机版扫码界面
```

#### 手机版布局 (src/mobile/res/layout/)
```
layout/
├── activity_home.xml                   # 手机版主界面布局
├── activity_video.xml                  # 手机版播放布局
├── fragment_vod.xml                    # 手机版点播Fragment
├── adapter_vod.xml                     # 手机版视频项布局
└── ...                                 # 其他手机版布局
```

## 子模块详细文件结构

### catvod子模块 (爬虫核心引擎)
```
catvod/
├── build.gradle.kts                    # 构建配置
├── src/main/java/com/github/catvod/crawler/
│   ├── Spider.java                     # 爬虫接口定义
│   ├── SpiderDebug.java                # 爬虫调试工具
│   └── JsonParallel.java               # 并行JSON解析
└── src/main/AndroidManifest.xml        # 清单文件
```

### quickjs子模块 (JavaScript引擎)
```
quickjs/
├── build.gradle.kts                    # 构建配置
├── src/main/java/com/fongmi/quickjs/
│   ├── QuickJS.java                    # JS引擎主类
│   ├── JSEngine.java                   # JS执行环境
│   └── utils/
│       └── Module.java                 # 模块加载器
├── src/main/cpp/                       # C++源码
└── src/main/AndroidManifest.xml        # 清单文件
```

### chaquo子模块 (Python引擎)
```
chaquo/
├── build.gradle.kts                    # 构建配置
├── requirements.txt                    # Python依赖
├── src/main/java/com/fongmi/chaquo/
│   └── Python.java                     # Python接口
├── src/main/python/                    # Python源码
└── src/main/AndroidManifest.xml        # 清单文件
```

### hook子模块 (Hook机制)
```
hook/
├── build.gradle.kts                    # 构建配置
├── src/main/java/com/github/tvbox/osc/hook/
│   └── HookLoader.java                 # Hook加载器
└── src/main/AndroidManifest.xml        # 清单文件
```

### 其他解析器子模块
```
forcetech/                              # Forcetech解析器
jianpian/                               # 简片解析器
thunder/                                # 迅雷解析器
tvbus/                                  # TVBus解析器
```

## 总结

onevod模块是一个功能完整的影视点播系统，包含：

1. **完整的TVBOX兼容性**: 支持标准TVBOX配置和解析器
2. **多平台支持**: TV版本和手机版本
3. **多引擎支持**: Java、Python、JavaScript解析引擎
4. **丰富的功能**: 点播、直播、搜索、收藏、投屏等
5. **模块化设计**: 清晰的模块划分和依赖关系
6. **完善的架构**: MVP架构，清晰的分层设计

该模块已成功集成到OneTV项目中，为用户提供完整的影视点播体验。

---

*此文档由OneTV项目自动生成，详细描述了onevod模块的完整文件结构和功能说明。*
*生成时间: 2025-07-14*
*文档版本: 1.0*
