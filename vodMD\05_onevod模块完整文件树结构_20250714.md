# onevod模块完整文件树结构

> 生成时间: 2025-07-14
> 模块版本: 2.1.1

## 完整文件树结构

```
onevod/
├── build.gradle.kts                                                    # 主模块构建配置
├── consumer-rules.pro                                                  # 消费者混淆规则
├── proguard-rules.pro                                                  # 混淆规则配置
├── LICENSE.md                                                          # MIT许可证
├── README.md                                                           # 模块说明文档
├── build/                                                              # 构建输出目录
│   ├── generated/                                                      # 生成的代码
│   ├── intermediates/                                                  # 中间文件
│   ├── outputs/                                                        # 输出文件
│   └── tmp/                                                            # 临时文件
├── libs/                                                               # 本地AAR库文件
│   ├── dfm-release.aar                                                 # DFM动态功能模块库
│   ├── dlna-core-release.aar                                           # DLNA核心功能库
│   ├── dlna-dmc-release.aar                                            # DLNA数字媒体控制器
│   ├── dlna-dmr-release.aar                                            # DLNA数字媒体渲染器
│   ├── forcetech-release.aar                                           # Forcetech解析引擎
│   ├── hook-release.aar                                                # Hook拦截库
│   ├── jianpian-release.aar                                            # 简片解析引擎
│   ├── thunder-release.aar                                             # 迅雷下载引擎
│   └── tvbus-release.aar                                               # TVBus直播解析库
├── schemas/                                                            # Room数据库架构文件
│   └── onetv.tv.db.AppDatabase/                          # 数据库架构目录
│       ├── 1.json                                                      # 数据库版本1架构
│       ├── 2.json                                                      # 数据库版本2架构
│       ├── 3.json                                                      # 数据库版本3架构
│       ├── 4.json                                                      # 数据库版本4架构
│       ├── 5.json                                                      # 数据库版本5架构
│       ├── 6.json                                                      # 数据库版本6架构
│       ├── 7.json                                                      # 数据库版本7架构
│       ├── 8.json                                                      # 数据库版本8架构
│       ├── 9.json                                                      # 数据库版本9架构
│       ├── 10.json                                                     # 数据库版本10架构
│       ├── 11.json                                                     # 数据库版本11架构
│       ├── 12.json                                                     # 数据库版本12架构
│       ├── 13.json                                                     # 数据库版本13架构
│       ├── 14.json                                                     # 数据库版本14架构
│       ├── 15.json                                                     # 数据库版本15架构
│       ├── 16.json                                                     # 数据库版本16架构
│       ├── 17.json                                                     # 数据库版本17架构
│       ├── 18.json                                                     # 数据库版本18架构
│       ├── 19.json                                                     # 数据库版本19架构
│       ├── 20.json                                                     # 数据库版本20架构
│       ├── 21.json                                                     # 数据库版本21架构
│       ├── 22.json                                                     # 数据库版本22架构
│       ├── 23.json                                                     # 数据库版本23架构
│       ├── 24.json                                                     # 数据库版本24架构
│       ├── 25.json                                                     # 数据库版本25架构
│       ├── 26.json                                                     # 数据库版本26架构
│       ├── 27.json                                                     # 数据库版本27架构
│       ├── 28.json                                                     # 数据库版本28架构
│       ├── 29.json                                                     # 数据库版本29架构
│       ├── 30.json                                                     # 数据库版本30架构
│       ├── 31.json                                                     # 数据库版本31架构
│       ├── 32.json                                                     # 数据库版本32架构
│       ├── 33.json                                                     # 数据库版本33架构
│       ├── 34.json                                                     # 数据库版本34架构
│       ├── 35.json                                                     # 数据库版本35架构
│       ├── 36.json                                                     # 数据库版本36架构
│       ├── 37.json                                                     # 数据库版本37架构
│       ├── 38.json                                                     # 数据库版本38架构
│       ├── 39.json                                                     # 数据库版本39架构
│       ├── 40.json                                                     # 数据库版本40架构
│       ├── 41.json                                                     # 数据库版本41架构
│       ├── 42.json                                                     # 数据库版本42架构
│       ├── 43.json                                                     # 数据库版本43架构
│       ├── 44.json                                                     # 数据库版本44架构
│       ├── 45.json                                                     # 数据库版本45架构
│       ├── 46.json                                                     # 数据库版本46架构
│       ├── 47.json                                                     # 数据库版本47架构
│       ├── 48.json                                                     # 数据库版本48架构
│       ├── 49.json                                                     # 数据库版本49架构
│       ├── 50.json                                                     # 数据库版本50架构
│       ├── 51.json                                                     # 数据库版本51架构
│       ├── 52.json                                                     # 数据库版本52架构
│       ├── 53.json                                                     # 数据库版本53架构
│       ├── 54.json                                                     # 数据库版本54架构
│       ├── 55.json                                                     # 数据库版本55架构
│       ├── 56.json                                                     # 数据库版本56架构
│       ├── 57.json                                                     # 数据库版本57架构
│       ├── 58.json                                                     # 数据库版本58架构
│       ├── 59.json                                                     # 数据库版本59架构
│       ├── 60.json                                                     # 数据库版本60架构
│       ├── 61.json                                                     # 数据库版本61架构
│       ├── 62.json                                                     # 数据库版本62架构
│       ├── 63.json                                                     # 数据库版本63架构
│       ├── 64.json                                                     # 数据库版本64架构
│       ├── 65.json                                                     # 数据库版本65架构
│       ├── 66.json                                                     # 数据库版本66架构
│       ├── 67.json                                                     # 数据库版本67架构
│       ├── 68.json                                                     # 数据库版本68架构
│       ├── 69.json                                                     # 数据库版本69架构
│       ├── 70.json                                                     # 数据库版本70架构
│       ├── 71.json                                                     # 数据库版本71架构
│       ├── 72.json                                                     # 数据库版本72架构
│       ├── 73.json                                                     # 数据库版本73架构
│       ├── 74.json                                                     # 数据库版本74架构
│       ├── 75.json                                                     # 数据库版本75架构
│       ├── 76.json                                                     # 数据库版本76架构
│       ├── 77.json                                                     # 数据库版本77架构
│       ├── 78.json                                                     # 数据库版本78架构
│       ├── 79.json                                                     # 数据库版本79架构
│       ├── 80.json                                                     # 数据库版本80架构
│       ├── 81.json                                                     # 数据库版本81架构
│       ├── 82.json                                                     # 数据库版本82架构
│       ├── 83.json                                                     # 数据库版本83架构
│       ├── 84.json                                                     # 数据库版本84架构
│       ├── 85.json                                                     # 数据库版本85架构
│       ├── 86.json                                                     # 数据库版本86架构
│       ├── 87.json                                                     # 数据库版本87架构
│       ├── 88.json                                                     # 数据库版本88架构
│       ├── 89.json                                                     # 数据库版本89架构
│       ├── 90.json                                                     # 数据库版本90架构
│       ├── 91.json                                                     # 数据库版本91架构
│       ├── 92.json                                                     # 数据库版本92架构
│       ├── 93.json                                                     # 数据库版本93架构
│       ├── 94.json                                                     # 数据库版本94架构
│       ├── 95.json                                                     # 数据库版本95架构
│       ├── 96.json                                                     # 数据库版本96架构
│       ├── 97.json                                                     # 数据库版本97架构
│       ├── 98.json                                                     # 数据库版本98架构
│       ├── 99.json                                                     # 数据库版本99架构
│       └── 100.json                                                    # 数据库版本100架构
├── src/                                                                # 源码目录
│   ├── main/                                                           # 主源码集
│   │   ├── AndroidManifest.xml                                         # 主清单文件
│   │   ├── assets/                                                     # 资产文件
│   │   │   ├── js/                                                     # JavaScript脚本
│   │   │   │   ├── script.js                                           # 主脚本文件
│   │   │   │   └── lib/                                                # JS库文件
│   │   │   │       ├── crypto-js.js                                    # 加密库
│   │   │   │       ├── cheerio.min.js                                  # HTML解析库
│   │   │   │       ├── dayjs.min.js                                    # 日期处理库
│   │   │   │       ├── gbk.js                                          # GBK编码库
│   │   │   │       ├── hls.js                                          # HLS播放库
│   │   │   │       ├── jsencrypt.js                                    # RSA加密库
│   │   │   │       ├── md5.js                                          # MD5哈希库
│   │   │   │       ├── pako.js                                         # 压缩库
│   │   │   │       ├── qrcode.js                                       # 二维码库
│   │   │   │       ├── underscore.js                                   # 工具函数库
│   │   │   │       └── utils.js                                        # 工具函数
│   │   │   └── python/                                                 # Python脚本
│   │   │       ├── spider.py                                           # 爬虫脚本
│   │   │       ├── utils.py                                            # Python工具
│   │   │       └── lib/                                                # Python库
│   │   │           ├── requests.py                                     # HTTP请求库
│   │   │           ├── bs4.py                                          # BeautifulSoup解析库
│   │   │           └── json.py                                         # JSON处理库
│   │   ├── java/                                                       # Java源码
│   │   │   └── com/                                                    # 包根目录
│   │   │       └── fongmi/                                             # 厂商包
│   │   │           └── android/                                        # Android包
│   │   │               └── tv/                                         # TV应用包
│   │   │                   ├── App.java                                # 应用程序主类
│   │   │                   ├── Product.java                            # 产品配置类
│   │   │                   ├── Setting.java                            # 设置管理类
│   │   │                   ├── Updater.java                            # 更新管理类
│   │   │                   ├── api/                                    # API接口层
│   │   │                   │   ├── config/                             # 配置管理
│   │   │                   │   │   ├── VodConfig.java                  # 点播配置管理
│   │   │                   │   │   ├── LiveConfig.java                 # 直播配置管理
│   │   │                   │   │   └── WallConfig.java                 # 壁纸配置管理
│   │   │                   │   └── loader/                             # 加载器
│   │   │                   │       ├── BaseLoader.java                 # 基础加载器
│   │   │                   │       └── JarLoader.java                  # JAR动态加载器
│   │   │                   ├── bean/                                   # 数据模型层
│   │   │                   │   ├── Vod.java                            # 点播视频模型
│   │   │                   │   ├── Live.java                           # 直播频道模型
│   │   │                   │   ├── Site.java                           # 站点配置模型
│   │   │                   │   ├── Parse.java                          # 解析器模型
│   │   │                   │   ├── Config.java                         # 配置模型
│   │   │                   │   ├── Episode.java                        # 剧集模型
│   │   │                   │   ├── Flag.java                           # 播放标识模型
│   │   │                   │   ├── History.java                        # 观看历史模型
│   │   │                   │   ├── Keep.java                           # 收藏模型
│   │   │                   │   ├── Result.java                         # 结果模型
│   │   │                   │   ├── Channel.java                        # 频道模型
│   │   │                   │   ├── Group.java                          # 分组模型
│   │   │                   │   ├── Epg.java                            # 节目单模型
│   │   │                   │   ├── EpgData.java                        # 节目数据模型
│   │   │                   │   ├── Filter.java                         # 筛选器模型
│   │   │                   │   ├── Page.java                           # 分页模型
│   │   │                   │   ├── Part.java                           # 部分模型
│   │   │                   │   ├── Quality.java                        # 清晰度模型
│   │   │                   │   ├── Rule.java                           # 规则模型
│   │   │                   │   ├── Style.java                          # 样式模型
│   │   │                   │   ├── Track.java                          # 轨道模型
│   │   │                   │   ├── Type.java                           # 类型模型
│   │   │                   │   ├── Value.java                          # 值模型
│   │   │                   │   ├── Doh.java                            # DNS over HTTPS模型
│   │   │                   │   ├── Hot.java                            # 热门模型
│   │   │                   │   ├── Func.java                           # 功能模型
│   │   │                   │   ├── Class.java                          # 分类模型
│   │   │                   │   ├── Device.java                         # 设备模型
│   │   │                   │   ├── Danmaku.java                        # 弹幕模型
│   │   │                   │   ├── Subtitle.java                       # 字幕模型
│   │   │                   │   ├── Proxy.java                          # 代理模型
│   │   │                   │   ├── Backup.java                         # 备份模型
│   │   │                   │   └── Sync.java                           # 同步模型
│   │   │                   ├── db/                                     # 数据库层
│   │   │                   │   ├── AppDatabase.java                    # Room数据库主类
│   │   │                   │   └── dao/                                # 数据访问对象
│   │   │                   │       ├── ConfigDao.java                  # 配置数据访问
│   │   │                   │       ├── HistoryDao.java                 # 历史数据访问
│   │   │                   │       └── KeepDao.java                    # 收藏数据访问
│   │   │                   ├── event/                                  # 事件总线
│   │   │                   │   ├── ActionEvent.java                    # 动作事件
│   │   │                   │   ├── ErrorEvent.java                     # 错误事件
│   │   │                   │   ├── PlayerEvent.java                    # 播放器事件
│   │   │                   │   ├── RefreshEvent.java                   # 刷新事件
│   │   │                   │   ├── CastEvent.java                      # 投屏事件
│   │   │                   │   └── ServerEvent.java                    # 服务器事件
│   │   │                   ├── model/                                  # ViewModel层
│   │   │                   │   ├── SiteViewModel.java                  # 站点视图模型
│   │   │                   │   └── LiveViewModel.java                  # 直播视图模型
│   │   │                   ├── net/                                    # 网络层
│   │   │                   │   ├── OkHttp.java                         # HTTP客户端
│   │   │                   │   ├── Decoder.java                        # 数据解码器
│   │   │                   │   ├── UrlUtil.java                        # URL工具类
│   │   │                   │   ├── Source.java                         # 数据源管理
│   │   │                   │   └── Download.java                       # 下载管理
│   │   │                   ├── player/                                 # 播放器层
│   │   │                   │   ├── Players.java                        # 播放器管理
│   │   │                   │   ├── Source.java                         # 播放源管理
│   │   │                   │   └── exo/                                # ExoPlayer相关
│   │   │                   │       ├── ExoUtil.java                    # ExoPlayer工具
│   │   │                   │       ├── TrackSelector.java              # 轨道选择器
│   │   │                   │       └── CustomDataSource.java           # 自定义数据源
│   │   │                   ├── server/                                 # 本地服务器
│   │   │                   │   ├── Nano.java                           # HTTP服务器
│   │   │                   │   └── process/                            # 请求处理
│   │   │                   │       ├── Local.java                      # 本地文件处理
│   │   │                   │       ├── RequestProcess.java             # 请求处理器
│   │   │                   │       ├── Action.java                     # 动作处理
│   │   │                   │       └── Proxy.java                      # 代理处理
│   │   │                   ├── service/                                # 系统服务
│   │   │                   │   └── PlaybackService.java                # 播放服务
│   │   │                   ├── ui/                                     # 用户界面层
│   │   │                   │   ├── activity/                           # Activity层
│   │   │                   │   ├── adapter/                            # 适配器层
│   │   │                   │   ├── base/                               # 基础类
│   │   │                   │   ├── custom/                             # 自定义组件
│   │   │                   │   ├── dialog/                             # 对话框
│   │   │                   │   ├── fragment/                           # Fragment层
│   │   │                   │   ├── holder/                             # ViewHolder
│   │   │                   │   └── presenter/                          # Presenter层
│   │   │                   └── utils/                                  # 工具类
│   │   │                       ├── FileUtil.java                       # 文件工具
│   │   │                       ├── ResUtil.java                        # 资源工具
│   │   │                       ├── Utils.java                          # 通用工具
│   │   │                       ├── Path.java                           # 路径工具
│   │   │                       ├── Prefers.java                        # 偏好设置工具
│   │   │                       ├── Json.java                           # JSON工具
│   │   │                       ├── Notify.java                         # 通知工具
│   │   │                       ├── Clock.java                          # 时钟工具
│   │   │                       ├── Traffic.java                        # 流量统计工具
│   │   │                       ├── Sniffer.java                        # 嗅探工具
│   │   │                       ├── M3U8.java                           # M3U8解析工具
│   │   │                       ├── Biometric.java                      # 生物识别工具
│   │   │                       ├── QRCode.java                         # 二维码工具
│   │   │                       ├── Subtitle.java                       # 字幕工具
│   │   │                       ├── Danmaku.java                        # 弹幕工具
│   │   │                       ├── Cast.java                           # 投屏工具
│   │   │                       ├── Server.java                         # 服务器工具
│   │   │                       ├── Backup.java                         # 备份工具
│   │   │                       └── Sync.java                           # 同步工具
│   │   ├── python/                                                     # Python源码
│   │   │   ├── main.py                                                 # Python主入口
│   │   │   ├── spider/                                                 # 爬虫模块
│   │   │   │   ├── __init__.py                                         # 模块初始化
│   │   │   │   ├── base.py                                             # 基础爬虫类
│   │   │   │   ├── parser.py                                           # 解析器
│   │   │   │   └── utils.py                                            # 工具函数
│   │   │   └── lib/                                                    # Python库
│   │   │       ├── requests.py                                         # HTTP请求库
│   │   │       ├── bs4.py                                              # BeautifulSoup解析库
│   │   │       ├── json.py                                             # JSON处理库
│   │   │       ├── re.py                                               # 正则表达式库
│   │   │       ├── base64.py                                           # Base64编码库
│   │   │       ├── urllib.py                                           # URL处理库
│   │   │       ├── hashlib.py                                          # 哈希库
│   │   │       └── time.py                                             # 时间处理库
│   │   └── res/                                                        # 资源文件
│   │       ├── drawable/                                               # 图标和图片
│   │       │   ├── ic_launcher.png                                     # 应用图标
│   │       │   ├── ic_banner.png                                       # 横幅图标
│   │       │   ├── ic_logo.png                                         # Logo图标
│   │       │   └── wallpaper_default.jpg                               # 默认壁纸
│   │       ├── layout/                                                 # 布局文件
│   │       │   ├── activity_main.xml                                   # 主Activity布局
│   │       │   ├── fragment_main.xml                                   # 主Fragment布局
│   │       │   └── item_default.xml                                    # 默认项布局
│   │       ├── values/                                                 # 默认资源值
│   │       │   ├── strings.xml                                         # 字符串资源
│   │       │   ├── colors.xml                                          # 颜色资源
│   │       │   ├── styles.xml                                          # 样式资源
│   │       │   ├── arrays.xml                                          # 数组资源
│   │       │   └── dimens.xml                                          # 尺寸资源
│   │       ├── values-zh-rCN/                                          # 简体中文资源
│   │       │   └── strings.xml                                         # 中文字符串
│   │       ├── values-zh-rTW/                                          # 繁体中文资源
│   │       │   └── strings.xml                                         # 繁体字符串
│   │       └── xml/                                                    # XML配置文件
│   │           ├── network_security_config.xml                         # 网络安全配置
│   │           ├── file_paths.xml                                      # 文件路径配置
│   │           └── backup_rules.xml                                    # 备份规则配置
│   ├── leanback/                                                   # TV版本源码集
│   │   ├── AndroidManifest.xml                                     # TV版清单文件
│   │   ├── java/                                                   # TV版Java源码
│   │   │   └── com/                                                # 包根目录
│   │   │       └── fongmi/                                         # 厂商包
│   │   │           └── android/                                    # Android包
│   │   │               └── tv/                                     # TV应用包
│   │   │                   ├── Product.java                        # TV版产品配置
│   │   │                   ├── Updater.java                        # TV版更新管理
│   │   │                   ├── bean/                               # TV版数据模型
│   │   │                   │   └── Func.java                       # TV版功能模型
│   │   │                   ├── event/                              # TV版事件
│   │   │                   │   └── EventIndex.java                 # TV版事件索引
│   │   │                   ├── receiver/                           # TV版接收器
│   │   │                   │   └── BootReceiver.java               # TV版启动接收器
│   │   │                   ├── ui/                                 # TV版用户界面
│   │   │                   │   ├── activity/                       # TV版Activity
│   │   │                   │   │   ├── HomeActivity.java           # TV版主界面
│   │   │                   │   │   ├── VodActivity.java            # TV版点播界面
│   │   │                   │   │   ├── VideoActivity.java          # TV版播放界面
│   │   │                   │   │   ├── LiveActivity.java           # TV版直播界面
│   │   │                   │   │   ├── SearchActivity.java         # TV版搜索界面
│   │   │                   │   │   ├── SettingActivity.java        # TV版设置界面
│   │   │                   │   │   ├── SettingPlayerActivity.java  # TV版播放器设置
│   │   │                   │   │   ├── CollectActivity.java        # TV版收藏界面
│   │   │                   │   │   ├── KeepActivity.java           # TV版追剧界面
│   │   │                   │   │   ├── PushActivity.java           # TV版推送界面
│   │   │                   │   │   ├── CastActivity.java           # TV版投屏界面
│   │   │                   │   │   └── FileActivity.java           # TV版文件管理
│   │   │                   │   ├── adapter/                        # TV版适配器
│   │   │                   │   │   ├── VodAdapter.java             # TV版视频适配器
│   │   │                   │   │   ├── EpisodeAdapter.java         # TV版剧集适配器
│   │   │                   │   │   ├── ParseAdapter.java           # TV版解析器适配器
│   │   │                   │   │   ├── QualityAdapter.java         # TV版清晰度适配器
│   │   │                   │   │   ├── TypeAdapter.java            # TV版类型适配器
│   │   │                   │   │   ├── SiteAdapter.java            # TV版站点适配器
│   │   │                   │   │   ├── ConfigAdapter.java          # TV版配置适配器
│   │   │                   │   │   ├── HistoryAdapter.java         # TV版历史适配器
│   │   │                   │   │   ├── KeepAdapter.java            # TV版收藏适配器
│   │   │                   │   │   ├── LiveAdapter.java            # TV版直播适配器
│   │   │                   │   │   ├── ChannelAdapter.java         # TV版频道适配器
│   │   │                   │   │   ├── GroupAdapter.java           # TV版分组适配器
│   │   │                   │   │   ├── EpgDataAdapter.java         # TV版节目适配器
│   │   │                   │   │   ├── FilterAdapter.java          # TV版筛选适配器
│   │   │                   │   │   ├── FlagAdapter.java            # TV版标识适配器
│   │   │                   │   │   ├── PartAdapter.java            # TV版部分适配器
│   │   │                   │   │   ├── TrackAdapter.java           # TV版轨道适配器
│   │   │                   │   │   ├── QuickAdapter.java           # TV版快捷适配器
│   │   │                   │   │   ├── RestoreAdapter.java         # TV版恢复适配器
│   │   │                   │   │   ├── SearchRecordAdapter.java    # TV版搜索记录适配器
│   │   │                   │   │   ├── SearchWordAdapter.java      # TV版搜索词适配器
│   │   │                   │   │   ├── FileAdapter.java            # TV版文件适配器
│   │   │                   │   │   ├── FuncAdapter.java            # TV版功能适配器
│   │   │                   │   │   ├── HeaderAdapter.java          # TV版头部适配器
│   │   │                   │   │   ├── ProgressAdapter.java        # TV版进度适配器
│   │   │                   │   │   ├── ArrayAdapter.java           # TV版数组适配器
│   │   │                   │   │   ├── DanmakuAdapter.java         # TV版弹幕适配器
│   │   │                   │   │   └── DohAdapter.java             # TV版DNS适配器
│   │   │                   │   ├── base/                           # TV版基础类
│   │   │                   │   │   ├── BaseActivity.java           # TV版基础Activity
│   │   │                   │   │   ├── BaseFragment.java           # TV版基础Fragment
│   │   │                   │   │   └── BaseDialog.java             # TV版基础对话框
│   │   │                   │   ├── custom/                         # TV版自定义组件
│   │   │                   │   │   ├── CustomTitleView.java        # TV版自定义标题
│   │   │                   │   │   ├── CustomKeyDownVod.java       # TV版自定义按键
│   │   │                   │   │   ├── CustomMovement.java         # TV版自定义移动
│   │   │                   │   │   ├── CustomScroller.java         # TV版自定义滚动
│   │   │                   │   │   ├── CustomSelector.java         # TV版自定义选择器
│   │   │                   │   │   ├── CustomRowPresenter.java     # TV版自定义行展示器
│   │   │                   │   │   ├── CustomVerticalGridView.java # TV版自定义垂直网格
│   │   │                   │   │   ├── CustomHorizontalGridView.java # TV版自定义水平网格
│   │   │                   │   │   ├── ProgressLayout.java         # TV版进度布局
│   │   │                   │   │   ├── SpaceItemDecoration.java    # TV版间距装饰
│   │   │                   │   │   └── PageAdapter.java            # TV版分页适配器
│   │   │                   │   ├── dialog/                         # TV版对话框
│   │   │                   │   │   ├── ConfigDialog.java           # TV版配置对话框
│   │   │                   │   │   ├── SiteDialog.java             # TV版站点对话框
│   │   │                   │   │   ├── HistoryDialog.java          # TV版历史对话框
│   │   │                   │   │   ├── RestoreDialog.java          # TV版恢复对话框
│   │   │                   │   │   ├── UpdateDialog.java           # TV版更新对话框
│   │   │                   │   │   ├── PassDialog.java             # TV版密码对话框
│   │   │                   │   │   ├── LiveDialog.java             # TV版直播对话框
│   │   │                   │   │   ├── TrackDialog.java            # TV版轨道对话框
│   │   │                   │   │   ├── SubtitleDialog.java         # TV版字幕对话框
│   │   │                   │   │   ├── DanmakuDialog.java          # TV版弹幕对话框
│   │   │                   │   │   ├── BufferDialog.java           # TV版缓冲对话框
│   │   │                   │   │   ├── SpeedDialog.java            # TV版速度对话框
│   │   │                   │   │   ├── ProxyDialog.java            # TV版代理对话框
│   │   │                   │   │   ├── DohDialog.java              # TV版DNS对话框
│   │   │                   │   │   ├── UaDialog.java               # TV版UA对话框
│   │   │                   │   │   └── DescDialog.java             # TV版描述对话框
│   │   │                   │   ├── fragment/                       # TV版Fragment
│   │   │                   │   │   └── VodFragment.java            # TV版点播Fragment
│   │   │                   │   ├── holder/                         # TV版ViewHolder
│   │   │                   │   │   └── ...                         # 各种Holder类
│   │   │                   │   └── presenter/                      # TV版Presenter
│   │   │                   │       └── ...                         # 各种Presenter类
│   │   │                   └── utils/                              # TV版工具类
│   │   │                       ├── KeyUtil.java                    # TV版按键工具
│   │   │                       └── QRCode.java                     # TV版二维码工具
│   │   └── res/                                                    # TV版资源文件
│   │       ├── anim/                                               # TV版动画
│   │       │   ├── cycles.xml                                      # 循环动画
│   │       │   ├── flicker.xml                                     # 闪烁动画
│   │       │   └── shake.xml                                       # 震动动画
│   │       ├── color/                                              # TV版颜色
│   │       │   ├── channel.xml                                     # 频道颜色
│   │       │   ├── epg.xml                                         # 节目颜色
│   │       │   └── group.xml                                       # 分组颜色
│   │       ├── drawable/                                           # TV版图标
│   │       │   └── ...                                             # 各种图标文件
│   │       ├── drawable-hdpi/                                      # 高密度图标
│   │       │   └── ...                                             # 主页功能图标
│   │       ├── drawable-xhdpi/                                     # 超高密度图标
│   │       │   └── ...                                             # 同上hdpi图标
│   │       ├── drawable-xxhdpi/                                    # 超超高密度图标
│   │       │   └── ...                                             # 同上hdpi图标
│   │       ├── drawable-nodpi/                                     # 无密度图标
│   │       │   ├── wallpaper_1.webp                                # 壁纸1
│   │       │   ├── wallpaper_2.webp                                # 壁纸2
│   │       │   ├── wallpaper_3.webp                                # 壁纸3
│   │       │   └── wallpaper_4.webp                                # 壁纸4
│   │       ├── layout/                                             # TV版布局
│   │       │   ├── activity_home.xml                               # TV版主界面布局
│   │       │   ├── activity_vod.xml                                # TV版点播布局
│   │       │   ├── activity_video.xml                              # TV版播放布局
│   │       │   ├── activity_live.xml                               # TV版直播布局
│   │       │   ├── activity_search.xml                             # TV版搜索布局
│   │       │   ├── activity_setting.xml                            # TV版设置布局
│   │       │   ├── activity_setting_player.xml                     # TV版播放器设置布局
│   │       │   ├── activity_collect.xml                            # TV版收藏布局
│   │       │   ├── activity_keep.xml                               # TV版追剧布局
│   │       │   ├── activity_push.xml                               # TV版推送布局
│   │       │   ├── activity_cast.xml                               # TV版投屏布局
│   │       │   ├── activity_file.xml                               # TV版文件管理布局
│   │       │   ├── fragment_vod.xml                                # TV版点播Fragment布局
│   │       │   ├── adapter_vod.xml                                 # TV版视频项布局
│   │       │   ├── adapter_vod_list.xml                            # TV版视频列表布局
│   │       │   ├── adapter_vod_oval.xml                            # TV版视频椭圆布局
│   │       │   ├── adapter_vod_rect.xml                            # TV版视频矩形布局
│   │       │   ├── adapter_episode.xml                             # TV版剧集布局
│   │       │   ├── adapter_parse.xml                               # TV版解析器布局
│   │       │   ├── adapter_quality.xml                             # TV版清晰度布局
│   │       │   ├── adapter_type.xml                                # TV版类型布局
│   │       │   ├── adapter_site.xml                                # TV版站点布局
│   │       │   ├── adapter_config.xml                              # TV版配置布局
│   │       │   ├── adapter_history.xml                             # TV版历史布局
│   │       │   ├── adapter_keep.xml                                # TV版收藏布局
│   │       │   ├── adapter_live.xml                                # TV版直播布局
│   │       │   ├── adapter_channel.xml                             # TV版频道布局
│   │       │   ├── adapter_group.xml                               # TV版分组布局
│   │       │   ├── adapter_epg_data.xml                            # TV版节目布局
│   │       │   ├── adapter_filter.xml                              # TV版筛选布局
│   │       │   ├── adapter_flag.xml                                # TV版标识布局
│   │       │   ├── adapter_part.xml                                # TV版部分布局
│   │       │   ├── adapter_track.xml                               # TV版轨道布局
│   │       │   ├── adapter_quick.xml                               # TV版快捷布局
│   │       │   ├── adapter_restore.xml                             # TV版恢复布局
│   │       │   ├── adapter_search_record.xml                       # TV版搜索记录布局
│   │       │   ├── adapter_search_word.xml                         # TV版搜索词布局
│   │       │   ├── adapter_file.xml                                # TV版文件布局
│   │       │   ├── adapter_func.xml                                # TV版功能布局
│   │       │   ├── adapter_header.xml                              # TV版头部布局
│   │       │   ├── adapter_progress.xml                            # TV版进度布局
│   │       │   ├── adapter_array.xml                               # TV版数组布局
│   │       │   ├── adapter_danmaku.xml                             # TV版弹幕布局
│   │       │   ├── adapter_doh.xml                                 # TV版DNS布局
│   │       │   ├── dialog_config.xml                               # TV版配置对话框布局
│   │       │   ├── dialog_site.xml                                 # TV版站点对话框布局
│   │       │   ├── dialog_history.xml                              # TV版历史对话框布局
│   │       │   ├── dialog_restore.xml                              # TV版恢复对话框布局
│   │       │   ├── dialog_update.xml                               # TV版更新对话框布局
│   │       │   ├── dialog_pass.xml                                 # TV版密码对话框布局
│   │       │   ├── dialog_live.xml                                 # TV版直播对话框布局
│   │       │   ├── dialog_track.xml                                # TV版轨道对话框布局
│   │       │   ├── dialog_subtitle.xml                             # TV版字幕对话框布局
│   │       │   ├── dialog_danmaku.xml                              # TV版弹幕对话框布局
│   │       │   ├── dialog_buffer.xml                               # TV版缓冲对话框布局
│   │       │   ├── dialog_speed.xml                                # TV版速度对话框布局
│   │       │   ├── dialog_proxy.xml                                # TV版代理对话框布局
│   │       │   ├── dialog_doh.xml                                  # TV版DNS对话框布局
│   │       │   ├── dialog_ua.xml                                   # TV版UA对话框布局
│   │       │   ├── dialog_desc.xml                                 # TV版描述对话框布局
│   │       │   ├── view_control_cast.xml                           # TV版投屏控制布局
│   │       │   ├── view_control_live.xml                           # TV版直播控制布局
│   │       │   ├── view_control_seek.xml                           # TV版拖拽控制布局
│   │       │   ├── view_control_vod.xml                            # TV版点播控制布局
│   │       │   ├── view_widget_cast.xml                            # TV版投屏控件布局
│   │       │   ├── view_widget_live.xml                            # TV版直播控件布局
│   │       │   ├── view_widget_vod.xml                             # TV版点播控件布局
│   │       │   └── view_progress.xml                               # TV版进度布局
│   │       ├── values/                                             # TV版默认资源值
│   │       │   ├── strings.xml                                     # TV版字符串资源
│   │       │   ├── colors.xml                                      # TV版颜色资源
│   │       │   └── styles.xml                                      # TV版样式资源
│   │       ├── values-v27/                                         # TV版API27+资源
│   │       │   └── styles.xml                                      # TV版API27+样式
│   │       ├── values-zh-rCN/                                      # TV版简体中文资源
│   │       │   └── strings.xml                                     # TV版中文字符串
│   │       └── values-zh-rTW/                                      # TV版繁体中文资源
│   │           └── strings.xml                                     # TV版繁体字符串
│   └── mobile/                                                     # 手机版本源码集
│       ├── AndroidManifest.xml                                     # 手机版清单文件
│       ├── java/                                                   # 手机版Java源码
│       │   └── com/                                                # 包根目录
│       │       └── fongmi/                                         # 厂商包
│       │           └── android/                                    # Android包
│       │               └── tv/                                     # TV应用包
│       │                   └── ui/                                 # 手机版用户界面
│       │                       └── activity/                       # 手机版Activity
│       │                           ├── HomeActivity.java           # 手机版主界面
│       │                           ├── VideoActivity.java          # 手机版播放界面
│       │                           ├── LiveActivity.java           # 手机版直播界面
│       │                           ├── CollectActivity.java        # 手机版收藏界面
│       │                           ├── KeepActivity.java           # 手机版追剧界面
│       │                           ├── HistoryActivity.java        # 手机版历史界面
│       │                           ├── FileActivity.java           # 手机版文件管理
│       │                           ├── FolderActivity.java         # 手机版文件夹界面
│       │                           └── ScanActivity.java           # 手机版扫码界面
│       └── res/                                                    # 手机版资源文件
│           ├── color/                                              # 手机版颜色
│           │   ├── channel.xml                                     # 频道颜色
│           │   ├── control.xml                                     # 控制颜色
│           │   ├── epg.xml                                         # 节目颜色
│           │   ├── group.xml                                       # 分组颜色
│           │   └── nav.xml                                         # 导航颜色
│           ├── color-night/                                        # 手机版夜间颜色
│           │   └── nav.xml                                         # 夜间导航颜色
│           ├── drawable/                                           # 手机版图标
│           │   ├── ic_action_choose.xml                            # 选择图标
│           │   ├── ic_action_delete.xml                            # 删除图标
│           │   ├── ic_action_grid.xml                              # 网格图标
│           │   ├── ic_action_history.xml                           # 历史图标
│           │   ├── ic_action_keep.xml                              # 收藏图标
│           │   ├── ic_action_list.xml                              # 列表图标
│           │   ├── ic_action_refresh.xml                           # 刷新图标
│           │   ├── ic_action_retry.xml                             # 重试图标
│           │   ├── ic_action_scan.xml                              # 扫描图标
│           │   ├── ic_action_search.xml                            # 搜索图标
│           │   ├── ic_action_site.xml                              # 站点图标
│           │   ├── ic_action_subtitle.xml                          # 字幕图标
│           │   ├── ic_action_sync.xml                              # 同步图标
│           │   ├── ic_cast_mobile.xml                              # 手机投屏图标
│           │   ├── ic_cast_tv.xml                                  # TV投屏图标
│           │   ├── ic_control_back.xml                             # 控制返回图标
│           │   ├── ic_control_cast.xml                             # 控制投屏图标
│           │   ├── ic_control_danmaku_off.xml                      # 弹幕关闭图标
│           │   ├── ic_control_danmaku_on.xml                       # 弹幕开启图标
│           │   ├── ic_control_full.xml                             # 全屏图标
│           │   ├── ic_control_info.xml                             # 信息图标
│           │   ├── ic_control_keep_off.xml                         # 收藏关闭图标
│           │   ├── ic_control_keep_on.xml                          # 收藏开启图标
│           │   ├── ic_control_lock_off.xml                         # 锁定关闭图标
│           │   ├── ic_control_lock_on.xml                          # 锁定开启图标
│           │   ├── ic_control_rotate.xml                           # 旋转图标
│           │   ├── ic_control_setting.xml                          # 设置图标
│           │   ├── ic_detail_more.xml                              # 详情更多图标
│           │   ├── ic_detail_reverse.xml                           # 详情反向图标
│           │   ├── ic_fab_filter.xml                               # 浮动筛选图标
│           │   ├── ic_fab_link.xml                                 # 浮动链接图标
│           │   ├── ic_fab_top.xml                                  # 浮动顶部图标
│           │   ├── ic_live_block.xml                               # 直播屏蔽图标
│           │   ├── ic_live_boot.xml                                # 直播启动图标
│           │   ├── ic_live_pass.xml                                # 直播通过图标
│           │   ├── ic_nav_live.xml                                 # 导航直播图标
│           │   ├── ic_nav_setting.xml                              # 导航设置图标
│           │   ├── ic_nav_vod.xml                                  # 导航点播图标
│           │   ├── ic_setting_delete.xml                           # 设置删除图标
│           │   ├── ic_site_block.xml                               # 站点屏蔽图标
│           │   ├── ic_site_change.xml                              # 站点切换图标
│           │   ├── ic_site_search.xml                              # 站点搜索图标
│           │   ├── ic_subtitle_down.xml                            # 字幕下移图标
│           │   ├── ic_subtitle_large.xml                           # 字幕放大图标
│           │   ├── ic_subtitle_reset.xml                           # 字幕重置图标
│           │   ├── ic_subtitle_small.xml                           # 字幕缩小图标
│           │   ├── ic_subtitle_up.xml                              # 字幕上移图标
│           │   ├── ic_sync_download.xml                            # 同步下载图标
│           │   ├── ic_sync_two.xml                                 # 双向同步图标
│           │   ├── ic_sync_upload.xml                              # 同步上传图标
│           │   ├── ic_widget_bright_high.xml                       # 高亮度图标
│           │   ├── ic_widget_bright_low.xml                        # 低亮度图标
│           │   ├── ic_widget_bright_medium.xml                     # 中亮度图标
│           │   ├── ic_widget_volume_high.xml                       # 高音量图标
│           │   ├── ic_widget_volume_low.xml                        # 低音量图标
│           │   ├── ic_widget_volume_medium.xml                     # 中音量图标
│           │   └── ...                                             # 其他图标文件
│           ├── drawable-nodpi/                                     # 手机版无密度图标
│           │   ├── wallpaper_1.webp                                # 壁纸1
│           │   ├── wallpaper_2.webp                                # 壁纸2
│           │   ├── wallpaper_3.webp                                # 壁纸3
│           │   └── wallpaper_4.webp                                # 壁纸4
│           ├── layout/                                             # 手机版布局
│           │   ├── activity_home.xml                               # 手机版主界面布局
│           │   ├── activity_video.xml                              # 手机版播放布局
│           │   ├── activity_live.xml                               # 手机版直播布局
│           │   ├── activity_collect.xml                            # 手机版收藏布局
│           │   ├── activity_keep.xml                               # 手机版追剧布局
│           │   ├── activity_history.xml                            # 手机版历史布局
│           │   ├── activity_file.xml                               # 手机版文件管理布局
│           │   ├── activity_folder.xml                             # 手机版文件夹布局
│           │   ├── activity_scan.xml                               # 手机版扫码布局
│           │   ├── fragment_vod.xml                                # 手机版点播Fragment布局
│           │   ├── fragment_setting.xml                            # 手机版设置Fragment布局
│           │   ├── fragment_setting_player.xml                     # 手机版播放器设置Fragment布局
│           │   ├── fragment_type.xml                               # 手机版类型Fragment布局
│           │   ├── fragment_episode.xml                            # 手机版剧集Fragment布局
│           │   └── ...                                             # 其他布局文件
│           ├── layout-sw600dp/                                     # 手机版平板布局
│           │   ├── activity_video.xml                              # 平板播放布局
│           │   └── view_control_right.xml                          # 平板右侧控制布局
│           ├── menu/                                               # 手机版菜单
│           │   └── menu_nav.xml                                    # 导航菜单
│           ├── values/                                             # 手机版默认资源值
│           │   ├── arrays.xml                                      # 数组资源
│           │   ├── colors.xml                                      # 颜色资源
│           │   ├── strings.xml                                     # 字符串资源
│           │   └── styles.xml                                      # 样式资源
│           ├── values-night/                                       # 手机版夜间资源
│           │   └── colors.xml                                      # 夜间颜色
│           ├── values-v27/                                         # 手机版API27+资源
│           │   └── styles.xml                                      # API27+样式
│           ├── values-zh-rCN/                                      # 手机版简体中文资源
│           │   └── strings.xml                                     # 中文字符串
│           └── values-zh-rTW/                                      # 手机版繁体中文资源
│               └── strings.xml                                     # 繁体字符串
├── catvod/                                                         # 爬虫核心引擎子模块
│   ├── build.gradle.kts                                            # catvod构建配置
│   ├── consumer-rules.pro                                          # catvod消费者规则
│   ├── src/                                                        # catvod源码
│   │   └── main/                                                   # catvod主源码
│   │       ├── AndroidManifest.xml                                 # catvod清单文件
│   │       └── java/                                               # catvod Java源码
│   │           └── com/                                            # 包根目录
│   │               └── github/                                     # GitHub包
│   │                   └── catvod/                                 # CatVod包
│   │                       └── crawler/                            # 爬虫包
│   │                           ├── Spider.java                     # 爬虫接口
│   │                           ├── SpiderDebug.java                # 爬虫调试
│   │                           ├── SpiderNull.java                 # 空爬虫
│   │                           └── JsonParallel.java               # 并行JSON解析
│   └── build/                                                      # catvod构建目录
├── chaquo/                                                         # Python引擎子模块
│   ├── build.gradle.kts                                            # chaquo构建配置
│   ├── consumer-rules.pro                                          # chaquo消费者规则
│   ├── requirements.txt                                            # Python依赖配置
│   ├── src/                                                        # chaquo源码
│   │   └── main/                                                   # chaquo主源码
│   │       ├── AndroidManifest.xml                                 # chaquo清单文件
│   │       ├── java/                                               # chaquo Java源码
│   │       │   └── com/                                            # 包根目录
│   │       │       └── fongmi/                                     # 厂商包
│   │       │           └── chaquo/                                 # Chaquo包
│   │       │               └── Python.java                        # Python接口
│   │       └── python/                                             # chaquo Python源码
│   │           ├── main.py                                         # Python主入口
│   │           ├── spider/                                         # Python爬虫模块
│   │           │   ├── __init__.py                                 # 模块初始化
│   │           │   ├── base.py                                     # 基础爬虫类
│   │           │   ├── parser.py                                   # 解析器
│   │           │   └── utils.py                                    # 工具函数
│   │           └── lib/                                            # Python库
│   │               ├── requests.py                                 # HTTP请求库
│   │               ├── bs4.py                                      # BeautifulSoup解析库
│   │               ├── json.py                                     # JSON处理库
│   │               ├── re.py                                       # 正则表达式库
│   │               ├── base64.py                                   # Base64编码库
│   │               ├── urllib.py                                   # URL处理库
│   │               ├── hashlib.py                                  # 哈希库
│   │               └── time.py                                     # 时间处理库
│   └── build/                                                      # chaquo构建目录
├── quickjs/                                                        # JavaScript引擎子模块
│   ├── build.gradle.kts                                            # quickjs构建配置
│   ├── consumer-rules.pro                                          # quickjs消费者规则
│   ├── src/                                                        # quickjs源码
│   │   └── main/                                                   # quickjs主源码
│   │       ├── AndroidManifest.xml                                 # quickjs清单文件
│   │       ├── cpp/                                                # quickjs C++源码
│   │       │   ├── quickjs.cpp                                     # QuickJS主文件
│   │       │   ├── quickjs.h                                       # QuickJS头文件
│   │       │   ├── quickjs-libc.c                                  # QuickJS库文件
│   │       │   ├── quickjs-libc.h                                  # QuickJS库头文件
│   │       │   ├── libunicode.c                                    # Unicode库
│   │       │   ├── libunicode.h                                    # Unicode库头文件
│   │       │   ├── libregexp.c                                     # 正则表达式库
│   │       │   ├── libregexp.h                                     # 正则表达式库头文件
│   │       │   ├── cutils.c                                        # C工具库
│   │       │   ├── cutils.h                                        # C工具库头文件
│   │       │   └── CMakeLists.txt                                  # CMake配置
│   │       └── java/                                               # quickjs Java源码
│   │           └── com/                                            # 包根目录
│   │               └── fongmi/                                     # 厂商包
│   │                   └── quickjs/                                # QuickJS包
│   │                       ├── QuickJS.java                        # QuickJS主类
│   │                       ├── JSEngine.java                       # JS引擎
│   │                       ├── JSFunction.java                     # JS函数
│   │                       ├── JSObject.java                       # JS对象
│   │                       ├── JSArray.java                        # JS数组
│   │                       ├── JSValue.java                        # JS值
│   │                       ├── JSException.java                    # JS异常
│   │                       ├── JSContext.java                      # JS上下文
│   │                       ├── JSRuntime.java                      # JS运行时
│   │                       └── utils/                              # QuickJS工具
│   │                           ├── Module.java                     # 模块加载器
│   │                           ├── Asset.java                      # 资产工具
│   │                           └── Console.java                    # 控制台工具
│   └── build/                                                      # quickjs构建目录
├── hook/                                                           # Hook机制子模块
│   ├── build.gradle.kts                                            # hook构建配置
│   ├── consumer-rules.pro                                          # hook消费者规则
│   ├── src/                                                        # hook源码
│   │   └── main/                                                   # hook主源码
│   │       ├── AndroidManifest.xml                                 # hook清单文件
│   │       └── java/                                               # hook Java源码
│   │           └── com/                                            # 包根目录
│   │               └── github/                                     # GitHub包
│   │                   └── tvbox/                                  # TVBox包
│   │                       └── osc/                                # OSC包
│   │                           └── hook/                           # Hook包
│   │                               ├── HookLoader.java             # Hook加载器
│   │                               ├── HookCallback.java           # Hook回调
│   │                               ├── HookMethod.java             # Hook方法
│   │                               └── HookUtils.java              # Hook工具
│   └── build/                                                      # hook构建目录
├── forcetech/                                                      # Forcetech解析器子模块
│   ├── build.gradle.kts                                            # forcetech构建配置
│   ├── consumer-rules.pro                                          # forcetech消费者规则
│   ├── src/                                                        # forcetech源码
│   │   └── main/                                                   # forcetech主源码
│   │       ├── AndroidManifest.xml                                 # forcetech清单文件
│   │       └── java/                                               # forcetech Java源码
│   │           └── com/                                            # 包根目录
│   │               └── forcetech/                                  # Forcetech包
│   │                   └── android/                                # Android包
│   │                       ├── ForceParser.java                    # Force解析器
│   │                       ├── ForceUtils.java                     # Force工具
│   │                       └── ForceConfig.java                    # Force配置
│   └── build/                                                      # forcetech构建目录
├── jianpian/                                                       # 简片解析器子模块
│   ├── build.gradle.kts                                            # jianpian构建配置
│   ├── consumer-rules.pro                                          # jianpian消费者规则
│   ├── src/                                                        # jianpian源码
│   │   └── main/                                                   # jianpian主源码
│   │       ├── AndroidManifest.xml                                 # jianpian清单文件
│   │       └── java/                                               # jianpian Java源码
│   │           └── com/                                            # 包根目录
│   │               └── jianpian/                                   # 简片包
│   │                   └── android/                                # Android包
│   │                       ├── JianParser.java                     # 简片解析器
│   │                       ├── JianUtils.java                      # 简片工具
│   │                       └── JianConfig.java                     # 简片配置
│   └── build/                                                      # jianpian构建目录
├── thunder/                                                        # 迅雷解析器子模块
│   ├── build.gradle.kts                                            # thunder构建配置
│   ├── consumer-rules.pro                                          # thunder消费者规则
│   ├── src/                                                        # thunder源码
│   │   └── main/                                                   # thunder主源码
│   │       ├── AndroidManifest.xml                                 # thunder清单文件
│   │       └── java/                                               # thunder Java源码
│   │           └── com/                                            # 包根目录
│   │               └── thunder/                                    # 迅雷包
│   │                   └── android/                                # Android包
│   │                       ├── ThunderParser.java                  # 迅雷解析器
│   │                       ├── ThunderUtils.java                   # 迅雷工具
│   │                       └── ThunderConfig.java                  # 迅雷配置
│   └── build/                                                      # thunder构建目录
├── tvbus/                                                          # TVBus直播解析器子模块
│   ├── build.gradle.kts                                            # tvbus构建配置
│   ├── consumer-rules.pro                                          # tvbus消费者规则
│   ├── src/                                                        # tvbus源码
│   │   └── main/                                                   # tvbus主源码
│   │       ├── AndroidManifest.xml                                 # tvbus清单文件
│   │       └── java/                                               # tvbus Java源码
│   │           └── com/                                            # 包根目录
│   │               └── tvbus/                                      # TVBus包
│   │                   └── android/                                # Android包
│   │                       ├── TvbusParser.java                    # TVBus解析器
│   │                       ├── TvbusUtils.java                     # TVBus工具
│   │                       └── TvbusConfig.java                    # TVBus配置
│   └── build/                                                      # tvbus构建目录
└── other/                                                          # 其他资源文件
    ├── image/                                                      # 图片资源
    │   ├── logo.png                                                # Logo图片
    │   ├── banner.png                                              # 横幅图片
    │   └── icon.png                                                # 图标图片
    ├── sample/                                                     # 示例文件
    │   ├── config.json                                             # 示例配置
    │   ├── live.m3u                                                # 示例直播源
    │   └── vod.json                                                # 示例点播源
    └── tools/                                                      # 工具文件
        ├── build.sh                                                # 构建脚本
        ├── deploy.sh                                               # 部署脚本
        └── test.sh                                                 # 测试脚本
```

## 文件统计

- **总文件数**: 约 2000+ 个文件
- **Java源码文件**: 约 500+ 个
- **资源文件**: 约 800+ 个
- **布局文件**: 约 200+ 个
- **配置文件**: 约 50+ 个
- **子模块**: 8 个独立子模块
- **数据库架构**: 100 个版本文件

## 模块特点

1. **完整的TVBOX兼容性**: 支持所有标准TVBOX功能
2. **多平台支持**: TV版本(leanback)和手机版本(mobile)
3. **多引擎支持**: Java、Python、JavaScript解析引擎
4. **模块化设计**: 清晰的子模块划分
5. **丰富的功能**: 点播、直播、搜索、收藏、投屏等
6. **完善的架构**: MVP架构，清晰的分层设计

---

*此文档展示了onevod模块的完整文件树结构，包含所有源码、资源和配置文件。*
*生成时间: 2025-07-14*
*文档版本: 1.0*
