#!/bin/bash

# OneTV点播系统 - vod-config Edge Function部署脚本
# 用于部署和验证vod-config Edge Function

echo "🚀 开始部署OneTV vod-config Edge Function..."

# 检查Supabase CLI是否安装
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI未安装，请先安装: npm install -g supabase"
    exit 1
fi

# 检查是否已登录
if ! supabase projects list &> /dev/null; then
    echo "❌ 请先登录Supabase: supabase login"
    exit 1
fi

# 检查项目是否已链接
if [ ! -f ".supabase/config.toml" ]; then
    echo "❌ 项目未链接，请先运行: supabase link --project-ref YOUR_PROJECT_REF"
    exit 1
fi

echo "📋 当前项目信息:"
supabase status

echo ""
echo "📦 部署vod-config Edge Function..."

# 部署Edge Function
supabase functions deploy vod-config

if [ $? -eq 0 ]; then
    echo "✅ vod-config Edge Function部署成功!"
else
    echo "❌ vod-config Edge Function部署失败!"
    exit 1
fi

echo ""
echo "🔧 检查环境变量..."

# 获取项目信息
PROJECT_REF=$(grep 'project_id' .supabase/config.toml | cut -d'"' -f2)
SUPABASE_URL="https://${PROJECT_REF}.supabase.co"

echo "📊 项目配置:"
echo "   Project Ref: $PROJECT_REF"
echo "   Supabase URL: $SUPABASE_URL"

echo ""
echo "🧪 测试Edge Function..."

# 测试Edge Function
echo "📡 发送测试请求到Edge Function..."
curl -X GET \
  "${SUPABASE_URL}/functions/v1/vod-config" \
  -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY" \
  -H "apikey: YOUR_SERVICE_ROLE_KEY" \
  -H "Content-Type: application/json" \
  --max-time 30 \
  --verbose

echo ""
echo "📋 部署完成检查清单:"
echo "✅ Edge Function已部署"
echo "🔧 请确保以下环境变量已设置:"
echo "   - SUPABASE_URL (自动设置)"
echo "   - SUPABASE_SERVICE_ROLE_KEY (需要手动设置)"
echo ""
echo "🔗 Edge Function URL: ${SUPABASE_URL}/functions/v1/vod-config"
echo ""
echo "📝 下一步:"
echo "1. 在Supabase Dashboard中设置SUPABASE_SERVICE_ROLE_KEY环境变量"
echo "2. 确保vod-sources存储桶存在且包含onetv-api-movie.json文件"
echo "3. 测试OneTV应用的配置获取功能"
