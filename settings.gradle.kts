pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.onetv.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
                // 允许 AGP 插件及其所有依赖项下载
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.android\\.tools.*")
                // 添加对 signflinger 和 zipflinger 的支持
                includeGroup("com.android")
                // 添加Chaquo Python插件支持
                includeGroup("com.chaquo.python")
            }
        }
        mavenCentral()
        gradlePluginPortal()
        // Chaquo Python插件仓库
        maven { url = uri("https://chaquo.com/maven") }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        // 移除Google仓库内容限制，确保Chaquo Python依赖可以下载
        google()
        mavenCentral()
        jcenter() // FongMi_TV需要
        maven { url = uri("https://jitpack.io") }
        // Chaquo Python插件仓库
        maven { url = uri("https://chaquo.com/maven") }
        maven {
            url = uri("https://s01.oss.sonatype.org/content/repositories/releases")
        }
        maven { url = uri("https://s01.oss.sonatype.org/content/repositories/snapshots") }
        maven { url = uri("https://maven.pkg.jetbrains.space/public/p/compose/dev") }
        maven { url = uri("https://maven.pkg.jetbrains.space/kotlin/p/kotlin/dev") }
        // FongMi_TV特定仓库
        maven {
            url = uri("http://4thline.org/m2")
            isAllowInsecureProtocol = true
        }

        flatDir { dirs("tv/libs") } // TV模块AAR文件支持
    }
}

rootProject.name = "壹来电视"
println("[OneTV-Build] 根项目名称: 壹来电视")

include(":core:data")
include(":core:util")
include(":core:designsystem")

// 恢复正确的构建顺序：TV主应用模块优先
println("[OneTV-Build] 包含TV模块 (主应用)")
include(":tv")
println("[OneTV-Build] :tv -> android.application")
include(":mobile")
include(":movie")
// include(":film") // 暂时移除film模块避免配置冲突


