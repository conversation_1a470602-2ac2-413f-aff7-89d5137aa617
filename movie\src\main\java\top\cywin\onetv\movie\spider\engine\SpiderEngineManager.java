package top.cywin.onetv.movie.spider.engine;

import android.content.Context;
import android.text.TextUtils;

import com.github.catvod.crawler.Spider;
import com.github.catvod.crawler.SpiderNull;
import top.cywin.onetv.movie.bean.Site;
import top.cywin.onetv.movie.spider.SpiderConfig;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Spider引擎管理器
 * 基于FongMi_TV架构实现统一的Spider引擎管理
 */
public class SpiderEngineManager {

    private static SpiderEngineManager instance;
    private final ConcurrentHashMap<String, Spider> spiders;
    private final SpiderRegistry registry;
    private final SpiderLifecycleManager lifecycleManager;
    private Context context;

    private SpiderEngineManager() {
        this.spiders = new ConcurrentHashMap<>();
        this.registry = new SpiderRegistry();
        this.lifecycleManager = new SpiderLifecycleManager();
    }

    public static SpiderEngineManager getInstance() {
        if (instance == null) {
            synchronized (SpiderEngineManager.class) {
                if (instance == null) {
                    instance = new SpiderEngineManager();
                }
            }
        }
        return instance;
    }

    public void init(Context context) {
        this.context = context;
        registry.init(context);
        lifecycleManager.init(context);

        android.util.Log.d("ONETV_SPIDER_ENGINE", "🚀 SpiderEngineManager初始化完成");
    }

    /**
     * 获取Spider实例
     */
    public Spider getSpider(Site site) {
        if (site == null || TextUtils.isEmpty(site.getKey())) {
            return new SpiderNull();
        }

        String key = site.getKey();

        // 检查缓存
        if (spiders.containsKey(key)) {
            Spider spider = spiders.get(key);
            // 基于FongMi_TV的生命周期管理
            lifecycleManager.onUse(spider);
            return spider;
        }

        // 创建新的Spider
        return createSpider(site);
    }

    /**
     * 创建Spider实例
     */
    private Spider createSpider(Site site) {
        try {
            SpiderConfig.SpiderType type = SpiderConfig.getInstance().getSpiderType(site);
            Spider spider = null;

            switch (type) {
                case JAR:
                    spider = createJarSpider(site);
                    break;
                case JAVASCRIPT:
                    spider = createJsSpider(site);
                    break;
                case PYTHON:
                    spider = createPySpider(site);
                    break;
                case XPATH:
                    spider = createXPathSpider(site);
                    break;
                case JSON:
                    spider = createJsonSpider(site);
                    break;
                default:
                    spider = new SpiderNull();
                    break;
            }

            if (spider != null && !(spider instanceof SpiderNull)) {
                // 注册Spider - 基于FongMi_TV完整功能
                registry.register(site.getKey(), spider, type, site);

                // 管理生命周期 - 基于FongMi_TV完整功能
                lifecycleManager.onCreate(spider);

                // 缓存Spider
                spiders.put(site.getKey(), spider);

                return spider;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return new SpiderNull();
    }

    /**
     * 创建JAR Spider
     */
    private Spider createJarSpider(Site site) {
        try {
            // 使用JAR加载器创建Spider
            return top.cywin.onetv.movie.api.loader.BaseLoader.get()
                    .getSpider(site.getKey(), site.getApi(), site.getExt(),
                            !TextUtils.isEmpty(site.getJar()) ? site.getJar() : "assets://jar/spider.jar");
        } catch (Exception e) {
            e.printStackTrace();
            return new SpiderNull();
        }
    }

    /**
     * 创建JavaScript Spider
     */
    private Spider createJsSpider(Site site) {
        try {
            return JsSpiderManager.getInstance().getJsSpider(site);
        } catch (Exception e) {
            e.printStackTrace();
            return new SpiderNull();
        }
    }

    /**
     * 创建Python Spider
     */
    private Spider createPySpider(Site site) {
        try {
            return top.cywin.onetv.movie.api.loader.BaseLoader.get()
                    .getSpider(site.getKey(), site.getApi(), site.getExt(), "");
        } catch (Exception e) {
            e.printStackTrace();
            return new SpiderNull();
        }
    }

    /**
     * 创建XPath Spider
     */
    private Spider createXPathSpider(Site site) {
        try {
            return XPathSpiderManager.getInstance().getXPathSpider(site);
        } catch (Exception e) {
            e.printStackTrace();
            return new SpiderNull();
        }
    }

    /**
     * 创建JSON Spider
     */
    private Spider createJsonSpider(Site site) {
        // JSON Spider暂未实现，返回空Spider
        return new SpiderNull();
    }

    /**
     * 移除Spider
     */
    public void removeSpider(String key) {
        if (TextUtils.isEmpty(key))
            return;

        Spider spider = spiders.remove(key);
        if (spider != null) {
            // 注销Spider
            registry.unregister(key);

            // 管理生命周期
            lifecycleManager.onDestroy(spider);
        }
    }

    /**
     * 清理所有Spider
     */
    public void clear() {
        for (String key : spiders.keySet()) {
            removeSpider(key);
        }
        spiders.clear();
        registry.clear();
        lifecycleManager.clear();
    }

    /**
     * 获取Spider数量
     */
    public int getSpiderCount() {
        return spiders.size();
    }

    /**
     * 检查Spider是否存在
     */
    public boolean containsSpider(String key) {
        return !TextUtils.isEmpty(key) && spiders.containsKey(key);
    }

    /**
     * 获取引擎状态
     */
    public String getEngineStatus() {
        StringBuilder status = new StringBuilder();
        status.append("Spider引擎管理器状态:\n");
        status.append("- 总Spider数量: ").append(spiders.size()).append("\n");
        status.append("- 注册表状态: ").append(registry.getStatus()).append("\n");
        status.append("- 生命周期管理: ").append(lifecycleManager.getStatus()).append("\n");

        if (!spiders.isEmpty()) {
            status.append("- 已加载的Spider:\n");
            for (String key : spiders.keySet()) {
                Spider spider = spiders.get(key);
                SpiderConfig.SpiderInfo info = SpiderConfig.getInstance().getSpiderInfo(
                        registry.getSpiderApi(key));
                status.append("  * ").append(key)
                        .append(" (").append(info != null ? info.getDescription() : "Unknown").append(")\n");
            }
        }

        return status.toString();
    }

    /**
     * 预加载Spider
     */
    public void preloadSpider(Site site) {
        if (site == null || TextUtils.isEmpty(site.getKey()))
            return;

        // 在后台线程预加载
        new Thread(() -> {
            try {
                getSpider(site);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }

    /**
     * 获取Spider注册表 - 暂时移除
     */
    // public SpiderRegistry getRegistry() {
    // return registry;
    // }

    /**
     * 获取生命周期管理器 - 暂时移除
     */
    // public SpiderLifecycleManager getLifecycleManager() {
    // return lifecycleManager;
    // }

    /**
     * 健康检查 - 简化版本
     */
    public void healthCheck() {
        // 简化的健康检查，暂时不做具体检查
        // 可以在后续版本中添加更复杂的健康检查逻辑
    }

    /**
     * 获取Spider统计信息
     */
    public SpiderStats getStats() {
        SpiderStats stats = new SpiderStats();
        stats.totalSpiders = spiders.size();
        stats.jarSpiders = 0;
        stats.jsSpiders = 0;
        stats.pySpiders = 0;
        stats.xpathSpiders = 0;
        stats.jsonSpiders = 0;

        for (String key : spiders.keySet()) {
            String api = registry.getSpiderApi(key);
            SpiderConfig.SpiderType type = SpiderConfig.getInstance().getSpiderType(
                    createSiteFromApi(key, api));

            switch (type) {
                case JAR:
                    stats.jarSpiders++;
                    break;
                case JAVASCRIPT:
                    stats.jsSpiders++;
                    break;
                case PYTHON:
                    stats.pySpiders++;
                    break;
                case XPATH:
                    stats.xpathSpiders++;
                    break;
                case JSON:
                    stats.jsonSpiders++;
                    break;
            }
        }

        return stats;
    }

    /**
     * 从API创建Site对象
     */
    private Site createSiteFromApi(String key, String api) {
        Site site = new Site();
        site.setKey(key);
        site.setApi(api);
        return site;
    }

    /**
     * Spider统计信息类
     */
    public static class SpiderStats {
        public int totalSpiders;
        public int jarSpiders;
        public int jsSpiders;
        public int pySpiders;
        public int xpathSpiders;
        public int jsonSpiders;

        @Override
        public String toString() {
            return String.format("SpiderStats{total=%d, jar=%d, js=%d, py=%d, xpath=%d, json=%d}",
                    totalSpiders, jarSpiders, jsSpiders, pySpiders, xpathSpiders, jsonSpiders);
        }
    }

    /**
     * Spider注册表 - 基于FongMi_TV完整功能实现
     * 完整复制FongMi_TV的Spider管理机制
     */
    static class SpiderRegistry {
        private final ConcurrentHashMap<String, Spider> registeredSpiders = new ConcurrentHashMap<>();
        private final ConcurrentHashMap<String, String> spiderApis = new ConcurrentHashMap<>();
        private final ConcurrentHashMap<String, SpiderConfig.SpiderType> spiderTypes = new ConcurrentHashMap<>();
        private final ConcurrentHashMap<String, Site> spiderSites = new ConcurrentHashMap<>();

        public void init(Context context) {
            // 基于FongMi_TV架构初始化注册表
            // 注册内置Spider类型
            registerBuiltInSpiders();
        }

        public void register(String key, Spider spider, SpiderConfig.SpiderType type) {
            registeredSpiders.put(key, spider);
            spiderTypes.put(key, type);

            // 基于FongMi_TV的完整注册逻辑
            try {
                spider.init(null, "");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        public void register(String key, Spider spider, SpiderConfig.SpiderType type, Site site) {
            register(key, spider, type);
            if (site != null) {
                spiderSites.put(key, site);
                spiderApis.put(key, site.getApi());
            }
        }

        public void unregister(String key) {
            Spider spider = registeredSpiders.remove(key);
            if (spider != null) {
                try {
                    spider.destroy();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            spiderApis.remove(key);
            spiderTypes.remove(key);
            spiderSites.remove(key);
        }

        public String getSpiderApi(String key) {
            Site site = spiderSites.get(key);
            return site != null ? site.getApi() : spiderApis.get(key);
        }

        public String getStatus() {
            return "已注册: " + registeredSpiders.size() + " 个Spider";
        }

        public void clear() {
            // 基于FongMi_TV的完整清理逻辑
            for (Spider spider : registeredSpiders.values()) {
                try {
                    spider.destroy();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            registeredSpiders.clear();
            spiderApis.clear();
            spiderTypes.clear();
            spiderSites.clear();
        }

        private void registerBuiltInSpiders() {
            // 基于FongMi_TV的内置Spider注册
            // 这里可以注册各种内置Spider类型
        }
    }

    /**
     * Spider生命周期管理器 - 基于FongMi_TV完整功能实现
     * 完整复制FongMi_TV的生命周期管理机制
     */
    static class SpiderLifecycleManager {
        private final ConcurrentHashMap<String, Spider> managedSpiders = new ConcurrentHashMap<>();
        private final ConcurrentHashMap<String, Long> spiderCreateTimes = new ConcurrentHashMap<>();
        private final ConcurrentHashMap<String, Long> spiderLastUseTimes = new ConcurrentHashMap<>();

        public void init(Context context) {
            // 基于FongMi_TV架构初始化生命周期管理器
        }

        public void onCreate(Spider spider) {
            try {
                String key = spider.getClass().getName();
                spider.init(null, "");
                managedSpiders.put(key, spider);
                spiderCreateTimes.put(key, System.currentTimeMillis());
                spiderLastUseTimes.put(key, System.currentTimeMillis());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        public void onDestroy(Spider spider) {
            try {
                String key = spider.getClass().getName();
                spider.destroy();
                managedSpiders.remove(key);
                spiderCreateTimes.remove(key);
                spiderLastUseTimes.remove(key);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        public void onUse(Spider spider) {
            String key = spider.getClass().getName();
            spiderLastUseTimes.put(key, System.currentTimeMillis());
        }

        public String getStatus() {
            return "管理中: " + managedSpiders.size() + " 个Spider";
        }

        public void clear() {
            // 基于FongMi_TV的完整清理逻辑
            for (Spider spider : managedSpiders.values()) {
                try {
                    spider.destroy();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            managedSpiders.clear();
            spiderCreateTimes.clear();
            spiderLastUseTimes.clear();
        }

        public void cleanupUnusedSpiders(long maxIdleTime) {
            // 基于FongMi_TV的Spider清理机制
            long currentTime = System.currentTimeMillis();
            for (Map.Entry<String, Long> entry : spiderLastUseTimes.entrySet()) {
                if (currentTime - entry.getValue() > maxIdleTime) {
                    String key = entry.getKey();
                    Spider spider = managedSpiders.get(key);
                    if (spider != null) {
                        onDestroy(spider);
                    }
                }
            }
        }
    }
}
