# VOD模块资源前缀自动化集成方案

## 项目概述

### 背景
在Android多模块项目中，所有res目录下的资源（layout、drawable、menu、color、string、style、xml、anim、mipmap等）以及values/strings.xml、styles.xml等资源内的文件也需加前缀，并同步引用。在主应用和所有依赖库合并时，同名资源会被覆盖，导致UI、样式、图片、字符串等混乱。

### 目标
对VOD库模块所有资源文件自动添加前缀`vod_`，并且同步更新模块内所有引用，实现彻底资源隔离，避免与主应用冲突。

## VOD模块结构分析

### 主要子模块
```
vod/
├── src/main/          # 主模块资源
├── src/leanback/      # TV版本资源
├── src/mobile/        # 手机版本资源
├── catvod/            # CatVOD解析器
├── chaquo/            # Python解析器
├── quickjs/           # JavaScript解析器
├── hook/              # Hook模块
├── forcetech/         # 强制技术模块
├── jianpian/          # 简片模块
├── thunder/           # 迅雷模块
└── tvbus/             # TVBus模块
```

### 资源文件统计

#### 主模块 (src/main/res)
- **layout**: 3个文件
- **drawable**: 15个XML + 12个PNG图片
- **mipmap**: 15个PNG图片 + 2个XML
- **anim**: 1个文件
- **color**: 1个文件
- **values**: 4个文件 (strings.xml, styles.xml, colors.xml, attrs.xml)
- **values-zh-rCN**: 1个文件
- **values-zh-rTW**: 1个文件
- **xml**: 1个文件

#### Leanback模块 (src/leanback/res)
- **layout**: 约80个文件
- **drawable**: 约100个文件 (XML + PNG)
- **anim**: 3个文件
- **color**: 3个文件
- **values**: 3个文件
- **values-zh-rCN**: 1个文件
- **values-zh-rTW**: 1个文件

#### Mobile模块 (src/mobile/res)
- **layout**: 约90个文件
- **drawable**: 约80个文件
- **color**: 5个文件 (包含night模式)
- **menu**: 1个文件
- **values**: 4个文件
- **values-night**: 1个文件
- **values-zh-rCN**: 1个文件
- **values-zh-rTW**: 1个文件

#### 子模块资源
- **catvod**: 仅strings.xml文件
- **其他子模块**: 主要为Java代码，无资源文件

## 技术方案

### 方案选择
由于novoda/gradle-android-resource-rename-plugin插件已不再维护，我们采用自定义Gradle脚本方案：

1. **Gradle resourcePrefix配置** - 强制资源命名规范
2. **自定义脚本** - 自动重命名现有资源
3. **引用同步** - 自动更新所有资源引用

### 实施策略
采用分步实施策略，每完成一个子模块的添加前缀就进行确认并同步更新引用，确保没有问题后再进行下一个子模块。

#### 实施顺序
1. **第一阶段**: 主模块 (src/main)
2. **第二阶段**: Leanback模块 (src/leanback)
3. **第三阶段**: Mobile模块 (src/mobile)
4. **第四阶段**: CatVOD子模块
5. **第五阶段**: 验证和测试

## 自动化脚本设计

### Gradle配置增强
```kotlin
android {
    // 强制资源前缀
    resourcePrefix = "vod_"
    
    // 资源处理配置
    androidResources {
        generateLocaleConfig = true
    }
}
```

### 资源重命名脚本
创建自定义Gradle任务，实现：
1. 扫描所有资源文件
2. 重命名资源文件
3. 更新XML文件中的引用
4. 更新Java/Kotlin代码中的引用
5. 生成变更报告

### 引用同步机制
- **XML资源引用**: `@drawable/icon` → `@drawable/vod_icon`
- **代码引用**: `R.drawable.icon` → `R.drawable.vod_icon`
- **样式引用**: `@style/Theme` → `@style/vod_Theme`
- **字符串引用**: `@string/app_name` → `@string/vod_app_name`

## 风险控制

### 备份策略
1. Git分支保护
2. 分步提交
3. 回滚机制

### 验证机制
1. 编译验证
2. 资源引用检查
3. 功能测试
4. Git diff审查

### 异常处理
1. 脚本执行失败恢复
2. 引用更新失败处理
3. 资源冲突解决

## 预期效果

### 资源隔离
- 完全避免与主应用资源冲突
- 清晰的模块资源边界
- 便于模块独立维护

### 构建优化
- 减少资源合并冲突
- 提高构建稳定性
- 支持模块化开发

### 维护便利
- 资源归属清晰
- 便于问题定位
- 支持模块重构

## 下一步计划

1. 创建详细实施方案文档
2. 开发自动化脚本
3. 分阶段执行重命名
4. 验证和测试
5. 文档更新和总结

---

**文档版本**: v1.0  
**创建日期**: 2025-01-16  
**更新日期**: 2025-01-16  
**状态**: 方案设计完成
