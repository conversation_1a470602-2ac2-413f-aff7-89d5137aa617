package top.cywin.onetv.movie.model;

import android.text.TextUtils;
import android.util.Log;

import androidx.collection.ArrayMap;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import top.cywin.onetv.movie.App;
import top.cywin.onetv.movie.Constants;
import top.cywin.onetv.movie.R;
import top.cywin.onetv.movie.event.*;
import top.cywin.onetv.movie.event.MovieIdTransformEvent;

import org.greenrobot.eventbus.EventBus;
import top.cywin.onetv.movie.api.config.VodConfig;
import top.cywin.onetv.movie.bean.Episode;
import top.cywin.onetv.movie.bean.Flag;
import top.cywin.onetv.movie.bean.Result;
import top.cywin.onetv.movie.bean.Site;
import top.cywin.onetv.movie.bean.Url;
import top.cywin.onetv.movie.bean.Vod;
import top.cywin.onetv.movie.exception.ExtractException;
import top.cywin.onetv.movie.player.Source;
import top.cywin.onetv.movie.utils.ResUtil;
import top.cywin.onetv.movie.utils.Sniffer;
import com.github.catvod.crawler.Spider;
import com.github.catvod.crawler.SpiderDebug;
import com.github.catvod.net.OkHttp;
import com.github.catvod.utils.Trans;
import com.github.catvod.utils.Util;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Response;

// ✅ 添加EventBus支持
import org.greenrobot.eventbus.EventBus;
import top.cywin.onetv.movie.event.*;

public class SiteViewModel extends ViewModel {

    private static final String TAG = "ONETV_SITE_VM";

    public MutableLiveData<Episode> episode;
    public MutableLiveData<Result> result;
    public MutableLiveData<Result> player;
    public MutableLiveData<Result> search;
    public MutableLiveData<Result> action;
    private ExecutorService executor;

    // ✅ 添加搜索状态记录
    private String lastSearchKeyword = "";
    private String lastTypeId = "";
    private int lastPage = 1;
    private boolean isDetailContent = false; // 标识是否为详情内容
    private String currentSiteKey = null; // 当前站点Key，用于设置Vod的siteKey

    public SiteViewModel() {
        this.episode = new MutableLiveData<>();
        this.result = new MutableLiveData<>();
        this.player = new MutableLiveData<>();
        this.search = new MutableLiveData<>();
        this.action = new MutableLiveData<>();
    }

    public void setEpisode(Episode value) {
        episode.setValue(value);
    }

    public void homeContent() {
        Log.d(TAG, "🔄 开始获取首页内容");
        // ✅ 重置分类状态，确保事件正确分发
        this.lastTypeId = null;
        this.lastPage = 1;
        execute(result, () -> {
            Site site = VodConfig.get().getHome();
            Log.d(TAG, "🏠 获取默认站点: " + (site != null ? site.getName() : "null"));
            Log.d(TAG, "📊 站点类型: " + (site != null ? site.getType() : "unknown"));

            if (site.getType() == 3) {
                Log.d(TAG, "🕷️ 处理type=3站点，开始获取Spider");
                try {
                    Spider spider = site.recent().spider();
                    Log.d(TAG, "✅ Spider获取成功: " + (spider != null ? spider.getClass().getSimpleName() : "null"));

                    Log.d(TAG, "🔄 调用spider.homeContent(true)");
                    String homeContent = spider.homeContent(true);
                    Log.d(TAG, "📊 homeContent结果长度: " + (homeContent != null ? homeContent.length() : 0));
                    SpiderDebug.log(homeContent);

                    Result result = Result.fromJson(homeContent);
                    Log.d(TAG, "📊 解析结果: " + (result != null ? result.getList().size() : 0) + "个内容");

                    if (!result.getList().isEmpty()) {
                        Log.d(TAG, "✅ homeContent返回了内容，直接返回");
                        return result;
                    }

                    Log.d(TAG, "⚠️ homeContent为空，尝试homeVideoContent");
                    String homeVideoContent = spider.homeVideoContent();
                    Log.d(TAG,
                            "📊 homeVideoContent结果长度: " + (homeVideoContent != null ? homeVideoContent.length() : 0));
                    SpiderDebug.log(homeVideoContent);

                    result.setList(Result.fromJson(homeVideoContent).getList());
                    Log.d(TAG, "📊 最终结果: " + result.getList().size() + "个内容");
                    return result;
                } catch (Exception e) {
                    Log.e(TAG, "❌ Spider处理失败", e);
                    throw e;
                }
            } else if (site.getType() == 4) {
                Log.d(TAG, "🔄 处理type=4站点");
                ArrayMap<String, String> params = new ArrayMap<>();
                params.put("filter", "true");
                String homeContent = call(site.fetchExt(), params);
                Log.d(TAG, "📊 type=4结果长度: " + (homeContent != null ? homeContent.length() : 0));
                SpiderDebug.log(homeContent);
                return Result.fromJson(homeContent);
            } else {
                Log.d(TAG, "🔄 处理type=" + site.getType() + "站点");
                Response response = OkHttp.newCall(site.getApi(), site.getHeaders()).execute();
                String homeContent = response.body().string();
                Log.d(TAG, "📊 type=" + site.getType() + "结果长度: " + (homeContent != null ? homeContent.length() : 0));
                SpiderDebug.log(homeContent);
                response.close();
                return fetchPic(site, Result.fromType(site.getType(), homeContent));
            }
        });
    }

    public void categoryContent(String key, String tid, String page, boolean filter, HashMap<String, String> extend) {
        // ✅ 记录分类状态
        this.lastTypeId = tid;
        this.lastPage = Integer.parseInt(page);

        execute(result, () -> {
            Site site = VodConfig.get().getSite(key);
            if (site.getType() == 3) {
                Spider spider = site.recent().spider();
                String categoryContent = spider.categoryContent(tid, page, filter, extend);
                SpiderDebug.log(categoryContent);
                return Result.fromJson(categoryContent);
            } else {
                ArrayMap<String, String> params = new ArrayMap<>();
                if (site.getType() == 1 && !extend.isEmpty())
                    params.put("f", App.gson().toJson(extend));
                if (site.getType() == 4)
                    params.put("ext", Util.base64(App.gson().toJson(extend), Util.URL_SAFE));
                params.put("ac", site.getType() == 0 ? "videolist" : "detail");
                params.put("t", tid);
                params.put("pg", page);
                String categoryContent = call(site, params);
                SpiderDebug.log(categoryContent);
                return Result.fromType(site.getType(), categoryContent);
            }
        });
    }

    public void detailContent(String key, String id) {
        // 🔥 关键修复：设置详情内容标识，避免与分类内容冲突
        lastTypeId = null; // 清空分类ID，标识这是详情内容
        isDetailContent = true; // 设置详情内容标识
        currentSiteKey = key; // 🔑 关键修复：保存当前站点Key

        android.util.Log.d("ONETV_SITE_VM", "🎬 [FongMi_TV兼容] detailContent调用: key=" + key + ", id=" + id);
        android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] SiteViewModel.detailContent开始执行");
        android.util.Log.d("ONETV_SITE_VM", "🔑 [架构修复] 设置currentSiteKey: " + currentSiteKey);

        execute(result, () -> {
            android.util.Log.d("ONETV_SITE_VM", "🔄 [电影ID跟踪] execute方法开始执行");
            // 参数验证 - 基于原版FongMi_TV的严格验证
            if (id == null || id.isEmpty()) {
                android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] 无效的电影ID: " + id);
                throw new IllegalArgumentException("电影ID不能为空");
            }

            // 🔥 删除临时ID检查逻辑
            // 原版FongMi_TV中不存在临时ID概念

            if (id.startsWith("msearch:")) {
                android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] msearch类型ID无法获取详情: " + id);
                throw new IllegalArgumentException("msearch类型ID需要先进行搜索转换");
            }

            android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] ID验证通过: " + id);

            Site site = VodConfig.get().getSite(key);
            if (site == null) {
                android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] 站点未找到: " + key);
                android.util.Log.e("ONETV_SITE_VM", "🎯 [电影ID跟踪] 站点查找失败，抛出异常");
                throw new IllegalArgumentException("站点未找到: " + key);
            }

            android.util.Log.d("ONETV_SITE_VM", "🔄 [FongMi_TV兼容] 开始获取详情: 站点=" + site.getName() + ", ID=" + id);
            android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] 站点验证成功，站点类型: " + site.getType());

            if (site.getType() == 3) {
                // Spider类型站点处理
                android.util.Log.d("ONETV_SITE_VM", "🕷️ [FongMi_TV兼容] Spider站点处理: " + site.getName());
                android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] 开始Spider类型站点处理");

                Spider spider = site.recent().spider();
                if (spider == null) {
                    android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] Spider创建失败");
                    android.util.Log.e("ONETV_SITE_VM", "🎯 [电影ID跟踪] Spider创建失败，抛出异常");
                    throw new RuntimeException("Spider创建失败");
                }

                android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] Spider创建成功，开始调用detailContent");
                android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] 调用Spider.detailContent，参数: " + id);

                String detailContent = spider.detailContent(Arrays.asList(id));
                android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] Spider.detailContent返回，结果长度: "
                        + (detailContent != null ? detailContent.length() : 0));

                SpiderDebug.log(detailContent);
                Result result = Result.fromJson(detailContent);
                android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] JSON解析完成，结果列表大小: "
                        + (result != null && result.getList() != null ? result.getList().size() : 0));
                if (!result.getList().isEmpty())
                    result.getList().get(0).setVodFlags();
                if (!result.getList().isEmpty())
                    Source.get().parse(result.getList().get(0).getVodFlags());

                android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] Spider详情获取成功");
                return result;
            } else if (site.isEmpty() && "push_agent".equals(key)) {
                // Push代理处理
                android.util.Log.d("ONETV_SITE_VM", "📡 [FongMi_TV兼容] Push代理处理: " + id);

                Vod vod = new Vod();
                vod.setVodId(id);
                vod.setVodName(id);
                vod.setVodPic(ResUtil.getString(R.string.vod_push_image));
                vod.setVodFlags(Flag.create(ResUtil.getString(R.string.vod_push), id));
                Source.get().parse(vod.getVodFlags());

                android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] Push代理处理成功");
                return Result.vod(vod);
            } else {
                // API类型站点处理
                android.util.Log.d("ONETV_SITE_VM", "🌐 [FongMi_TV兼容] API站点处理: " + site.getName());

                ArrayMap<String, String> params = new ArrayMap<>();
                params.put("ac", site.getType() == 0 ? "videolist" : "detail");
                params.put("ids", id);
                String detailContent = call(site, params);
                SpiderDebug.log(detailContent);
                Result result = Result.fromType(site.getType(), detailContent);
                if (!result.getList().isEmpty())
                    result.getList().get(0).setVodFlags();
                if (!result.getList().isEmpty())
                    Source.get().parse(result.getList().get(0).getVodFlags());

                android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] API详情获取成功");
                return result;
            }
        });
    }

    public void playerContent(String key, String flag, String id) {
        execute(player, () -> {
            // 🔥 新增：播放地址解析日志跟踪
            android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 开始解析播放地址");
            android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 参数: key=" + key + ", flag=" + flag + ", id=" + id);

            Source.get().stop();
            Site site = VodConfig.get().getSite(key);
            android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 站点信息: " + (site != null ? site.getName() : "null")
                    + ", type=" + (site != null ? site.getType() : "null"));

            if (site.getType() == 3) {
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 使用Spider解析 (type=3)");
                Spider spider = site.recent().spider();
                android.util.Log.d("ONETV_PLAYER_CONTENT",
                        "🎬 [播放地址解析] Spider创建成功: " + spider.getClass().getSimpleName());

                String playerContent = spider.playerContent(flag, id, VodConfig.get().getFlags());
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] Spider.playerContent返回: "
                        + (playerContent != null ? playerContent.length() : 0) + " 字符");
                SpiderDebug.log(playerContent);

                Result result = Result.fromJson(playerContent);
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] JSON解析完成: " + (result != null ? "成功" : "失败"));

                if (result.getFlag().isEmpty())
                    result.setFlag(flag);

                // 🔥 关键：Source.fetch()解析特殊前缀地址
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 调用Source.fetch()前: url=" + result.getUrl());
                result.setUrl(Source.get().fetch(result));
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 调用Source.fetch()后: url=" + result.getUrl());

                result.setHeader(site.getHeader());
                result.setKey(key);
                return result;
            } else if (site.getType() == 4) {
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 使用API解析 (type=4)");
                ArrayMap<String, String> params = new ArrayMap<>();
                params.put("play", id);
                params.put("flag", flag);
                String playerContent = call(site, params);
                android.util.Log.d("ONETV_PLAYER_CONTENT",
                        "🎬 [播放地址解析] API调用返回: " + (playerContent != null ? playerContent.length() : 0) + " 字符");
                SpiderDebug.log(playerContent);

                Result result = Result.fromJson(playerContent);
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] JSON解析完成: " + (result != null ? "成功" : "失败"));

                if (result.getFlag().isEmpty())
                    result.setFlag(flag);

                // 🔥 关键：Source.fetch()解析特殊前缀地址
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 调用Source.fetch()前: url=" + result.getUrl());
                result.setUrl(Source.get().fetch(result));
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 调用Source.fetch()后: url=" + result.getUrl());

                result.setHeader(site.getHeader());
                return result;
            } else if (site.isEmpty() && "push_agent".equals(key)) {
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 使用推送代理解析 (push_agent)");
                Result result = new Result();
                result.setParse(0);
                result.setFlag(flag);
                result.setUrl(Url.create().add(id));

                // 🔥 关键：Source.fetch()解析特殊前缀地址
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 调用Source.fetch()前: url=" + result.getUrl());
                result.setUrl(Source.get().fetch(result));
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 调用Source.fetch()后: url=" + result.getUrl());

                return result;
            } else {
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 使用默认解析");
                Url url = Url.create().add(id);
                Result result = new Result();
                result.setUrl(url);
                result.setFlag(flag);
                result.setHeader(site.getHeader());
                result.setPlayUrl(site.getPlayUrl());
                result.setParse(Sniffer.isVideoFormat(url.v()) && result.getPlayUrl().isEmpty() ? 0 : 1);

                // 🔥 关键：Source.fetch()解析特殊前缀地址
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 调用Source.fetch()前: url=" + result.getUrl());
                result.setUrl(Source.get().fetch(result));
                android.util.Log.d("ONETV_PLAYER_CONTENT", "🎬 [播放地址解析] 调用Source.fetch()后: url=" + result.getUrl());

                SpiderDebug.log(result.toString());
                return result;
            }
        });
    }

    public void action(String key, String action) {
        execute(this.action, () -> {
            Site site = VodConfig.get().getSite(key);
            if (site.getType() == 3)
                return Result.fromJson(site.recent().spider().action(action));
            if (site.getType() == 4)
                return Result.fromJson(OkHttp.string(action));
            return Result.empty();
        });
    }

    public void searchContent(Site site, String keyword, boolean quick) throws Throwable {
        if (site.getType() == 3) {
            if (quick && !site.isQuickSearch())
                return;

            // 🔥 关键调试：记录搜索参数
            android.util.Log.d("ONETV_SITE_VM", "🔍 [搜索开始] 站点: " + site.getName() +
                    ", 原始关键词: '" + keyword + "'" +
                    ", 转换后关键词: '" + Trans.t2s(keyword) + "'" +
                    ", quick: " + quick);

            // 🔥 获取Spider实例并验证
            Spider spider = site.spider();
            android.util.Log.d("ONETV_SITE_VM", "🕷️ [Spider验证] 站点: " + site.getName() +
                    ", Spider类型: " + (spider != null ? spider.getClass().getSimpleName() : "null") +
                    ", API: " + site.getApi() +
                    ", JAR: " + site.getJar());

            if (spider == null) {
                android.util.Log.e("ONETV_SITE_VM", "❌ [Spider错误] Spider为null: " + site.getName());
                return;
            }

            // 🔥 修复关键问题：使用3参数版本的searchContent，与原版FongMi_TV一致
            String searchKeyword = Trans.t2s(keyword);
            android.util.Log.d("ONETV_SITE_VM", "🔍 [搜索调用] 即将调用Spider.searchContent: " +
                    "keyword='" + searchKeyword + "', quick=" + false + ", pg='1'");

            String searchContent = spider.searchContent(searchKeyword, false, "1");
            SpiderDebug.log(site.getName() + "," + searchContent);

            // 🔥 关键调试：记录原始搜索内容
            android.util.Log.d("ONETV_SITE_VM", "🔍 [搜索结果] 站点: " + site.getName() +
                    ", 原始搜索内容: '" + searchContent + "'" +
                    ", 内容长度: " + (searchContent != null ? searchContent.length() : 0));

            Result result = Result.fromJson(searchContent);
            android.util.Log.d("ONETV_SITE_VM", "🔍 [JSON解析] 站点: " + site.getName() +
                    ", Result: " + (result != null ? "非空" : "空") +
                    ", List大小: " + (result != null && result.getList() != null ? result.getList().size() : 0));

            post(site, result);
        } else {
            if (quick && !site.isQuickSearch())
                return;
            ArrayMap<String, String> params = new ArrayMap<>();
            params.put("wd", Trans.t2s(keyword));
            params.put("quick", String.valueOf(quick));
            String searchContent = call(site, params);
            SpiderDebug.log(site.getName() + "," + searchContent);
            post(site, fetchPic(site, Result.fromType(site.getType(), searchContent)));
        }
    }

    public void searchContent(Site site, String keyword, String page) {
        // ✅ 记录搜索状态
        this.lastSearchKeyword = keyword;

        execute(search, () -> {
            if (site.getType() == 3) {
                String searchContent = site.spider().searchContent(Trans.t2s(keyword), false, page);
                SpiderDebug.log(site.getName() + "," + searchContent);
                Result result = Result.fromJson(searchContent);
                for (Vod vod : result.getList())
                    vod.setSite(site);
                return result;
            } else {
                ArrayMap<String, String> params = new ArrayMap<>();
                params.put("wd", Trans.t2s(keyword));
                params.put("pg", page);
                String searchContent = call(site, params);
                SpiderDebug.log(site.getName() + "," + searchContent);
                Result result = fetchPic(site, Result.fromType(site.getType(), searchContent));
                for (Vod vod : result.getList())
                    vod.setSite(site);
                return result;
            }
        });
    }

    private String call(Site site, ArrayMap<String, String> params) throws IOException {
        if (!site.getExt().isEmpty())
            params.put("extend", site.getExt());
        Call get = OkHttp.newCall(site.getApi(), site.getHeaders(), params);
        Call post = OkHttp.newCall(site.getApi(), site.getHeaders(), OkHttp.toBody(params));
        Response response = (site.getExt().length() <= 1000 ? get : post).execute();
        String result = response.body().string();
        response.close();
        return result;
    }

    private Result fetchPic(Site site, Result result) throws Exception {
        if (site.getType() > 2 || result.getList().isEmpty() || !result.getList().get(0).getVodPic().isEmpty())
            return result;
        ArrayList<String> ids = new ArrayList<>();
        if (site.getCategories().isEmpty())
            for (Vod item : result.getList())
                ids.add(item.getVodId());
        else
            for (Vod item : result.getList())
                if (site.getCategories().contains(item.getTypeName()))
                    ids.add(item.getVodId());
        if (ids.isEmpty())
            return result.clear();
        ArrayMap<String, String> params = new ArrayMap<>();
        params.put("ac", site.getType() == 0 ? "videolist" : "detail");
        params.put("ids", TextUtils.join(",", ids));
        Response response = OkHttp.newCall(site.getApi(), site.getHeaders(), params).execute();
        result.setList(Result.fromType(site.getType(), response.body().string()).getList());
        response.close();
        return result;
    }

    private void post(Site site, Result result) {
        // 🔥 关键调试：详细记录搜索结果解析情况
        android.util.Log.d("ONETV_SITE_VM", "🔍 [调试] 站点搜索结果解析: " + site.getName());
        android.util.Log.d("ONETV_SITE_VM", "🔍 [调试] Result对象: " + (result != null ? "非空" : "空"));
        if (result != null) {
            android.util.Log.d("ONETV_SITE_VM", "🔍 [调试] List大小: " + result.getList().size());
            android.util.Log.d("ONETV_SITE_VM", "🔍 [调试] List内容: " + result.getList().toString());
        }

        if (result.getList().isEmpty()) {
            android.util.Log.d("ONETV_SITE_VM", "⚠️ [FongMi_TV兼容] 站点搜索无结果: " + site.getName());
            return;
        }

        // 设置站点信息
        for (Vod vod : result.getList())
            vod.setSite(site);

        // 原版LiveData通知
        this.search.postValue(result);

        // 🔥 关键：发送EventBus事件，对应原版FongMi_TV的搜索结果处理
        android.util.Log.d("ONETV_SITE_VM",
                "✅ [FongMi_TV兼容] 站点搜索成功: " + site.getName() + ", 结果数: " + result.getList().size());

        // 发送搜索结果事件
        try {
            org.greenrobot.eventbus.EventBus.getDefault().post(
                    new top.cywin.onetv.movie.event.SearchResultEvent(
                            result.getList(), // results: List<Vod>
                            lastSearchKeyword != null ? lastSearchKeyword : "", // keyword: String
                            false, // hasMore: Boolean
                            1, // page: Int
                            result.getList().size() // total: Int
                    ));
            android.util.Log.d("ONETV_SITE_VM", "📡 [FongMi_TV兼容] 搜索结果事件已发送: " + result.getList().size() + " 个结果");
        } catch (Exception e) {
            android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] 发送搜索结果事件失败", e);
        }
    }

    private void execute(MutableLiveData<Result> result, Callable<Result> callable) {
        android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] execute方法被调用");
        if (executor != null)
            executor.shutdownNow();
        executor = Executors.newFixedThreadPool(2);
        executor.execute(() -> {
            try {
                android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] 开始执行callable任务");
                if (Thread.interrupted())
                    return;
                Result resultData = executor.submit(callable).get(Constants.TIMEOUT_VOD, TimeUnit.MILLISECONDS);
                android.util.Log.d("ONETV_SITE_VM",
                        "🎯 [电影ID跟踪] callable任务执行完成，结果: " + (resultData != null ? "非空" : "空"));
                result.postValue(resultData);

                // ✅ 添加EventBus通知机制
                android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] 准备通知Compose UI");
                notifyComposeUI(result, resultData);

            } catch (Throwable e) {
                android.util.Log.e("ONETV_SITE_VM", "🎯 [电影ID跟踪] execute方法执行异常", e);
                if (e instanceof InterruptedException || Thread.interrupted())
                    return;

                Result errorResult;
                if (e.getCause() instanceof ExtractException) {
                    errorResult = Result.error(e.getCause().getMessage());
                    // ✅ 通知Compose UI错误
                    EventBus.getDefault().post(new ErrorEvent(e.getCause().getMessage(), ErrorEvent.Type.EXTRACT));
                } else {
                    errorResult = Result.empty();
                    // ✅ 通知Compose UI错误
                    EventBus.getDefault().post(new ErrorEvent("请求失败", ErrorEvent.Type.NETWORK));
                }
                result.postValue(errorResult);
                e.printStackTrace();
            }
        });
    }

    /**
     * 通知Compose UI - 根据结果类型发送相应事件
     */
    private void notifyComposeUI(MutableLiveData<Result> resultLiveData, Result resultData) {
        android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] notifyComposeUI被调用");
        android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] resultLiveData类型: "
                + (resultLiveData == result ? "result" : resultLiveData == search ? "search" : "其他"));
        android.util.Log.d("ONETV_SITE_VM", "🎯 [电影ID跟踪] isDetailContent标志: " + isDetailContent);
        try {
            // ✅ 根据LiveData类型判断操作类型
            if (resultLiveData == search) {
                // 搜索结果
                EventBus.getDefault().post(new SearchResultEvent(
                        resultData.getList(),
                        lastSearchKeyword,
                        resultData.getList().size() >= 20,
                        lastPage,
                        resultData.getTotal()));
            } else if (resultLiveData == result) {
                // 🔥 关键修复：根据isDetailContent标识区分详情内容和分类内容
                if (isDetailContent) {
                    // 详情内容
                    Vod vod = resultData.getList().isEmpty() ? null : resultData.getList().get(0);

                    android.util.Log.d("ONETV_SITE_VM", "📡 [FongMi_TV兼容] 准备发送ContentDetailEvent");
                    android.util.Log.d("ONETV_SITE_VM",
                            "📊 [FongMi_TV兼容] 详情数据: vod=" + (vod != null ? vod.getVodName() : "null"));
                    android.util.Log.d("ONETV_SITE_VM", "🔍 [FongMi_TV兼容] LiveData类型: result (详情内容)");

                    // 🔥 关键修复：确保Vod对象包含正确的siteKey
                    if (vod != null && currentSiteKey != null) {
                        Site site = VodConfig.get().getSite(currentSiteKey);
                        if (site != null) {
                            vod.setSite(site);
                            android.util.Log.d("ONETV_SITE_VM", "🔑 [架构修复] 设置Vod的Site: " + currentSiteKey);
                            android.util.Log.d("ONETV_SITE_VM",
                                    "🎯 [电影ID跟踪] Vod详情: vodId=" + vod.getVodId() + ", siteKey="
                                            + vod.getSiteKey() + ", name=" + vod.getVodName());
                        } else {
                            android.util.Log.w("ONETV_SITE_VM", "⚠️ [架构修复] 未找到站点: " + currentSiteKey);
                        }
                    }

                    EventBus.getDefault().post(new ContentDetailEvent(vod, true, null));

                    android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] ContentDetailEvent已发送");

                    // 重置标识
                    isDetailContent = false;
                } else if (!TextUtils.isEmpty(lastTypeId)) {
                    // 分类内容
                    EventBus.getDefault().post(new CategoryContentEvent(
                            resultData.getList(),
                            lastTypeId,
                            lastPage,
                            resultData.getList().size() >= 20,
                            resultData.getTotal()));
                } else {
                    // 首页内容
                    EventBus.getDefault().post(new HomeContentEvent(
                            resultData.getTypes(),
                            resultData.getList(),
                            true));
                }
            } else if (resultLiveData == action) {
                // action类型的详情内容（如果有的话）
                Vod vod = resultData.getList().isEmpty() ? null : resultData.getList().get(0);

                android.util.Log.d("ONETV_SITE_VM", "📡 [FongMi_TV兼容] 准备发送ContentDetailEvent (action类型)");
                android.util.Log.d("ONETV_SITE_VM",
                        "📊 [FongMi_TV兼容] 详情数据: vod=" + (vod != null ? vod.getVodName() : "null"));

                // 🔥 关键修复：确保Vod对象包含正确的siteKey (action类型)
                if (vod != null && currentSiteKey != null) {
                    Site site = VodConfig.get().getSite(currentSiteKey);
                    if (site != null) {
                        vod.setSite(site);
                        android.util.Log.d("ONETV_SITE_VM", "🔑 [架构修复] 设置Vod的Site (action类型): " + currentSiteKey);
                    } else {
                        android.util.Log.w("ONETV_SITE_VM", "⚠️ [架构修复] 未找到站点 (action类型): " + currentSiteKey);
                    }
                }

                EventBus.getDefault().post(new ContentDetailEvent(vod, true, null));

                android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] ContentDetailEvent已发送 (action类型)");
            } else if (resultLiveData == player) {
                // 🔥 修复：播放地址解析结果处理
                android.util.Log.d("ONETV_SITE_VM", "📡 [播放地址解析] 收到播放地址解析结果");

                // 播放地址解析的结果是Result对象，不是Vod对象
                if (resultData instanceof Result) {
                    Result playResult = (Result) resultData;
                    android.util.Log.d("ONETV_SITE_VM", "🎬 [播放地址解析] Result对象: url=" + playResult.getUrl());
                    android.util.Log.d("ONETV_SITE_VM", "🎬 [播放地址解析] Result对象: flag=" + playResult.getFlag());
                    android.util.Log.d("ONETV_SITE_VM", "🎬 [播放地址解析] Result对象: parse=" + playResult.getParse());

                    // 发送播放地址解析成功事件
                    String finalPlayUrl = playResult.getUrl() != null ? playResult.getUrl().toString() : "";
                    android.util.Log.d("ONETV_SITE_VM", "🎬 [播放地址解析] 最终播放地址: " + finalPlayUrl);

                    EventBus.getDefault().post(
                            new PlayUrlParseEvent(finalPlayUrl, playResult.getHeaders(), "", 0, playResult.getFlag()));
                    android.util.Log.d("ONETV_SITE_VM", "✅ [播放地址解析] PlayUrlParseEvent已发送");
                } else {
                    android.util.Log.e("ONETV_SITE_VM", "❌ [播放地址解析] 结果类型错误: "
                            + (resultData != null ? resultData.getClass().getSimpleName() : "null"));
                }
            }
        } catch (Exception e) {
            // 通知失败不影响主流程
            e.printStackTrace();
        }
    }

    // ✅ 添加便捷方法供Compose UI调用

    /**
     * 搜索内容 - Compose UI调用入口，完全按照原版FongMi_TV的多站点搜索逻辑
     */
    public void searchContent(String keyword, boolean quick) {
        android.util.Log.d("ONETV_SITE_VM", "🔍 [FongMi_TV兼容] 开始多站点搜索: keyword=" + keyword + ", quick=" + quick);

        this.lastSearchKeyword = keyword;

        // 🔥 关键：完全按照原版FongMi_TV的CollectActivity.setSite()逻辑
        java.util.List<Site> mSites = new java.util.ArrayList<>();
        int totalSites = VodConfig.get().getSites().size();
        int searchableSites = 0;
        int cspSites = 0;
        int jsSites = 0;

        android.util.Log.d("ONETV_SITE_VM", "📊 [站点统计] 总站点数: " + totalSites);

        for (Site site : VodConfig.get().getSites()) {
            // 统计站点类型
            if (site.getApi().startsWith("csp_")) {
                cspSites++;
            } else if (site.getApi().contains(".js")) {
                jsSites++;
            }

            if (site.isSearchable()) {
                mSites.add(site);
                searchableSites++;
                android.util.Log.d("ONETV_SITE_VM",
                        "✅ [可搜索站点] " + site.getName() + " (type=" + site.getType() + ", api=" + site.getApi() + ")");
            } else {
                android.util.Log.d("ONETV_SITE_VM",
                        "❌ [不可搜索站点] " + site.getName() + " (searchable=" + site.getSearchable() + ")");
            }
        }

        android.util.Log.d("ONETV_SITE_VM",
                "📊 [站点统计] CSP站点: " + cspSites + ", JS站点: " + jsSites + ", 可搜索站点: " + searchableSites);

        // 按照原版逻辑：将home站点移到第一位
        Site home = VodConfig.get().getHome();
        if (mSites.contains(home)) {
            mSites.remove(home);
            mSites.add(0, home);
        }

        android.util.Log.d("ONETV_SITE_VM", "🌐 [FongMi_TV兼容] 获取到 " + mSites.size() + " 个可搜索站点");

        // 🔥 关键调试：检查前5个可搜索站点的详细信息
        android.util.Log.d("ONETV_SITE_VM", "📋 [前5个可搜索站点详情]:");
        for (int i = 0; i < Math.min(5, mSites.size()); i++) {
            Site site = mSites.get(i);
            android.util.Log.d("ONETV_SITE_VM", String.format("  %d. %s (key=%s, type=%d, api=%s, jar=%s)",
                    i + 1, site.getName(), site.getKey(), site.getType(), site.getApi(),
                    site.getJar() != null ? site.getJar().substring(0, Math.min(50, site.getJar().length())) + "..."
                            : "null"));
        }

        // 🔥 关键：使用原版FongMi_TV的PauseExecutor逻辑（用ThreadPoolExecutor模拟）
        // 原版使用PauseExecutor(10)，我们用固定线程池模拟
        java.util.concurrent.ExecutorService searchExecutor = java.util.concurrent.Executors.newFixedThreadPool(10);

        // 🔥 关键：完全按照原版FongMi_TV的搜索逻辑
        for (Site site : mSites) {
            searchExecutor.execute(() -> {
                try {
                    android.util.Log.d("ONETV_SITE_VM", "🔍 执行单站点搜索: " + site.getName() + " (" + site.getKey() + ")");
                    // 🔥 关键修复：调用单站点搜索方法
                    searchContent(site, keyword, quick);
                } catch (Throwable e) {
                    android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] 站点搜索失败: " + site.getName(), e);
                }
            });
        }

        // 关闭线程池
        searchExecutor.shutdown();
    }

    /**
     * 分类内容 - Compose UI调用入口
     */
    public void categoryContent(String typeId, int page, boolean more, java.util.Map<String, String> filters) {
        this.lastTypeId = typeId;
        this.lastPage = page;

        Site site = VodConfig.get().getHome();
        if (site != null) {
            HashMap<String, String> extend = new HashMap<>();
            if (filters != null)
                extend.putAll(filters);
            categoryContent(site.getKey(), typeId, String.valueOf(page), true, extend);
        }
    }

    /**
     * 获取内容详情 - Compose UI调用入口
     */
    public void detailContent(String vodId) {
        android.util.Log.d("ONETV_SITE_VM", "🎬 [FongMi_TV兼容] detailContent调用: vodId=" + vodId);
        Site site = VodConfig.get().getHome();
        if (site != null) {
            android.util.Log.d("ONETV_SITE_VM", "🔑 [FongMi_TV兼容] 使用站点: " + site.getName() + ", key: " + site.getKey());
            detailContent(site.getKey(), vodId);
        } else {
            android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] 没有可用的站点");
        }
    }

    /**
     * 搜索转换ID - 实现原版VideoActivity的ID转换机制
     */
    public void searchForIdTransformation(String movieName, String siteKey) {
        android.util.Log.d("ONETV_SITE_VM", "🔄 [FongMi_TV兼容] 开始搜索转换ID: " + movieName + ", siteKey: " + siteKey);

        execute(search, () -> {
            android.util.Log.d("ONETV_SITE_VM", "🔍 [FongMi_TV兼容] 执行搜索以获取真实ID: " + movieName);

            Site site = VodConfig.get().getHome();
            if (site == null) {
                android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] 没有可用的站点进行搜索");
                EventBus.getDefault()
                        .post(new MovieIdTransformEvent("msearch:home", null, siteKey, movieName, "没有可用的站点"));
                return null;
            }

            try {
                String searchResult;
                if (site.getType() == 3) {
                    // Spider搜索
                    android.util.Log.d("ONETV_SITE_VM", "🕷️ [FongMi_TV兼容] 使用Spider搜索: " + site.getKey());
                    Spider spider = site.recent().spider();
                    searchResult = spider.searchContent(movieName, false);
                } else {
                    // API搜索
                    android.util.Log.d("ONETV_SITE_VM", "🌐 [FongMi_TV兼容] 使用API搜索: " + site.getKey());
                    ArrayMap<String, String> params = new ArrayMap<>();
                    params.put("ac", site.getType() == 0 ? "videolist" : "list");
                    params.put("wd", movieName);
                    searchResult = call(site, params);
                }

                android.util.Log.d("ONETV_SITE_VM",
                        "📊 [FongMi_TV兼容] 搜索结果长度: " + (searchResult != null ? searchResult.length() : 0));

                Result result = site.getType() == 3 ? Result.fromJson(searchResult)
                        : Result.fromType(site.getType(), searchResult);

                if (result != null && !result.getList().isEmpty()) {
                    // 找到搜索结果，使用第一个结果的vodId
                    Vod firstResult = result.getList().get(0);
                    String realVodId = firstResult.getVodId();
                    String realSiteKey = site.getKey();

                    android.util.Log.d("ONETV_SITE_VM", "✅ [FongMi_TV兼容] ID转换成功: " + movieName + " -> vodId: "
                            + realVodId + ", siteKey: " + realSiteKey);

                    // 发送ID转换成功事件
                    EventBus.getDefault()
                            .post(new MovieIdTransformEvent("msearch:home", realVodId, realSiteKey, movieName));
                } else {
                    android.util.Log.w("ONETV_SITE_VM", "⚠️ [FongMi_TV兼容] 搜索无结果，无法进行ID转换: " + movieName);
                    EventBus.getDefault()
                            .post(new MovieIdTransformEvent("msearch:home", null, siteKey, movieName, "搜索无结果"));
                }

                return result;
            } catch (Exception e) {
                android.util.Log.e("ONETV_SITE_VM", "❌ [FongMi_TV兼容] ID转换过程中发生异常: " + movieName, e);
                EventBus.getDefault()
                        .post(new MovieIdTransformEvent("msearch:home", null, siteKey, movieName, e.getMessage()));
                return null;
            }
        });
    }

    /**
     * 解析播放地址 - Compose UI调用入口
     */
    public void playerContent(String url, String flag) {
        Site site = VodConfig.get().getHome();
        if (site != null) {
            playerContent(site.getKey(), flag, url);
        }
    }

    @Override
    protected void onCleared() {
        if (executor != null)
            executor.shutdownNow();
    }
}
