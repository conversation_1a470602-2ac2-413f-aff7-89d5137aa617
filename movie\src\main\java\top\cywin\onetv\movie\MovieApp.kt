package top.cywin.onetv.movie

import android.app.Application
import android.util.Log
import coil.ImageLoader
import coil.ImageLoaderFactory
import coil.util.DebugLogger
import top.cywin.onetv.movie.utils.HeadersInterceptor

// ✅ 正确的引用 - 使用现有适配器
import top.cywin.onetv.movie.adapter.IntegrationManager
import top.cywin.onetv.movie.adapter.RepositoryAdapter
import top.cywin.onetv.movie.adapter.UIAdapter
import top.cywin.onetv.movie.adapter.ViewModelAdapter

// ✅ 直接使用FongMi_TV系统
import top.cywin.onetv.movie.api.config.VodConfig
import top.cywin.onetv.movie.model.SiteViewModel
import top.cywin.onetv.movie.model.LiveViewModel

/**
 * OneTV Movie模块应用单例
 * 整合FongMi_TV解析系统，通过适配器提供统一访问接口
 */
class MovieApp : Application(), ImageLoaderFactory {

    companion object {
        private const val TAG = "ONETV_MOVIE_APP"
        private lateinit var instance: MovieApp

        fun getInstance(): MovieApp {
            if (!::instance.isInitialized) {
                Log.w(TAG, "⚠️ MovieApp实例未初始化，尝试创建临时实例")

                // ✅ 检查App类是否已经初始化
                try {
                    val context = top.cywin.onetv.movie.App.get()
                    if (context == null) {
                        Log.e(TAG, "❌ App.get()返回null，App类未初始化")
                        // 创建一个最小化的实例，不进行完整初始化
                        val tempApp = MovieApp()
                        instance = tempApp
                        Log.w(TAG, "⚠️ 创建未初始化的MovieApp实例，等待后续初始化")
                        return instance
                    }

                    // App类已初始化，可以安全创建完整实例
                    val tempApp = MovieApp()
                    tempApp.initializeWithContext(context)
                    instance = tempApp
                    Log.d(TAG, "✅ 临时MovieApp实例创建成功")

                } catch (e: Exception) {
                    Log.e(TAG, "❌ 创建临时MovieApp实例失败", e)
                    // 创建一个最小化的实例，避免完全失败
                    val tempApp = MovieApp()
                    instance = tempApp
                    Log.w(TAG, "⚠️ 使用未完全初始化的MovieApp实例")
                }
            }
            return instance
        }

        /**
         * 静态初始化方法 - 供TV模块调用
         * 基于FongMi_TV完整功能，不得简化处理
         */
        fun initialize(context: android.content.Context) {
            Log.d(TAG, "🚀 静态初始化Movie模块开始")
            try {
                // 如果实例还未创建，创建一个临时实例进行初始化
                if (!::instance.isInitialized) {
                    Log.d(TAG, "📦 创建Movie模块临时实例")
                    val tempApp = MovieApp()
                    tempApp.initializeWithContext(context)
                    instance = tempApp
                } else {
                    // 如果实例已存在，重新初始化
                    Log.d(TAG, "🔄 重新初始化现有Movie模块实例")
                    instance.initializeWithContext(context)
                }
                Log.d(TAG, "✅ 静态初始化Movie模块完成")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 静态初始化Movie模块失败", e)
                throw e
            }
        }
    }

    // ✅ 使用适配器系统替代不存在的类
    val integrationManager by lazy {
        Log.d(TAG, "🏗️ 创建IntegrationManager")
        IntegrationManager.getInstance()
    }

    val repositoryAdapter by lazy {
        Log.d(TAG, "🏗️ 创建RepositoryAdapter")
        RepositoryAdapter()
    }

    val uiAdapter by lazy {
        Log.d(TAG, "🏗️ 创建UIAdapter")
        UIAdapter()
    }

    val viewModelAdapter by lazy {
        Log.d(TAG, "🏗️ 创建ViewModelAdapter")
        ViewModelAdapter()
    }

    // ✅ 直接使用FongMi_TV的核心组件 - 延迟到初始化完成后
    val vodConfig by lazy {
        Log.d(TAG, "🏗️ 获取VodConfig")
        try {
            // ✅ 确保App类已经初始化
            if (top.cywin.onetv.movie.App.get() == null) {
                Log.e(TAG, "❌ App类未初始化，无法获取VodConfig")
                throw IllegalStateException("App类未初始化")
            }
            VodConfig.get()
        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取VodConfig失败", e)
            throw e
        }
    }

    val siteViewModel: top.cywin.onetv.movie.model.SiteViewModel by lazy {
        Log.d(TAG, "🏗️ 创建SiteViewModel实例")
        top.cywin.onetv.movie.model.SiteViewModel()
    }

    override fun onCreate() {
        super.onCreate()
        instance = this

        Log.d(TAG, "🚀 OneTV Movie应用启动")

        try {
            initializeWithContext(applicationContext)
            Log.d(TAG, "✅ 应用初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "💥 应用初始化失败", e)
        }
    }

    /**
     * 使用指定Context初始化Movie模块 - 基于FongMi_TV完整功能
     * 支持TV模块的外部初始化调用
     */
    fun initializeWithContext(context: android.content.Context) {
        Log.d(TAG, "� [第7阶段] Movie模块Context初始化开始")
        Log.d(TAG, "📍 位置: MovieApp.kt:105")
        Log.d(TAG, "🏗️ Context类型: ${context.javaClass.simpleName}")
        Log.d(TAG, "⏰ 时间戳: ${System.currentTimeMillis()}")

        try {
            // 1. 初始化FongMi_TV核心配置系统
            Log.d(TAG, "🔧 [第7阶段] 开始初始化FongMi_TV核心系统")
            initializeFongMiTVCore(context)
            Log.d(TAG, "✅ [第7阶段] FongMi_TV核心系统初始化完成")

            // 2. 初始化Coil图片加载器
            Log.d(TAG, "🖼️ [第7阶段] 开始初始化Coil图片加载器")
            initializeCoil(context)
            Log.d(TAG, "✅ [第7阶段] Coil图片加载器初始化完成")

            // 3. 初始化适配器系统
            Log.d(TAG, "🔗 [第7阶段] 开始初始化适配器系统")
            initializeAdapters(context)
            Log.d(TAG, "✅ [第7阶段] 适配器系统初始化完成")

            // 4. 🔥 额外初始化Spider引擎（确保完全初始化）
            Log.d(TAG, "🕷️ [第7阶段] 开始额外初始化Spider引擎")
            initializeSpiderEnginesExtra(context)
            Log.d(TAG, "✅ [第7阶段] Spider引擎额外初始化完成")

            Log.d(TAG, "✅ [第7阶段] Movie模块Context初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "❌ [第7阶段] Movie模块Context初始化失败", e)
            throw e
        }
    }

    /**
     * 初始化FongMi_TV核心系统 - 基于FongMi_TV完整功能
     */
    private fun initializeFongMiTVCore(context: android.content.Context) {
        Log.d(TAG, "🔧 初始化FongMi_TV核心系统")

        try {
            // 1. 初始化FongMi_TV的App类（如果还未初始化）
            initializeFongMiTVApp(context)

            // 2. 初始化VOD配置系统
            VodConfig.get().init()
            Log.d(TAG, "✅ VOD配置系统初始化完成")

            // 3. 启动本地服务器（用于assets://协议转换）
            Log.d(TAG, "🔄 启动本地服务器")
            try {
                top.cywin.onetv.movie.server.Server.get().start()
                Log.d(TAG, "✅ 本地服务器启动成功，端口: " + com.github.catvod.Proxy.getPort())
            } catch (e: Exception) {
                Log.e(TAG, "❌ 本地服务器启动失败", e)
            }

            // 4. 初始化其他FongMi_TV核心组件
            // 这里可以添加更多FongMi_TV核心组件的初始化

        } catch (e: Exception) {
            Log.e(TAG, "❌ FongMi_TV核心系统初始化失败", e)
            throw e
        }
    }

    /**
     * 初始化FongMi_TV的App类 - 确保FongMi_TV核心功能可用
     */
    private fun initializeFongMiTVApp(context: android.content.Context) {
        Log.d(TAG, "🔧 初始化FongMi_TV App类")

        try {
            // 0. 首先初始化QuickJS库 - 确保JavaScript解析器正常工作
            Log.d(TAG, "🔧 初始化QuickJS库")
            try {
                com.whl.quickjs.android.QuickJSLoader.init()
                Log.d(TAG, "✅ QuickJS库初始化成功")
            } catch (e: Exception) {
                Log.e(TAG, "❌ QuickJS库初始化失败", e)
                // 不抛出异常，让其他功能继续工作
            }
            // 检查FongMi_TV的App是否已经初始化
            val fongMiApp = top.cywin.onetv.movie.App.get()
            if (fongMiApp == null) {
                Log.d(TAG, "📦 FongMi_TV App未初始化，创建实例")
                // 创建FongMi_TV App实例
                val app = top.cywin.onetv.movie.App()
                // 手动调用attachBaseContext和onCreate
                app.attachBaseContext(context)
                app.onCreate()
                Log.d(TAG, "✅ FongMi_TV App初始化完成")
            } else {
                Log.d(TAG, "✅ FongMi_TV App已存在，跳过初始化")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ FongMi_TV App初始化失败", e)
            // 不抛出异常，因为这可能不是致命错误
        }
    }

    private fun initializeAdapters(context: android.content.Context) {
        Log.d(TAG, "🏗️ 初始化适配器系统")

        try {
            // 1. 初始化Repository适配器（连接FongMi_TV数据层）
            repositoryAdapter.reconnectRepositories()
            Log.d(TAG, "✅ RepositoryAdapter初始化完成")

            // 2. 初始化UI适配器（初始化EventBus监听）
            uiAdapter.initializeEventBus()
            Log.d(TAG, "✅ UIAdapter初始化完成")

            // 3. ViewModel适配器无需初始化（纯静态转换器）
            Log.d(TAG, "✅ ViewModelAdapter准备就绪")

            // 4. 初始化集成管理器（统一管理）
            Log.d(TAG, "🔧 开始初始化IntegrationManager")
            Log.d(TAG, "📍 传入Context: ${context.javaClass.simpleName}")
            integrationManager.initialize(context.applicationContext)
            Log.d(TAG, "✅ IntegrationManager初始化完成")

            Log.d(TAG, "✅ 适配器系统初始化完成")

        } catch (e: Exception) {
            Log.e(TAG, "💥 适配器系统初始化失败", e)
            throw e
        }
    }

    /**
     * 初始化Coil图片加载器 - 使用FongMi_TV的OkHttp客户端
     */
    private fun initializeCoil(context: android.content.Context) {
        Log.d(TAG, "🖼️ 初始化Coil图片加载器")

        try {
            // Coil会自动使用newImageLoader方法创建ImageLoader
            Log.d(TAG, "✅ Coil图片加载器配置完成")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Coil图片加载器初始化失败", e)
            throw e
        }
    }

    /**
     * 创建自定义ImageLoader - 使用FongMi_TV的网络配置，优化SSL处理
     */
    override fun newImageLoader(): ImageLoader {
        Log.d(TAG, "🏗️ 创建自定义ImageLoader")

        return ImageLoader.Builder(this)
            .okHttpClient {
                // 使用FongMi_TV的OkHttp客户端，包含代理、DNS等配置
                try {
                    val client = com.github.catvod.net.OkHttp.client()
                        .newBuilder()
                        // 🔧 修复SSL证书验证问题，提高图片加载成功率
                        .hostnameVerifier { _, _ -> true }
                        .build()
                    Log.d(TAG, "✅ 使用FongMi_TV OkHttp客户端，已优化SSL处理")
                    client
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 获取FongMi_TV OkHttp客户端失败，使用默认客户端", e)
                    okhttp3.OkHttpClient.Builder()
                        .hostnameVerifier { _, _ -> true }
                        .build()
                }
            }
            .components {
                // 🔧 添加Headers处理拦截器，提高图片加载成功率
                val interceptor = HeadersInterceptor()
                add(interceptor)
                Log.d(TAG, "✅ 已添加HeadersInterceptor到ImageLoader")
            }
            .logger(DebugLogger())
            .respectCacheHeaders(false) // 不严格遵守缓存头，允许更灵活的缓存
            .build().also {
                Log.d(TAG, "✅ ImageLoader创建完成")
            }
    }

    /**
     * 🔥 额外初始化Spider引擎管理器（确保完全初始化）
     */
    private fun initializeSpiderEnginesExtra(context: android.content.Context) {
        Log.d(TAG, "🕷️ 额外初始化Spider引擎管理器")

        try {
            // 1. 确保SpiderEngineManager完全初始化
            val engineManager = top.cywin.onetv.movie.spider.engine.SpiderEngineManager.getInstance()
            engineManager.init(context)
            Log.d(TAG, "✅ SpiderEngineManager额外初始化完成")

            // 2. 确保AppSpiderManager完全初始化
            val appSpiderManager = top.cywin.onetv.movie.spider.AppSpiderManager.getInstance()
            appSpiderManager.init(context)
            Log.d(TAG, "✅ AppSpiderManager额外初始化完成")

            // 3. 确保SpiderManager完全初始化
            val spiderManager = top.cywin.onetv.movie.spider.SpiderManager.getInstance()
            // SpiderManager是单例，获取实例即完成初始化
            Log.d(TAG, "✅ SpiderManager额外初始化完成: ${spiderManager.javaClass.simpleName}")

            // 4. 验证BaseLoader是否可用
            val baseLoader = top.cywin.onetv.movie.api.loader.BaseLoader.get()
            Log.d(TAG, "✅ BaseLoader验证完成: ${baseLoader.javaClass.simpleName}")

            Log.d(TAG, "✅ 所有Spider引擎额外初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Spider引擎额外初始化失败", e)
            // 不抛出异常，允许系统继续运行
            Log.w(TAG, "⚠️ Spider引擎额外初始化失败，但系统将继续运行")
        }
    }

}
