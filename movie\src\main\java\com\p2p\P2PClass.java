/**
 * 第三方库模块: JianPian P2P Engine (简片P2P引擎)
 * 原始模块名: jianpian
 * 库包名: com.p2p
 * 功能说明: P2P点对点传输引擎，用于视频流媒体传输
 *
 * 注意: 文件夹名为 p2p 是为了符合 Java 包名规范
 * 实际对应的是 jianpian 第三方库模块
 */
package com.p2p;

import com.github.catvod.utils.Path;

public class P2PClass {

    public int port;

    public P2PClass() {
        System.loadLibrary("jpa");
        this.port = P2Pdoxstarthttpd("TEST3E63BAAECDAA79BEAA91853490A69F08".getBytes(),
                Path.jpa().getAbsolutePath().getBytes());
    }

    public int P2Pdoxstarthttpd(byte[] bArr, byte[] bArr2) {
        return doxstarthttpd(bArr, bArr2);
    }

    public void P2Pdoxstart(byte[] bArr) {
        doxstart(bArr);
    }

    public void P2Pdoxpause(byte[] bArr) {
        doxpause(bArr);
    }

    private native int doxstarthttpd(byte[] bArr, byte[] bArr2);

    private native int doxstart(byte[] bArr);

    private native int doxpause(byte[] bArr);
}
