package top.cywin.onetv.movie.network;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.os.Build;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 网络工具类
 * 基于FongMi_TV架构设计
 */
public class NetworkUtils {
    private static final String TAG = "NetworkUtils";

    /**
     * 检查网络是否可用 - 基于FongMi_TV完整功能，使用现代API
     */
    public static boolean isNetworkAvailable(Context context) {
        if (context == null) {
            return false;
        }

        try {
            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm == null) {
                return false;
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 使用现代API (Android 6.0+)
                Network network = cm.getActiveNetwork();
                if (network == null) {
                    return false;
                }
                NetworkCapabilities capabilities = cm.getNetworkCapabilities(network);
                return capabilities != null &&
                       capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                       capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED) &&
                       (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET));
            } else {
                // 对于旧版本，使用反射或其他方式避免直接使用NetworkInfo
                // 基于FongMi_TV的兼容性处理
                Network[] networks = cm.getAllNetworks();
                for (Network network : networks) {
                    NetworkCapabilities capabilities = cm.getNetworkCapabilities(network);
                    if (capabilities != null &&
                        capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)) {
                        return true;
                    }
                }
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为WiFi网络 - 基于FongMi_TV完整功能，使用现代API
     */
    public static boolean isWifiConnected(Context context) {
        if (context == null) {
            return false;
        }

        try {
            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm == null) {
                return false;
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Network network = cm.getActiveNetwork();
                if (network == null) {
                    return false;
                }
                NetworkCapabilities capabilities = cm.getNetworkCapabilities(network);
                return capabilities != null &&
                       capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) &&
                       capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
            } else {
                // 对于旧版本，检查所有网络
                Network[] networks = cm.getAllNetworks();
                for (Network network : networks) {
                    NetworkCapabilities capabilities = cm.getNetworkCapabilities(network);
                    if (capabilities != null &&
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) &&
                        capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)) {
                        return true;
                    }
                }
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为移动网络 - 基于FongMi_TV完整功能，使用现代API
     */
    public static boolean isMobileConnected(Context context) {
        if (context == null) {
            return false;
        }

        try {
            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm == null) {
                return false;
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Network network = cm.getActiveNetwork();
                if (network == null) {
                    return false;
                }
                NetworkCapabilities capabilities = cm.getNetworkCapabilities(network);
                return capabilities != null &&
                       capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) &&
                       capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
            } else {
                // 对于旧版本，检查所有网络
                Network[] networks = cm.getAllNetworks();
                for (Network network : networks) {
                    NetworkCapabilities capabilities = cm.getNetworkCapabilities(network);
                    if (capabilities != null &&
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) &&
                        capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)) {
                        return true;
                    }
                }
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取网络类型名称 - 基于FongMi_TV完整功能，使用现代API
     */
    public static String getNetworkTypeName(Context context) {
        if (context == null) {
            return "Unknown";
        }

        try {
            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm == null) {
                return "Unknown";
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Network network = cm.getActiveNetwork();
                if (network == null) {
                    return "No Connection";
                }
                NetworkCapabilities capabilities = cm.getNetworkCapabilities(network);
                if (capabilities == null) {
                    return "No Connection";
                }

                if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                    return "WIFI";
                } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                    return "MOBILE";
                } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)) {
                    return "ETHERNET";
                } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH)) {
                    return "BLUETOOTH";
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O &&
                          capabilities.hasTransport(NetworkCapabilities.TRANSPORT_LOWPAN)) {
                    return "LOWPAN";
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
                          capabilities.hasTransport(NetworkCapabilities.TRANSPORT_USB)) {
                    return "USB";
                } else {
                    return "OTHER";
                }
            } else {
                // 对于旧版本，检查所有网络并返回第一个可用的类型
                Network[] networks = cm.getAllNetworks();
                for (Network network : networks) {
                    NetworkCapabilities capabilities = cm.getNetworkCapabilities(network);
                    if (capabilities != null &&
                        capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)) {
                        if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                            return "WIFI";
                        } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                            return "MOBILE";
                        } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)) {
                            return "ETHERNET";
                        }
                    }
                }
                return "No Connection";
            }
        } catch (Exception e) {
            return "Unknown";
        }
    }

    /**
     * 检查域名是否可达
     */
    public static boolean isHostReachable(String host) {
        return isHostReachable(host, 5000);
    }

    /**
     * 检查域名是否可达（指定超时时间）
     */
    public static boolean isHostReachable(String host, int timeout) {
        if (host == null || host.trim().isEmpty()) {
            return false;
        }

        try {
            InetAddress address = InetAddress.getByName(host);
            return address.isReachable(timeout);
        } catch (UnknownHostException e) {
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 解析域名获取IP地址
     */
    public static String resolveHostToIp(String host) {
        if (host == null || host.trim().isEmpty()) {
            return null;
        }

        try {
            InetAddress address = InetAddress.getByName(host);
            return address.getHostAddress();
        } catch (UnknownHostException e) {
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 检查IP地址格式是否有效
     */
    public static boolean isValidIpAddress(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 检查端口号是否有效
     */
    public static boolean isValidPort(int port) {
        return port > 0 && port <= 65535;
    }

    /**
     * 检查URL格式是否有效
     */
    public static boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }

        return url.startsWith("http://") || url.startsWith("https://");
    }
}
