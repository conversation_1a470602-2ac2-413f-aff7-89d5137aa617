package top.cywin.onetv.movie.utils

import android.util.Log
import coil.intercept.Interceptor
import coil.request.ImageRequest
import coil.request.ImageResult
import okhttp3.Headers
import org.json.JSONObject

/**
 * Coil拦截器：处理@Headers格式的图片URL
 * 🎯 目标：提高图片加载成功率，匹配原版FongMi_TV的处理方式
 */
class HeadersInterceptor : Interceptor {
    
    companion object {
        private const val TAG = "HEADERS_INTERCEPTOR"
    }

    override suspend fun intercept(chain: Interceptor.Chain): ImageResult {
        val request = chain.request
        val originalUrl = request.data.toString()

        Log.d(TAG, "🔍 拦截器被调用，URL: $originalUrl")

        // 检查是否包含@Headers、@Referer或@User-Agent参数
        if (originalUrl.contains("@Headers=") || originalUrl.contains("@Referer=") || originalUrl.contains("@User-Agent=")) {
            try {
                Log.d(TAG, "🎯 发现Headers参数，开始处理: $originalUrl")
                val modifiedRequest = processUrlWithHeaders(request, originalUrl)
                Log.d(TAG, "🔧 处理Headers URL完成: $originalUrl")
                return chain.proceed(modifiedRequest)
            } catch (e: Exception) {
                Log.e(TAG, "❌ 处理Headers URL失败: $originalUrl", e)
                // 降级处理：移除所有@参数
                val cleanUrl = originalUrl.split("@")[0]
                val fallbackRequest = request.newBuilder()
                    .data(cleanUrl)
                    .build()
                return chain.proceed(fallbackRequest)
            }
        } else {
            Log.d(TAG, "ℹ️ 无Headers参数，直接处理: $originalUrl")
        }

        return chain.proceed(request)
    }
    
    /**
     * 处理包含各种@参数的URL
     */
    private fun processUrlWithHeaders(request: ImageRequest, originalUrl: String): ImageRequest {
        // 🔧 修复：正确解析URL参数
        val baseUrl = originalUrl.split("@")[0]
        val requestBuilder = request.newBuilder().data(baseUrl)

        // 🔧 修复：使用正则表达式解析参数，处理URL编码
        val refererMatch = Regex("@Referer=([^@]+)").find(originalUrl)
        val userAgentMatch = Regex("@User-Agent=([^@]+)").find(originalUrl)
        val cookieMatch = Regex("@Cookie=([^@]+)").find(originalUrl)
        val headersMatch = Regex("@Headers=([^@]+)").find(originalUrl)

        // 处理Referer
        refererMatch?.let { match ->
            val referer = java.net.URLDecoder.decode(match.groupValues[1], "UTF-8")
            requestBuilder.addHeader("Referer", referer)
            Log.d(TAG, "🔗 添加Referer: $referer")
        }

        // 处理User-Agent
        userAgentMatch?.let { match ->
            val userAgent = java.net.URLDecoder.decode(match.groupValues[1], "UTF-8")
            requestBuilder.addHeader("User-Agent", userAgent)
            Log.d(TAG, "🤖 添加User-Agent: $userAgent")
        }

        // 处理Cookie
        cookieMatch?.let { match ->
            val cookie = java.net.URLDecoder.decode(match.groupValues[1], "UTF-8")
            requestBuilder.addHeader("Cookie", cookie)
            Log.d(TAG, "🍪 添加Cookie: $cookie")
        }

        // 处理Headers JSON
        headersMatch?.let { match ->
            try {
                val headersJson = java.net.URLDecoder.decode(match.groupValues[1], "UTF-8")
                val headers = parseHeaders(headersJson)
                headers.forEach { (key, value) ->
                    requestBuilder.addHeader(key, value)
                }
                Log.d(TAG, "📋 添加Headers: $headers")
            } catch (e: Exception) {
                Log.w(TAG, "⚠️ 解析Headers失败: ${match.groupValues[1]}", e)
            }
        }

        // 🔧 如果没有找到任何Headers，添加默认的Headers来避免403错误
        if (refererMatch == null && userAgentMatch == null && headersMatch == null) {
            requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36")
            requestBuilder.addHeader("Referer", "https://api.douban.com/")
            Log.d(TAG, "🛡️ 添加默认Headers防止403错误")
        }

        Log.d(TAG, "✅ 成功处理URL参数，基础URL: $baseUrl")
        return requestBuilder.build()
    }
    
    /**
     * 解析Headers JSON字符串
     */
    private fun parseHeaders(headersJson: String): Map<String, String> {
        val headers = mutableMapOf<String, String>()
        
        try {
            val jsonObject = JSONObject(headersJson)
            val keys = jsonObject.keys()
            
            while (keys.hasNext()) {
                val key = keys.next()
                val value = jsonObject.getString(key)
                headers[key] = value
            }
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ 解析Headers JSON失败，使用默认Headers: $headersJson", e)
            // 提供默认Headers
            headers["User-Agent"] = "Mozilla/5.0 (Linux; Android 13; V2049A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/116.0.0.0 Mobile Safari/537.36"
        }
        
        return headers
    }
}
