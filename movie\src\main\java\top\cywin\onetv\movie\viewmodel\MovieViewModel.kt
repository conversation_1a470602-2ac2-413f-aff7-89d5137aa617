package top.cywin.onetv.movie.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import top.cywin.onetv.movie.MovieApp
import top.cywin.onetv.movie.bean.Site
import top.cywin.onetv.movie.bean.Class
import top.cywin.onetv.movie.bean.Vod
import android.util.Log

// ✅ 添加EventBus支持
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import top.cywin.onetv.movie.event.*
import top.cywin.onetv.movie.event.MovieIdTransformEvent
import top.cywin.onetv.movie.ui.model.*
import top.cywin.onetv.movie.adapter.ViewModelAdapter

/**
 * 首页UI状态数据类 - 完整版本
 */
data class MovieUiState(
    // 基础状态
    val isLoading: Boolean = false,
    val error: String? = null,
    val loadingMessage: String = "",
    val loadingProgress: Float = 0f,

    // 配置相关
    val isStoreHouseIndex: Boolean = false,
    val storeHouseName: String = "",
    val availableRoutes: List<VodConfigUrl> = emptyList(),
    val selectedRoute: VodConfigUrl? = null,
    val showRouteSelector: Boolean = false,
    val showConfigSetup: Boolean = false,
    val showWelcomeScreen: Boolean = false,

    // 站点相关
    val currentSite: top.cywin.onetv.movie.bean.Site? = null,
    val siteList: List<SiteInfo> = emptyList(),

    // 内容相关
    val categories: List<CategoryInfo> = emptyList(),
    val selectedCategory: CategoryInfo? = null,
    val recommendMovies: List<MovieItem> = emptyList(),
    val homeCategories: List<HomeCategorySection> = emptyList(),
    val hotMovies: List<MovieItem> = emptyList(),
    val newMovies: List<MovieItem> = emptyList(),
    val currentCategoryName: String? = null,  // 当前显示的分类名称
    val categoryMovies: List<MovieItem> = emptyList(),  // 分类电影列表

    // UI控制
    val showSearch: Boolean = false,
    val showSettings: Boolean = false,
    val refreshing: Boolean = false,

    // 网络状态
    val networkState: NetworkState = NetworkState(),

    // 历史记录相关
    val watchHistories: List<WatchHistory> = emptyList(),
    val isLoadingHistory: Boolean = false,

    // 其他状态
    val lastUpdateTime: Long = 0
)

/**
 * OneTV Movie首页ViewModel - 完整版本
 * 通过适配器系统调用FongMi_TV解析功能，完整的事件驱动架构
 */
class MovieViewModel : ViewModel() {

    companion object {
        private const val TAG = "ONETV_MOVIE_VM"
    }

    // ✅ 通过MovieApp访问适配器系统 - 安全的延迟初始化
    private val movieApp: MovieApp by lazy {
        Log.d(TAG, "🔄 [第5阶段] 开始获取MovieApp实例")
        try {
            // ✅ 检查FongMi_TV App类是否已初始化
            val fongmiApp = top.cywin.onetv.movie.App.get()
            if (fongmiApp == null) {
                Log.w(TAG, "⚠️ [第5阶段] FongMi_TV App类未初始化，创建最小化实例")
                // 不等待，直接创建最小化实例，避免阻塞UI
                val tempApp = MovieApp()
                Log.d(TAG, "✅ [第5阶段] 创建最小化MovieApp实例")
                tempApp
            } else {
                val app = MovieApp.getInstance()
                Log.d(TAG, "✅ [第5阶段] MovieApp实例获取成功")
                app
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ [第5阶段] MovieApp实例获取失败，创建临时实例", e)
            // 创建临时实例，避免完全崩溃
            MovieApp()
        }
    }
    private val repositoryAdapter by lazy {
        Log.d(TAG, "🔄 [第5阶段] 获取RepositoryAdapter")
        movieApp.repositoryAdapter
    }
    private val viewModelAdapter by lazy {
        Log.d(TAG, "🔄 [第5阶段] 获取ViewModelAdapter")
        movieApp.viewModelAdapter
    }
    private val vodConfig by lazy {
        Log.d(TAG, "🔄 [第5阶段] 获取VodConfig")
        try {
            movieApp.vodConfig
        } catch (e: Exception) {
            Log.e(TAG, "❌ [第5阶段] 获取VodConfig失败，使用默认实例", e)
            // 创建一个默认的VodConfig实例，避免崩溃
            top.cywin.onetv.movie.api.config.VodConfig()
        }
    }

    private val _uiState = MutableStateFlow(MovieUiState())
    val uiState: StateFlow<MovieUiState> = _uiState.asStateFlow()

    // 导航事件
    private val _navigationEvent = MutableStateFlow<NavigationEvent?>(null)
    val navigationEvent: StateFlow<NavigationEvent?> = _navigationEvent.asStateFlow()

    /**
     * 🔥 原版FongMi_TV直接导航支持：设置NavController
     */
    fun setNavController(navController: androidx.navigation.NavController) {
        repositoryAdapter.setNavController(navController)
        Log.d(TAG, "🔥 [原版直接导航] NavController已传递给RepositoryAdapter")
    }

    // 清理导航事件
    fun clearNavigationEvent() {
        Log.d(TAG, "🎯 [电影ID跟踪] 清理导航事件")
        _navigationEvent.value = null
    }

    // ✅ 防止EventBus事件循环的标志
    private var isLoadingHomeData = false
    private var isHandlingConfigUpdate = false

    init {
        Log.d(TAG, "🎬 [第5阶段] MovieViewModel开始初始化")
        Log.d(TAG, "📍 位置: MovieViewModel.kt:114")
        Log.d(TAG, "⏰ 时间戳: ${System.currentTimeMillis()}")

        try {
            // ✅ 强制触发lazy初始化，逐步检查每个组件
            Log.d(TAG, "🔄 [第5阶段] 开始初始化MovieApp")
            val app = movieApp // 触发movieApp的lazy初始化
            Log.d(TAG, "✅ [第5阶段] MovieApp初始化完成: ${app.javaClass.simpleName}")

            Log.d(TAG, "🔄 [第5阶段] 开始初始化RepositoryAdapter")
            val repo = repositoryAdapter // 触发repositoryAdapter的lazy初始化
            Log.d(TAG, "✅ [第5阶段] RepositoryAdapter初始化完成: ${repo.javaClass.simpleName}")

            Log.d(TAG, "🔄 [第5阶段] 开始初始化ViewModelAdapter")
            val viewAdapter = viewModelAdapter // 触发viewModelAdapter的lazy初始化
            Log.d(TAG, "✅ [第5阶段] ViewModelAdapter初始化完成: ${viewAdapter.javaClass.simpleName}")

            Log.d(TAG, "🔄 [第5阶段] 开始初始化VodConfig")
            val config = vodConfig // 触发vodConfig的lazy初始化
            Log.d(TAG, "✅ [第5阶段] VodConfig初始化完成: ${config.javaClass.simpleName}")

            // ✅ 注册EventBus监听FongMi_TV事件
            Log.d(TAG, "🔗 [第5阶段] 注册EventBus事件监听")
            EventBus.getDefault().register(this)
            Log.d(TAG, "✅ [第5阶段] EventBus注册成功")

            // ✅ 不在init中自动加载首页数据，等待UI准备好后手动调用
            Log.d(TAG, "🚀 [第5阶段] ViewModel初始化完成，等待UI调用loadHomeData")
            // loadHomeData() // 移除自动调用

        } catch (e: Exception) {
            Log.e(TAG, "❌ [第5阶段] MovieViewModel初始化失败", e)
            Log.e(TAG, "❌ [第5阶段] 异常详情: ${e.javaClass.simpleName}: ${e.message}")
            e.printStackTrace()
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "初始化失败: ${e.message}"
            )
        }
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "🧹 MovieViewModel 清理")

        // ✅ 取消EventBus注册
        try {
            EventBus.getDefault().unregister(this)
        } catch (e: Exception) {
            Log.e(TAG, "EventBus取消注册失败", e)
        }
    }

    // ===== EventBus事件监听 =====

    /**
     * 监听电影ID转换事件 - 处理导航逻辑
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMovieIdTransform(event: MovieIdTransformEvent) {
        Log.d(TAG, "📡 [FongMi_TV兼容] 收到ID转换事件: success=${event.isSuccess}")

        if (event.isSuccess && event.realVodId != null) {
            Log.d(TAG, "✅ [FongMi_TV兼容] ID转换成功，开始导航: ${event.movieName} -> ${event.realVodId}")

            // 发送导航事件到UI层
            _navigationEvent.value = top.cywin.onetv.movie.event.NavigationEvent.NavigateToDetail(
                event.realVodId,
                event.siteKey,
                event.movieName
            )
        } else {
            Log.e(TAG, "❌ [FongMi_TV兼容] ID转换失败: ${event.error}")
            // 可以在这里显示错误提示
        }
    }

    /**
     * 监听搜索结果列表导航事件 - 处理推荐电影点击后的导航
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNavigateToSearchResultList(event: top.cywin.onetv.movie.event.NavigationEvent.NavigateToSearchResultList) {
        Log.d(TAG, "📡 [修改逻辑] 收到搜索结果列表导航事件: keyword=${event.keyword}, count=${event.results.size}")

        // 转发导航事件到UI层
        _navigationEvent.value = event

        Log.d(TAG, "✅ [修改逻辑] 搜索结果列表导航事件已转发到UI层")
    }

    /**
     * 监听配置更新事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onConfigUpdate(event: ConfigUpdateEvent) {
        Log.d(TAG, "📡 收到配置更新事件: success=${event.isSuccess}")

        // ✅ 防止循环处理
        if (isHandlingConfigUpdate) {
            Log.w(TAG, "⚠️ 正在处理配置更新，跳过重复事件")
            return
        }

        // ✅ 允许成功的配置更新事件和WELCOME_SCREEN事件在加载过程中正常处理
        if (isLoadingHomeData && !event.isSuccess && event.errorMessage != "WELCOME_SCREEN") {
            Log.w(TAG, "⚠️ 正在加载首页数据，跳过非成功且非欢迎界面的配置更新事件")
            return
        }

        if (event.isSuccess && event.config != null) {
            Log.d(TAG, "✅ 配置更新成功，处理配置")
            isHandlingConfigUpdate = true
            try {
                // ✅ 确保在主线程更新UI状态
                viewModelScope.launch(Dispatchers.Main.immediate) {
                    // ✅ 先重置加载标志
                    isLoadingHomeData = false

                    // ✅ 隐藏欢迎界面
                    _uiState.value = _uiState.value.copy(showWelcomeScreen = false)

                    Log.d(TAG, "🔄 [第7阶段] 配置加载成功，重置加载标志")
                }

                handleConfigUpdateSuccess(event.config)
            } finally {
                isHandlingConfigUpdate = false
            }
        } else {
            Log.e(TAG, "❌ 配置更新失败: ${event.errorMessage}")

            // ✅ 检查是否是欢迎界面事件
            if (event.errorMessage == "WELCOME_SCREEN") {
                Log.d(TAG, "🎉 显示欢迎界面")
                Log.d(TAG, "🔄 [第7阶段] 更新UI状态为欢迎界面")
                Log.d(TAG, "🔄 [第7阶段] 当前线程: ${Thread.currentThread().name}")

                // ✅ 修复状态更新逻辑 - 确保在主线程更新UI状态，添加异常处理
                viewModelScope.launch(Dispatchers.Main.immediate) {
                    try {
                        Log.d(TAG, "🔄 [第7阶段] 在主线程中更新UI状态")

                        // ✅ 先重置加载标志，避免状态冲突
                        isLoadingHomeData = false
                        Log.d(TAG, "🔄 [第7阶段] 重置加载标志完成")

                        // ✅ 创建新状态，确保所有相关字段都正确设置
                        val currentUiState = _uiState.value
                        Log.d(TAG, "🔄 [第7阶段] 当前状态: isLoading=${currentUiState.isLoading}, showWelcomeScreen=${currentUiState.showWelcomeScreen}")

                        val newState = currentUiState.copy(
                            isLoading = false,
                            error = null,
                            showWelcomeScreen = true,
                            loadingMessage = "", // 清空加载消息
                            showConfigSetup = false // 确保配置设置界面不显示
                        )
                        Log.d(TAG, "🔄 [第7阶段] 新状态创建完成: isLoading=${newState.isLoading}, showWelcomeScreen=${newState.showWelcomeScreen}")

                        // ✅ 强制更新状态，移除delay避免协程中断
                        _uiState.value = newState
                        Log.d(TAG, "🔄 [第7阶段] 状态更新完成")

                        // ✅ 立即验证状态更新，不使用delay
                        val verifyState = _uiState.value
                        Log.d(TAG, "✅ [第7阶段] UI状态更新完成 - showWelcomeScreen: ${newState.showWelcomeScreen}")
                        Log.d(TAG, "✅ [第7阶段] 当前UI状态: isLoading=${verifyState.isLoading}, error=${verifyState.error}, showWelcomeScreen=${verifyState.showWelcomeScreen}")
                        Log.d(TAG, "🔍 [第7阶段] 状态验证: showWelcomeScreen=${verifyState.showWelcomeScreen}, isLoading=${verifyState.isLoading}, error=${verifyState.error}")

                        // ✅ 如果状态不一致，强制再次更新
                        if (verifyState.showWelcomeScreen != newState.showWelcomeScreen) {
                            Log.w(TAG, "⚠️ [第7阶段] 状态不一致，强制再次更新")
                            _uiState.value = newState.copy()

                            // ✅ 再次验证，不使用delay
                            val finalState = _uiState.value
                            Log.d(TAG, "🔍 [第7阶段] 最终状态验证: showWelcomeScreen=${finalState.showWelcomeScreen}")
                        }

                        Log.d(TAG, "✅ [第7阶段] 欢迎界面状态更新流程完成")
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ [第7阶段] 状态更新过程中发生异常", e)
                        // ✅ 异常情况下仍然尝试设置欢迎界面状态
                        try {
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = null,
                                showWelcomeScreen = true
                            )
                            Log.d(TAG, "✅ [第7阶段] 异常恢复：强制设置欢迎界面状态")
                        } catch (e2: Exception) {
                            Log.e(TAG, "❌ [第7阶段] 异常恢复失败", e2)
                        }
                    }
                }
            } else {
                Log.d(TAG, "🔄 [第7阶段] 更新UI状态为错误界面: ${event.errorMessage}")

                // ✅ 确保在主线程更新UI状态
                viewModelScope.launch(Dispatchers.Main.immediate) {
                    // ✅ 先重置加载标志
                    isLoadingHomeData = false

                    val newState = _uiState.value.copy(
                        isLoading = false,
                        error = event.errorMessage ?: "配置更新失败",
                        showWelcomeScreen = false
                    )

                    // ✅ 强制更新状态
                    _uiState.value = newState

                    Log.d(TAG, "✅ [第7阶段] UI状态更新完成 - error: ${newState.error}")
                    Log.d(TAG, "✅ [第7阶段] 当前UI状态: isLoading=${newState.isLoading}, error=${newState.error}, showWelcomeScreen=${newState.showWelcomeScreen}")
                }
            }
        }
    }

    /**
     * 监听首页内容事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onHomeContent(event: HomeContentEvent) {
        Log.d(TAG, "📡 收到首页内容事件: success=${event.isSuccess}")
        Log.d(TAG, "📊 分类数量: ${event.categories.size}")
        Log.d(TAG, "📊 推荐内容数量: ${event.recommendVods.size}")

        if (event.isSuccess) {
            handleHomeContentSuccess(event.categories, event.recommendVods)
        } else {
            Log.e(TAG, "❌ 首页内容加载失败")
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "首页内容加载失败"
            )
        }
    }

    /**
     * 监听分类内容事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onCategoryContent(event: CategoryContentEvent) {
        Log.d(TAG, "📡 收到分类内容: typeId=${event.typeId}, count=${event.vods.size}")

        handleCategoryContentUpdate(event)
    }

    /**
     * 监听搜索结果事件（用于推荐内容）
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSearchResult(event: SearchResultEvent) {
        Log.d(TAG, "📡 收到搜索结果: keyword=${event.keyword}, count=${event.results.size}")

        // 如果是首页推荐搜索（空关键词或特定关键词）
        if (event.keyword.isEmpty() || event.keyword == "推荐") {
            val movieItems = event.results.map { vod ->
                ViewModelAdapter.convertVodToMovie(vod)
            }.filterNotNull()

            _uiState.value = _uiState.value.copy(
                recommendMovies = movieItems
            )
        }
    }

    /**
     * 监听站点变更事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSiteChange(event: SiteChangeEvent) {
        Log.d(TAG, "📡 收到站点变更事件: success=${event.isSuccess}")

        if (event.isSuccess && event.site != null) {
            _uiState.value = _uiState.value.copy(
                currentSite = event.site
            )

            // ✅ 重新启用站点变更后的内容加载，添加防循环机制
            if (!isLoadingHomeData && !isHandlingConfigUpdate) {
                Log.d(TAG, "🔄 站点变更后重新加载首页内容")
                viewModelScope.launch {
                    try {
                        // ✅ 直接加载站点内容，不触发配置重新加载
                        repositoryAdapter.getCategories()
                        repositoryAdapter.getRecommendContent()
                        Log.d(TAG, "✅ 站点变更后内容加载请求已发送")
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 站点变更后内容加载失败", e)
                    }
                }
            } else {
                Log.w(TAG, "⚠️ 正在加载数据或处理配置更新，跳过站点变更重载")
            }
        }
    }

    // ✅ 防止错误事件循环的标志
    private var isHandlingError = false
    private var lastErrorTime = 0L
    private var lastErrorMessage = ""

    // 🔥 删除：ContentDetailEvent处理已移至RepositoryAdapter
    // 现在按照项目架构：FongMi_TV → EventBus → RepositoryAdapter → NavigationEvent → ViewModel

    /**
     * 监听错误事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onError(event: ErrorEvent) {
        val errorMsg = event.msg ?: "未知错误"
        val currentTime = System.currentTimeMillis()

        // ✅ 防止错误事件循环
        if (isHandlingError) {
            Log.w(TAG, "⚠️ 正在处理错误事件，跳过重复事件")
            return
        }

        // ✅ 防止相同错误在短时间内重复处理
        if (errorMsg == lastErrorMessage && (currentTime - lastErrorTime) < 1000) {
            Log.w(TAG, "⚠️ 相同错误在1秒内重复，跳过处理: $errorMsg")
            return
        }

        // ✅ 过滤空错误消息
        if (errorMsg.isBlank() || errorMsg == "null") {
            Log.w(TAG, "⚠️ 跳过空错误消息")
            return
        }

        isHandlingError = true
        lastErrorTime = currentTime
        lastErrorMessage = errorMsg

        try {
            Log.e(TAG, "📡 收到错误事件: $errorMsg")
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = errorMsg
            )
        } finally {
            isHandlingError = false
        }
    }

    // ===== 公共方法 =====

    /**
     * 加载首页数据 - 通过适配器调用FongMi_TV解析系统
     */
    fun loadHomeData() {
        Log.d(TAG, "🎬 [第6阶段] loadHomeData方法被调用")
        Log.d(TAG, "📍 位置: MovieViewModel.kt:235")
        Log.d(TAG, "⏰ 时间戳: ${System.currentTimeMillis()}")

        // ✅ 防止重复加载
        if (isLoadingHomeData) {
            Log.w(TAG, "⚠️ 正在加载首页数据，跳过重复调用")
            return
        }

        // ✅ 检查是否已有缓存数据，避免重复加载
        val currentState = _uiState.value
        if (!currentState.categories.isEmpty() && !currentState.homeCategories.isEmpty()) {
            Log.d(TAG, "✅ [第6阶段] 首页数据已缓存，分类数: ${currentState.categories.size}，跳过重复加载")
            return
        }

        isLoadingHomeData = true
        viewModelScope.launch {
            try {
                Log.d(TAG, "🔄 [第6阶段] 开始在协程中加载首页数据")

                // ✅ 修复状态管理逻辑 - 检查当前是否已经在显示欢迎界面
                val currentState = _uiState.value
                if (currentState.showWelcomeScreen) {
                    Log.d(TAG, "⚠️ [第6阶段] 当前已显示欢迎界面，跳过状态重置")
                    // 如果已经在显示欢迎界面，不要重置状态
                    return@launch
                } else {
                    // ✅ 只有在非欢迎界面状态时才设置加载状态
                    _uiState.value = _uiState.value.copy(
                        isLoading = true,
                        error = null,
                        loadingMessage = "正在加载配置...",
                        showWelcomeScreen = false // 明确设置为false
                    )
                }

                // ✅ 通过适配器加载配置，添加详细的异常处理
                Log.d(TAG, "🔄 [第6阶段] 开始加载配置文件")
                try {
                    Log.d(TAG, "🔄 [第6阶段] RepositoryAdapter实例: ${repositoryAdapter.javaClass.simpleName}")
                    Log.d(TAG, "🔄 [第6阶段] 调用repositoryAdapter.loadConfig()")
                    repositoryAdapter.loadConfig()
                    Log.d(TAG, "✅ [第6阶段] repositoryAdapter.loadConfig()调用成功")
                } catch (e: Exception) {
                    Log.e(TAG, "❌ [第6阶段] repositoryAdapter.loadConfig()调用失败", e)
                    // ✅ 如果调用失败，直接发送欢迎界面事件
                    EventBus.getDefault().post(ConfigUpdateEvent(vodConfig, false, "WELCOME_SCREEN"))
                }

                // ✅ 配置加载是异步的，通过EventBus接收结果
                // 不在这里直接检查sites，而是等待ConfigUpdateEvent
                Log.d(TAG, "🔄 [第6阶段] 配置加载请求已发送，等待EventBus事件")

                // ✅ 更新UI状态为等待配置加载
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    error = null,
                    loadingMessage = "正在加载配置文件..."
                )

                // ✅ 配置加载完成后的处理将在ConfigUpdateEvent中进行
                Log.d(TAG, "✅ [第6阶段] 配置加载流程启动完成")

            } catch (e: Exception) {
                Log.e(TAG, "💥 首页数据加载失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "首页数据加载失败: ${e.message}"
                )
            } finally {
                // ✅ 配置加载是异步的，不在这里重置标志
                // 标志将在配置更新事件处理完成后重置
                Log.d(TAG, "🔄 [第6阶段] 首页数据加载请求完成，等待配置更新事件")
            }
        }
    }

    /**
     * 检查是否为仓库索引文件
     */
    private fun checkIfStoreHouseIndex(config: top.cywin.onetv.movie.api.config.VodConfig): Boolean {
        return config.sites.any { site ->
            site.name?.contains("仓库") == true ||
            site.api?.contains("index") == true
        }
    }

    /**
     * 处理仓库索引文件
     */
    private suspend fun handleStoreHouseIndex(config: top.cywin.onetv.movie.api.config.VodConfig) {
        Log.d("ONETV_MOVIE", "🏪 检测到仓库索引文件")

        // 获取配置URL列表
        val configUrls = config.sites.map { site ->
            VodConfigUrl(
                name = site.name ?: "未知线路",
                url = site.api ?: ""
            )
        }

        Log.d("ONETV_MOVIE", "📋 可用线路数: ${configUrls.size}")

        // 设置仓库索引状态
        _uiState.value = _uiState.value.copy(
            isLoading = false,
            isStoreHouseIndex = true,
            storeHouseName = "默认仓库",
            availableRoutes = configUrls,
            showRouteSelector = false,
            error = null
        )

        // 自动选择第一条线路
        if (configUrls.isNotEmpty()) {
            selectRoute(configUrls[0])
        }
    }

    /**
     * 加载正常配置
     */
    private suspend fun loadNormalConfig(config: top.cywin.onetv.movie.api.config.VodConfig) {
        Log.d(TAG, "📋 加载正常配置，站点数: ${config.sites.size}")

        // 获取当前站点
        val currentSite = config.home
        Log.d(TAG, "🏠 当前站点: ${currentSite?.name}")

        // ✅ 转换站点列表为UI数据
        val siteInfos = config.sites.map { site ->
            ViewModelAdapter.convertSiteToSiteInfo(site)
        }.filterNotNull()

        // ✅ 更新UI状态为加载完成，但保留现有的内容数据
        _uiState.value = _uiState.value.copy(
            isLoading = false,
            currentSite = currentSite,
            siteList = siteInfos,
            error = null,
            loadingMessage = ""
            // 不重置categories和recommendMovies，保留现有数据
        )

        Log.d(TAG, "✅ 正常配置加载完成，UI状态已更新")
        Log.d(TAG, "📊 当前UI状态: isLoading=${_uiState.value.isLoading}, error=${_uiState.value.error}")
        Log.d(TAG, "📊 站点数量: ${siteInfos.size}, 当前站点: ${currentSite?.name}")

        // ✅ 配置加载完成后，立即加载内容
        Log.d(TAG, "🔄 配置加载完成，开始加载首页内容（包含分类和推荐）")
        try {
            // ✅ 修复：只调用一次homeContent，它会同时返回分类和推荐内容
            repositoryAdapter.getCategories()
            Log.d(TAG, "✅ 首页内容加载请求已发送")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 内容加载请求失败", e)
        }
    }

    /**
     * 加载分类内容 - 在主页显示分类内容
     */
    fun loadCategoryContent(typeId: String, typeName: String) {
        Log.d(TAG, "📂 [修复逻辑] 加载分类内容: typeId=$typeId, typeName=$typeName")

        // 🔥 修复逻辑：不显示加载状态，直接设置分类名称和清空电影列表
        _uiState.value = _uiState.value.copy(
            isLoading = false,  // 不显示加载状态
            error = null,
            currentCategoryName = typeName,
            categoryMovies = emptyList()  // 清空之前的分类电影
        )

        try {
            // 通过RepositoryAdapter加载分类内容
            repositoryAdapter.getContentList(typeId, 1, emptyMap())
            Log.d(TAG, "✅ [修复逻辑] 分类内容加载请求已发送")
        } catch (e: Exception) {
            Log.e(TAG, "❌ [修复逻辑] 分类内容加载失败", e)
            _uiState.value = _uiState.value.copy(
                error = "分类内容加载失败: ${e.message}"
            )
        }
    }

    /**
     * 清除分类内容，返回主页
     */
    fun clearCategoryContent() {
        Log.d(TAG, "🏠 [修复逻辑] 清除分类内容，返回主页")
        _uiState.value = _uiState.value.copy(
            currentCategoryName = null,
            categoryMovies = emptyList()
        )
    }

    /**
     * 处理电影点击事件 - 实现原版VideoActivity的ID转换逻辑
     */
    fun onMovieClick(movie: Vod) {
        Log.d(TAG, "🎬 [FongMi_TV兼容] 电影点击: ${movie.vodName}")
        Log.d(TAG, "📊 [FongMi_TV兼容] 电影信息 - vodId: ${movie.vodId}, siteKey: ${movie.siteKey}")

        // 通过适配器处理点击事件（包含ID转换）
        repositoryAdapter.handleMovieClick(
            movie.vodId,
            movie.vodName,
            movie.siteKey
        )
    }

    /**
     * 刷新配置和内容
     */
    fun refresh() {
        Log.d("ONETV_MOVIE", "🔄 用户触发刷新，强制更新配置")
        viewModelScope.launch {
            // ✅ 保留showWelcomeScreen状态，避免被重置
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null
                // showWelcomeScreen保持不变
            )

            try {
                // ✅ 通过适配器强制刷新配置 - 解析逻辑在FongMi_TV中
                repositoryAdapter.refreshConfig()
                Log.d("ONETV_MOVIE", "✅ 配置刷新请求已发送，重新加载首页数据")
                loadHomeData()
            } catch (e: Exception) {
                Log.e("ONETV_MOVIE", "💥 刷新过程异常", e)
                loadHomeData() // 降级处理
            }
        }
    }

    /**
     * 设置欢迎界面状态
     */
    fun setWelcomeScreenState(showWelcome: Boolean) {
        Log.d(TAG, "🔄 设置欢迎界面状态: $showWelcome")
        viewModelScope.launch(Dispatchers.Main.immediate) {
            try {
                val currentState = _uiState.value
                if (currentState.showWelcomeScreen != showWelcome) {
                    val newState = currentState.copy(
                        showWelcomeScreen = showWelcome,
                        isLoading = false,
                        error = null
                    )
                    _uiState.value = newState
                    Log.d(TAG, "✅ 欢迎界面状态更新完成: showWelcomeScreen=$showWelcome")
                } else {
                    Log.d(TAG, "⚠️ 欢迎界面状态已经是: $showWelcome，跳过更新")
                }
            } catch (e: Exception) {
                Log.e(TAG, "❌ 设置欢迎界面状态失败", e)
            }
        }
    }

    /**
     * 选择仓库线路
     */
    fun selectRoute(routeUrl: VodConfigUrl) {
        Log.d("ONETV_MOVIE", "🔗 用户选择线路: ${routeUrl.name}")
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                // ✅ 通过适配器解析选中的线路配置 - 解析逻辑在FongMi_TV中
                repositoryAdapter.parseRouteConfig(routeUrl.url)

                // 等待配置加载完成
                delay(1000)

                // 获取当前配置
                val config = vodConfig
                if (config.sites.isEmpty()) {
                    Log.e("ONETV_MOVIE", "线路解析失败")
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "线路解析失败",
                        showRouteSelector = false
                    )
                    return@launch
                }

                Log.d("ONETV_MOVIE", "✅ 线路解析成功: 站点=${config.sites.size}个")

                // ✅ 重新加载首页数据
                loadNormalConfig(config)

                _uiState.value = _uiState.value.copy(
                    showRouteSelector = false,
                    selectedRoute = routeUrl
                )

            } catch (e: Exception) {
                Log.e("ONETV_MOVIE", "线路切换失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "线路切换失败: ${e.message}",
                    showRouteSelector = false
                )
            }
        }
    }

    /**
     * 显示线路选择器
     */
    fun showRouteSelector() {
        _uiState.value = _uiState.value.copy(showRouteSelector = true)
    }

    /**
     * 隐藏线路选择器
     */
    fun hideRouteSelector() {
        _uiState.value = _uiState.value.copy(showRouteSelector = false)
    }

    // ===== 新增功能方法 =====



    /**
     * 加载收藏列表
     */
    fun loadFavorites() {
        Log.d(TAG, "❤️ 开始加载收藏列表")
        viewModelScope.launch {
            try {
                repositoryAdapter.getFavoriteList()
                Log.d(TAG, "✅ 收藏列表加载请求已发送")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 加载收藏列表失败", e)
            }
        }
    }

    /**
     * 加载站点列表
     */
    fun loadSiteList() {
        Log.d(TAG, "🌐 开始加载站点列表")
        viewModelScope.launch {
            try {
                repositoryAdapter.getSiteList()
                Log.d(TAG, "✅ 站点列表加载请求已发送")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 加载站点列表失败", e)
            }
        }
    }

    /**
     * 切换站点
     */
    fun switchSite(siteKey: String) {
        Log.d(TAG, "🔄 开始切换站点: $siteKey")
        viewModelScope.launch {
            try {
                repositoryAdapter.switchSite(siteKey)
                Log.d(TAG, "✅ 站点切换请求已发送")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 切换站点失败", e)
            }
        }
    }

    /**
     * 加载推荐内容
     */
    fun loadRecommendContent() {
        viewModelScope.launch {
            try {
                Log.d("ONETV_MOVIE", "🌟 加载推荐内容")

                // ✅ 通过适配器获取推荐内容
                repositoryAdapter.getRecommendContent()

                Log.d("ONETV_MOVIE", "✅ 推荐内容请求已发送")

            } catch (e: Exception) {
                Log.e("ONETV_MOVIE", "推荐内容加载失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "推荐内容加载失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 加载分类列表
     */
    fun loadCategories() {
        viewModelScope.launch {
            try {
                Log.d("ONETV_MOVIE", "📂 加载分类列表")

                // ✅ 通过适配器获取分类列表
                repositoryAdapter.getCategories()

                Log.d("ONETV_MOVIE", "✅ 分类列表请求已发送")

            } catch (e: Exception) {
                Log.e("ONETV_MOVIE", "分类列表加载失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "分类列表加载失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        viewModelScope.launch {
            try {
                Log.d("ONETV_MOVIE", "🔄 刷新数据")

                // ✅ 保留showWelcomeScreen状态，避免被重置
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    error = null
                    // showWelcomeScreen保持不变
                )

                // ✅ 通过适配器刷新配置
                repositoryAdapter.refreshConfig()

                // 重新加载首页数据
                loadHomeData()

                Log.d("ONETV_MOVIE", "✅ 数据刷新完成")

            } catch (e: Exception) {
                Log.e("ONETV_MOVIE", "数据刷新失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "数据刷新失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清空缓存
     */
    fun clearCache() {
        viewModelScope.launch {
            try {
                Log.d("ONETV_MOVIE", "🗑️ 清空缓存")

                // ✅ 通过适配器清空缓存
                repositoryAdapter.clearAllCache()

                Log.d("ONETV_MOVIE", "✅ 缓存清空完成")

            } catch (e: Exception) {
                Log.e("ONETV_MOVIE", "缓存清空失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "缓存清空失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    // ===== 私有方法 =====

    /**
     * 处理配置更新成功
     */
    private fun handleConfigUpdateSuccess(config: top.cywin.onetv.movie.api.config.VodConfig) {
        Log.d(TAG, "✅ 处理配置更新成功")

        // ✅ 检查配置是否有效
        if (config.sites.isEmpty()) {
            Log.w(TAG, "⚠️ 配置文件中没有站点数据")
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "配置文件无效，没有可用的站点"
            )
            return
        }

        Log.d(TAG, "✅ 配置文件加载成功，站点数: ${config.sites.size}")

        // ✅ 检查是否为仓库索引文件
        val isStoreHouse = checkIfStoreHouseIndex(config)
        if (isStoreHouse) {
            Log.d(TAG, "🏪 检测到仓库索引文件")
            viewModelScope.launch {
                handleStoreHouseIndex(config)
            }
            return
        }

        // ✅ 加载正常配置
        Log.d(TAG, "📺 加载正常配置文件")
        viewModelScope.launch {
            loadNormalConfig(config)

            // ✅ 强制触发UI更新
            Log.d(TAG, "🔄 强制触发UI状态更新")
            val currentState = _uiState.value
            _uiState.value = currentState.copy(
                isLoading = false,
                error = null,
                loadingMessage = ""
            )
            Log.d(TAG, "✅ UI状态强制更新完成: isLoading=${_uiState.value.isLoading}")
        }

        // ✅ 重置配置更新标志
        isHandlingConfigUpdate = false
        Log.d(TAG, "✅ 配置更新处理完成，重置标志")
    }

    /**
     * 处理首页内容成功
     */
    private fun handleHomeContentSuccess(
        categories: List<top.cywin.onetv.movie.bean.Class>,
        recommendVods: List<top.cywin.onetv.movie.bean.Vod>
    ) {
        Log.d(TAG, "✅ 处理首页内容成功: 分类${categories.size}个, 推荐${recommendVods.size}个")

        // 转换分类数据
        val categoryInfos = categories.map { clazz ->
            Log.d(TAG, "🔄 转换分类: ${clazz.typeName} (${clazz.typeId})")
            ViewModelAdapter.convertClassToCategory(clazz)
        }.filterNotNull()

        // 转换推荐电影数据
        val recommendMovies = recommendVods.map { vod ->
            Log.d(TAG, "🔄 转换推荐电影: ${vod.vodName}")
            ViewModelAdapter.convertVodToMovie(vod)
        }.filterNotNull()

        Log.d(TAG, "📊 转换后分类数量: ${categoryInfos.size}")
        Log.d(TAG, "📊 转换后推荐电影数量: ${recommendMovies.size}")

        _uiState.value = _uiState.value.copy(
            categories = categoryInfos,
            recommendMovies = recommendMovies,
            isLoading = false,
            error = null
        )

        Log.d(TAG, "✅ UI状态已更新")

        // 加载各分类的内容
        categories.take(6).forEach { category ->
            Log.d(TAG, "🔄 加载分类内容: ${category.typeName}")
            repositoryAdapter.getContentList(category.typeId, 1, emptyMap())
        }
    }

    /**
     * 处理分类内容更新
     */
    private fun handleCategoryContentUpdate(event: CategoryContentEvent) {
        // 转换电影数据
        val movieItems = event.vods.map { vod ->
            ViewModelAdapter.convertVodToMovie(vod)
        }.filterNotNull()

        // 🔥 修复逻辑：检查是否是分类点击触发的内容加载
        val currentCategoryName = _uiState.value.currentCategoryName
        if (currentCategoryName != null) {
            // 这是分类点击触发的，显示在主要区域
            Log.d(TAG, "📂 [修复逻辑] 分类点击内容更新: $currentCategoryName, 电影数: ${movieItems.size}")

            val existingMovies = if (event.page == 1) emptyList() else _uiState.value.categoryMovies
            val allMovies = existingMovies + movieItems

            _uiState.value = _uiState.value.copy(
                isLoading = false,
                categoryMovies = allMovies,
                error = null
            )
        } else {
            // 这是正常的首页分类内容加载
            val currentCategories = _uiState.value.homeCategories.toMutableList()
            val existingIndex = currentCategories.indexOfFirst { it.categoryId == event.typeId }

            if (existingIndex >= 0) {
                // 更新现有分类
                val existingCategory = currentCategories[existingIndex]
                currentCategories[existingIndex] = existingCategory.copy(
                    movies = if (event.page == 1) movieItems else existingCategory.movies + movieItems,
                    hasMore = event.hasMore,
                    isLoading = false
                )
            } else {
                // 添加新分类 - 通过RepositoryAdapter获取分类信息
                val categoryName = repositoryAdapter.getCategoryName(event.typeId) ?: "未知分类"
                currentCategories.add(
                    HomeCategorySection(
                        categoryId = event.typeId,
                        categoryName = categoryName,
                        movies = movieItems,
                        hasMore = event.hasMore,
                        isLoading = false
                    )
                )
            }

            _uiState.value = _uiState.value.copy(
                homeCategories = currentCategories
            )
        }
    }







    // ===== 历史记录管理方法 =====

    /**
     * 加载观看历史
     */
    fun loadWatchHistory() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoadingHistory = true, error = null)

                val histories = withContext(Dispatchers.IO) {
                    // ✅ 通过FongMi_TV的History系统获取历史记录
                    val historyList = top.cywin.onetv.movie.bean.History.get()
                    historyList.map { history ->
                        WatchHistory(
                            vodId = history.key,
                            vodName = history.vodName,
                            siteKey = history.cid.toString(),
                            episodeName = history.vodRemarks ?: "",
                            position = history.position,
                            duration = history.duration,
                            watchTime = history.createTime,
                            isCompleted = history.position >= history.duration * 0.9
                        )
                    }
                }

                _uiState.value = _uiState.value.copy(
                    isLoadingHistory = false,
                    watchHistories = histories,
                    error = null
                )

                Log.d(TAG, "✅ 观看历史加载完成: ${histories.size}条")

            } catch (e: Exception) {
                Log.e(TAG, "💥 观看历史加载失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoadingHistory = false,
                    error = "加载观看历史失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 删除单条观看历史
     */
    fun deleteWatchHistory(vodId: String) {
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    // ✅ 通过FongMi_TV的History系统删除历史记录
                    val history = top.cywin.onetv.movie.bean.History.find(vodId)
                    history?.delete()
                }

                // 重新加载历史记录
                loadWatchHistory()

                Log.d(TAG, "✅ 观看历史删除成功: $vodId")

            } catch (e: Exception) {
                Log.e(TAG, "💥 观看历史删除失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "删除观看历史失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清空所有观看历史
     */
    fun clearAllWatchHistory() {
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    // ✅ 通过FongMi_TV的History系统清空所有历史记录
                    top.cywin.onetv.movie.bean.History.delete(top.cywin.onetv.movie.api.config.VodConfig.getCid())
                }

                _uiState.value = _uiState.value.copy(
                    watchHistories = emptyList(),
                    error = null
                )

                Log.d(TAG, "✅ 所有观看历史清空成功")

            } catch (e: Exception) {
                Log.e(TAG, "💥 清空观看历史失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "清空观看历史失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 🔥 原版FongMi_TV直接导航：移除StateFlow导航机制
     * 现在采用原版直接导航：RepositoryAdapter → 直接navController.navigate()
     *
     * 注释掉原有的NavigationEvent监听，因为现在RepositoryAdapter直接处理导航
     */
    /*
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNavigationEvent(event: NavigationEvent.NavigateToDetail) {
        Log.d(TAG, "📡 [原版直接导航] 已移除StateFlow导航机制")
        // 现在RepositoryAdapter直接处理导航，不再需要通过StateFlow传递
    }
    */
}
