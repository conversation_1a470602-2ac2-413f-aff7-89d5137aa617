package top.cywin.onetv.movie.quickjs.crawler;

import android.content.Context;
import android.util.Log;

import top.cywin.onetv.movie.quickjs.bean.Res;
import top.cywin.onetv.movie.quickjs.method.Console;
import top.cywin.onetv.movie.quickjs.method.Global;
import top.cywin.onetv.movie.quickjs.method.Local;
import top.cywin.onetv.movie.quickjs.utils.Async;
import top.cywin.onetv.movie.quickjs.utils.JSUtil;
import top.cywin.onetv.movie.quickjs.utils.Module;
import com.github.catvod.utils.Asset;
import com.github.catvod.utils.Json;
import com.github.catvod.utils.UriUtil;
import com.github.catvod.utils.Util;
import com.whl.quickjs.wrapper.JSArray;
import com.whl.quickjs.wrapper.JSObject;
import com.whl.quickjs.wrapper.QuickJSContext;

import org.json.JSONArray;

import java.io.ByteArrayInputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import dalvik.system.DexClassLoader;
import java9.util.concurrent.CompletableFuture;

public class Spider extends com.github.catvod.crawler.Spider { // 使用兼容的Spider基类

    private final ExecutorService executor;
    private final DexClassLoader dex;
    private QuickJSContext ctx;
    private JSObject jsObject;
    private final String key;
    private final String api;
    private boolean cat;

    public Spider(String key, String api, DexClassLoader dex) throws Exception {
        this.executor = Executors.newSingleThreadExecutor();
        this.key = key;
        this.api = api;
        this.dex = dex;
        initializeJS();
    }

    private void submit(Runnable runnable) {
        executor.submit(runnable);
    }

    private <T> Future<T> submit(Callable<T> callable) {
        return executor.submit(callable);
    }

    private Object call(String func, Object... args) throws Exception {
        android.util.Log.d("ONETV_SPIDER_CALL",
                "🎯 Spider调用函数: " + func + ", 参数: " + (args != null ? args.length : 0) + "个");

        try {
            Object result = CompletableFuture.supplyAsync(() -> Async.run(jsObject, func, args), executor).join().get();

            if (result != null) {
                String resultStr = result.toString();
                android.util.Log.d("ONETV_SPIDER_CALL", "✅ Spider函数调用成功: " + func + ", 返回长度: " + resultStr.length());
            } else {
                android.util.Log.w("ONETV_SPIDER_CALL", "⚠️ Spider函数返回null: " + func);
            }

            return result;
        } catch (Exception e) {
            android.util.Log.e("ONETV_SPIDER_CALL", "❌ Spider函数调用失败: " + func, e);
            throw e;
        }
    }

    @Override
    public void init(Context context, String extend) throws Exception {
        android.util.Log.d("ONETV_SPIDER_INIT",
                "🚀 Spider初始化开始, extend: " + (extend != null ? extend.length() + "字符" : "null"));

        if (cat) {
            Future<JSObject> configFuture = submit(() -> cfg(extend));
            JSObject config = configFuture.get();
            call("init", config);
        } else {
            Object configObj = Json.isObj(extend) ? ctx.parse(extend) : extend;
            call("init", configObj);
        }

        android.util.Log.d("ONETV_SPIDER_INIT", "✅ Spider初始化完成");
    }

    @Override
    public String homeContent(boolean filter) throws Exception {
        try {
            System.out.println("🔧 Spider.homeContent: 开始获取首页内容, filter=" + filter);
            String result = (String) call("home", filter);
            System.out.println("🔧 Spider.homeContent: 首页内容获取成功，长度: " +
                    (result != null ? result.length() : 0));
            return result;
        } catch (Exception e) {
            System.err.println("❌ Spider.homeContent: 获取首页内容失败 - " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    public String homeVideoContent() throws Exception {
        return (String) call("homeVod");
    }

    @Override
    public String categoryContent(String tid, String pg, boolean filter, HashMap<String, String> extend)
            throws Exception {
        Future<JSObject> objFuture = submit(() -> JSUtil.toObject(ctx, extend));
        JSObject obj = objFuture.get();
        return (String) call("category", tid, pg, filter, obj);
    }

    @Override
    public String detailContent(List<String> ids) throws Exception {
        return (String) call("detail", ids.get(0));
    }

    @Override
    public String searchContent(String key, boolean quick) throws Exception {
        return (String) call("search", key, quick);
    }

    @Override
    public String searchContent(String key, boolean quick, String pg) throws Exception {
        return (String) call("search", key, quick, pg);
    }

    @Override
    public String playerContent(String flag, String id, List<String> vipFlags) throws Exception {
        Future<JSArray> arrayFuture = submit(() -> JSUtil.toArray(ctx, vipFlags));
        JSArray array = arrayFuture.get();
        return (String) call("play", flag, id, array);
    }

    @Override
    public String liveContent(String url) throws Exception {
        return (String) call("live", url);
    }

    @Override
    public boolean manualVideoCheck() throws Exception {
        return (Boolean) call("sniffer");
    }

    @Override
    public boolean isVideoFormat(String url) throws Exception {
        return (Boolean) call("isVideo", url);
    }

    @Override
    public Object[] proxyLocal(Map<String, String> params) throws Exception {
        if ("catvod".equals(params.get("from")))
            return proxy2(params);
        else
            return submit(() -> proxy1(params)).get();
    }

    @Override
    public String action(String action) throws Exception {
        return (String) call("action", action);
    }

    @Override
    public void destroy() {
        try {
            call("destroy");
        } catch (Throwable e) {
            e.printStackTrace();
        }
        submit(() -> {
            executor.shutdownNow();
            jsObject.release();
            ctx.destroy();
        });
    }

    private void initializeJS() throws Exception {
        submit(() -> {
            createCtx();
            createFun();
            createObj();
            return null;
        }).get();
    }

    private void createCtx() {
        ctx = QuickJSContext.create();
        ctx.setConsole(new Console());

        // 🔧 修复：按照FongMi_TV原版实现，只加载http.js，不加载cat.js
        ctx.evaluate(Asset.read("js/lib/http.js"));

        // 🔧 修复：添加drpy2所需的HTML解析函数
        String htmlParserFunctions = "// drpy2框架所需的HTML解析函数\n" +
                "globalThis.pdfh = function(html, rule, add_url) {\n" +
                "    console.log('⚠️ pdfh函数被调用: rule=' + rule);\n" +
                "    return '';\n" +
                "};\n" +
                "globalThis.pdfa = function(html, rule) {\n" +
                "    console.log('⚠️ pdfa函数被调用: rule=' + rule);\n" +
                "    return [];\n" +
                "};\n" +
                "globalThis.pd = function(html, rule, add_url) {\n" +
                "    console.log('⚠️ pd函数被调用: rule=' + rule);\n" +
                "    return '';\n" +
                "};\n" +
                "globalThis.pq = function(html) {\n" +
                "    console.log('⚠️ pq函数被调用');\n" +
                "    return {};\n" +
                "};\n" +
                "console.log('✅ HTML解析函数已定义');\n";

        ctx.evaluate(htmlParserFunctions);

        ctx.getGlobalObject().setProperty("local", Local.class);
        ctx.setModuleLoader(new QuickJSContext.BytecodeModuleLoader() {
            @Override
            public String moduleNormalizeName(String baseModuleName, String moduleName) {
                return UriUtil.resolve(baseModuleName, moduleName);
            }

            @Override
            public byte[] getModuleBytecode(String moduleName) {
                return ctx.compileModule(Module.get().fetch(moduleName), moduleName);
            }
        });
    }

    private void createFun() {
        try {
            Global.create(ctx, executor);
            // 🔧 修复：移除对不存在的com.github.catvod.js.Function类的依赖
            // Global.create已经注入了所有必要的JavaScript函数，包括pdfh、pdfa、pd等
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private void createObj() {
        String spider = "__JS_SPIDER__";
        String global = "globalThis." + spider;

        // 🎯 与原版FongMi_TV保持一致：直接网络下载JavaScript内容
        android.util.Log.d("ONETV_JS_LOADER", "📥 开始下载JavaScript内容: " + api);
        String content = Module.get().fetch(api);
        android.util.Log.d("ONETV_JS_LOADER", "📊 JavaScript内容长度: " + content.length());

        // 🔧 检查下载内容是否为有效的JavaScript
        if (content.startsWith("<!DOCTYPE") || content.startsWith("<html")) {
            android.util.Log.e("ONETV_JS_LOADER", "❌ 网络下载失败，获取到HTML页面而非JavaScript文件");
            throw new RuntimeException("JavaScript Spider下载失败：获取到HTML页面而非JavaScript文件");
        }

        if (content.isEmpty()) {
            android.util.Log.e("ONETV_JS_LOADER", "❌ JavaScript内容为空");
            throw new RuntimeException("JavaScript Spider下载失败：内容为空");
        }

        android.util.Log.d("ONETV_JS_LOADER", "🎯 开始创建JavaScript Spider对象");
        cat = content.contains("__jsEvalReturn");
        ctx.evaluateModule(content.replace(spider, global), api);
        ctx.evaluateModule(String.format(Asset.read("js/lib/spider.js"), api));
        jsObject = (JSObject) ctx.getProperty(ctx.getGlobalObject(), spider);
        android.util.Log.d("ONETV_JS_LOADER", "✅ JavaScript Spider对象创建完成");
    }

    private JSObject cfg(String ext) {
        JSObject cfg = ctx.createNewJSObject();
        cfg.setProperty("stype", 3);
        cfg.setProperty("skey", key);
        if (!Json.isObj(ext))
            cfg.setProperty("ext", ext);
        else
            cfg.setProperty("ext", (JSObject) ctx.parse(ext));
        return cfg;
    }

    private Object[] proxy1(Map<String, String> params) throws Exception {
        JSObject object = JSUtil.toObject(ctx, params);
        JSONArray array = new JSONArray(((JSArray) jsObject.getJSFunction("proxy").call(object)).stringify());
        Map<String, String> headers = array.length() > 3 ? Json.toMap(array.optString(3)) : null;
        boolean base64 = array.length() > 4 && array.optInt(4) == 1;
        Object[] result = new Object[4];
        result[0] = array.optInt(0);
        result[1] = array.optString(1);
        result[2] = getStream(array.opt(2), base64);
        result[3] = headers;
        return result;
    }

    private Object[] proxy2(Map<String, String> params) throws Exception {
        String url = params.get("url");
        String header = params.get("header");
        JSArray array = submit(() -> JSUtil.toArray(ctx, Arrays.asList(url.split("/")))).get();
        Object object = submit(() -> ctx.parse(header)).get();
        String json = (String) call("proxy", array, object);
        Res res = Res.objectFrom(json);
        Object[] result = new Object[3];
        result[0] = res.getCode();
        result[1] = res.getContentType();
        result[2] = res.getStream();
        return result;
    }

    private ByteArrayInputStream getStream(Object o, boolean base64) {
        if (o instanceof byte[]) {
            return new ByteArrayInputStream((byte[]) o);
        } else {
            String content = o.toString();
            if (base64 && content.contains("base64,"))
                content = content.split("base64,")[1];
            return new ByteArrayInputStream(base64 ? Util.decode(content) : content.getBytes());
        }
    }
}
